#\u7EBF\u7A0B\u6C60\u5927\u5C0F
threadpool.count=20
#kafkaConsumer kafka\u6D88\u8D39\u8005\u914D\u7F6E\u6587\u4EF6\u8DEF\u5F84
#kafka.consumer.config.path=G:\\whjcDataRecv\\config\\kafkaConsumer.properties
kafka.consumer.config.path=/server/dtsServer/xjjcDataRecv/config/kafkaConsumer.properties
#kafkaProducer kafka\u6D88\u8D39\u8005\u914D\u7F6E\u6587\u4EF6\u8DEF\u5F84
#kafka.producer.config.path=G:\\whjcDataRecv\\config\\kafkaProducer.properties
kafka.producer.config.path=/server/dtsServer/xjjcDataRecv/config/kafkaProducer.properties
#\u8BB0\u5F55\u6570\u636E\u4F20\u8F93\u65E5\u5FD7\u7684topic
kafka.msg.transfer.log.topic=msgTranLog
#\u5E94\u7528\u5173\u95ED\u76D1\u63A7\u7AEF\u53E3
ytsodb.stop.port=20028
#\u6D77\u5EB7\u670D\u52A1ip:port
rest.hk.host=*********:443
#\u6D77\u5EB7\u670D\u52A1appkey
rest.hk.appKey=25102920
#\u6D77\u5EB7\u670D\u52A1appsecret
rest.hk.appSecret=2QILUJfcjIHqhvdBlFJ7
#\u6D77\u5EB7\u624B\u52A8\u62A5\u8B66\u8D44\u6E90\u4FE1\u606Fcode
hk.handEquInfo.service.code=hkhandEquInfo
#\u6D77\u5EB7\u83B7\u53D6\u624B\u52A8\u62A5\u8B66\u8D44\u6E90\u4FE1\u606Furl
hk.handEquInfo.url=/api/irds/v1/deviceResource/resources
#\u6D77\u5EB7\u624B\u52A8\u62A5\u8B66\u8D44\u6E90\u53D8\u66F4\u4FE1\u606Fcode
hk.handEquState.service.code=hkhandEquState
#\u6D77\u5EB7\u83B7\u53D6\u624B\u52A8\u62A5\u8B66\u8D44\u6E90\u53D8\u66F4\u4FE1\u606Furl
hk.handEquState.url=/api/scpms/v1/defence/status
#\u6D77\u5EB7\u83B7\u53D6\u6309\u4E8B\u4EF6\u7C7B\u578B\u8BA2\u9605\u4E8B\u4EF6\u4FE1\u606Furl
hk.eventSubscription.url=/api/eventService/v1/eventSubscriptionByEventTypes
#\u6D77\u5EB7\u5165\u4FB5\u62A5\u8B66\u4E8B\u4EF6\uFF08\u624B\u52A8\u62A5\u8B66\u62A5\u8B66\uFF09code
hk.handAlarmMessage.service.code=handAlarmMessage
#\u95E8\u7981\u62A5\u8B66\u8D44\u6E90code
hk.mjEquInfo.service.code=hkmjEquInfo
#\u95E8\u7981\u62A5\u8B66\u8D44\u6E90url
hk.mjEquInfo.url=/api/irds/v1/deviceResource/resources
#\u95E8\u7981\u62A5\u8B66\u53D8\u66F4\u8D44\u6E90code
hk.mjEquState.service.code=hkmjEquState
#\u95E8\u7981\u62A5\u8B66\u53D8\u66F4\u8D44\u6E90url
hk.mjEquState.url=/api/acs/v1/door/states
#\u83B7\u53D6\u5355\u4E2A\u5361\u7247\u4FE1\u606F
hk.cardNoInfo.url=/api/irds/v1/card/cardInfo
#\u95E8\u7981\u62A5\u8B66code
hk.mjAlarmMessage.service.code=mjAlarmMessage

#\u9053\u53E3\u8FC7\u8F66code
crossingCarPass.service.code=crossingCarPass
#sodb\u624B\u52A8\u62A5\u8B66\u8D44\u6E90\u72B6\u6001\u8BF7\u6C42\u63A5\u53E3
sodb.ec03.equ.status.req.url=http://**********/api/bs/equ/info/open/getEquInfoByCode/EC03
#\u95E8\u7981\u62A5\u8B66\u8D44\u6E90
sodb.ec02.equ.status.req.url=http://**********/api/bs/equ/info/open/getEquInfoByCode/EC02
#sodb\u89C6\u9891\u8D44\u6E90\u72B6\u6001\u8BF7\u6C42\u63A5\u53E3
sodb.video.equ.status.req.url=http://**********/api/bs/equ/info/open/getEquInfoByCode/EC01
#\u9053\u53E3\u8D44\u6E90
sodb.cross.equ.status.req.url=http://**********/api/bs/equ/info/open/getEquInfoByCode/EC05

#\u65C5\u68C0\u6570\u636E\u63A5\u5165\u4E3B\u9898
psgr.service=true
kafka.psgr.msg.input.topics=relsTopic1
#\u8D27\u68C0\u53D1\u9001\u63A5\u53E3
cargo.service=true
kafka.cargo.msg.output.topic=relsTopic2
#\u822A\u73ED\u6570\u636E\u63A5\u5165\u4E3B\u9898
flight.service=true
kafka.flight.msg.input.topics=relsTopic3
#minio\u53C2\u6570
endpoint=http://10.9.25.43:9000
access_key=miniominio
secret_key=hayc@123
bucket=datarecv

#\u673A\u573A\u4E09\u5B57\u7801
airport.iata=HQL

username=txsjdj
pwd =admin123
login_url=http://10.9.28.1:8080/sp/login
white_url=http://10.9.28.1:8080/sp/carManager/getMonthRentVehicleInfoPage
black_url=http://10.9.28.1:8080/sp/carManager/getStorgeVehicleInfoPage

