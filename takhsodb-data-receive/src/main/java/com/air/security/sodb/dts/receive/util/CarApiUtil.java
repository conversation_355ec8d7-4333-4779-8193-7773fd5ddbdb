package com.air.security.sodb.dts.receive.util;

import com.air.security.sodb.data.core.util.PropertyUtil;
import com.air.security.sodb.data.core.util.UuidUtil;
import com.air.security.sodb.dts.srvc.domain.entity.BlackWhiteEntity;
import com.air.security.sodb.dts.srvc.domain.entity.JsonRequestBean;
import com.air.security.sodb.dts.srvc.domain.entity.JsonResultBean;
import com.air.security.sodb.dts.srvc.domain.entity.UserEntity;
import com.alibaba.fastjson.JSONObject;
import org.slf4j.Logger;
import org.slf4j.LoggerFactory;
import org.springframework.http.ResponseEntity;

import java.io.UnsupportedEncodingException;

public class CarApiUtil {

    private static final Logger log = LoggerFactory.getLogger(CarApiUtil.class);

    private static final String username = PropertyUtil.getProperty("username");
    private static final String pwd = PropertyUtil.getProperty("pwd");
    private static final String login_url = PropertyUtil.getProperty("login_url");
    private static final String black_url = PropertyUtil.getProperty("black_url");
    private static final String white_url = PropertyUtil.getProperty("white_url");


    /**
     * 登陆接口
     *
     * @return
     */
    public static String login() {

        UserEntity user = new UserEntity();
        user.setUserName(username);
        user.setPassWord(Md5Util.MD5EncodeInUppercase(pwd, "utf-8"));
        JsonRequestBean bean = new JsonRequestBean();
        bean.setInfo(user);
        bean.setSessionId(UuidUtil.getUuid32());
        bean.setMethod("/sp/login");
        ResponseEntity<String> post = RestTemplateUtils.post(login_url, bean, String.class);
        String body = post.getBody();
        JSONObject jsonObject = JSONObject.parseObject(body);
        Boolean result = jsonObject.getBoolean("result");
        if (!result) {
            log.error("鉴权失败" + result);
        }
        JSONObject json = (JSONObject) jsonObject.getJSONObject("info").getJSONArray("data").get(0);
        String accessToken = json.getString("accessToken");
        return accessToken;
    }

    /**
     * 黑名单车辆
     *
     * @return
     */
    public static JsonResultBean blackList(BlackWhiteEntity entity) {
        String sessionId = login();
        JsonRequestBean bean = new JsonRequestBean();
        bean.setInfo(entity);
        bean.setSessionId(sessionId);
        bean.setMethod("/sp/carManager/getStorgeVehicleInfoPage");
        ResponseEntity<String> post = RestTemplateUtils.post(black_url, bean, String.class);

        String body = post.getBody();
        try {
            body =new String(body.getBytes("ISO-8859-1"), "UTF-8");
        } catch (UnsupportedEncodingException e) {
            e.printStackTrace();
        }
        JSONObject jsonObject = JSONObject.parseObject(body);
        JsonResultBean responseBDto = JSONObject.toJavaObject(jsonObject, JsonResultBean.class);

        return responseBDto;
    }

    /**
     * 登陆接口
     *
     * @return
     */
    public static JsonResultBean whiteList(BlackWhiteEntity entity) {
        String sessionId = login();
        JsonRequestBean bean = new JsonRequestBean();
        bean.setInfo(entity);
        bean.setSessionId(sessionId);
        bean.setMethod("/sp/carManager/getMonthRentVehicleInfoPage");
        ResponseEntity<String> post = RestTemplateUtils.post(white_url, bean, String.class);

        String body = post.getBody();
        try {
            body =new String(body.getBytes("ISO-8859-1"), "UTF-8");
        } catch (UnsupportedEncodingException e) {
            e.printStackTrace();
        }
        JSONObject jsonObject1 = JSONObject.parseObject(body);
        JsonResultBean responseBDto = JSONObject.toJavaObject(jsonObject1, JsonResultBean.class);
        return  responseBDto;
    }

}
