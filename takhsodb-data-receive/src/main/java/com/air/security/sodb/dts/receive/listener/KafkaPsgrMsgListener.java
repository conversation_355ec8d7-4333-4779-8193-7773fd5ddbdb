package com.air.security.sodb.dts.receive.listener;

import com.air.security.sodb.data.core.base.Meta;
import com.air.security.sodb.data.core.constant.MsgIdConstant;
import com.air.security.sodb.data.core.util.*;
import com.air.security.sodb.dts.receive.server.MqMessageRecvServer;
import com.air.security.sodb.dts.receive.server.MqMessageRecvServerImpl;
import com.air.security.sodb.dts.receive.util.KafkaConscumerUtils;
import com.alibaba.fastjson.JSONArray;
import com.alibaba.fastjson.JSONObject;
import org.apache.commons.lang.StringUtils;
import org.apache.kafka.clients.consumer.ConsumerRecord;
import org.apache.kafka.clients.consumer.ConsumerRecords;
import org.apache.kafka.clients.consumer.KafkaConsumer;
import org.slf4j.Logger;
import org.slf4j.LoggerFactory;

import java.time.Duration;

/**
 * @Description: kafka事件消息监听
 * @author: zhangc
 * @Date: 2018年12月18日
 */
public class KafkaPsgrMsgListener implements Runnable {

    private static final Logger log = LoggerFactory.getLogger(KafkaPsgrMsgListener.class);

    /**
     * 消息分发处理器
     */
    private MqMessageRecvServer server = new MqMessageRecvServerImpl();

    /**
     * 报警消息发布服务主题
     */
    private static String topic = PropertyUtil.getProperty("kafka.psgr.msg.input.topics");

    @Override
    public void run() {

        HaLog.info(log, MsgIdConstant.MS_INF_0001, "kafka事件安检消息监听");

        KafkaConsumer<String, String> consumer = KafkaConscumerUtils.getConsumer(new String[]{topic});
        while (true) {
            ThreadUtil.sleepThreadUnit();

            // 消费消息
            ConsumerRecords<String, String> records = consumer.poll(Duration.ZERO);
            for (ConsumerRecord<String, String> record : records) {
                String message = record.value();

                if (StringUtils.isBlank(message)) {
                    continue;
                }

                // 接收消息日志
                HaLog.debug(log, MsgIdConstant.MS_INF_0003, "旅客安检数据", message);
                try {
                    JSONObject msgJson = JSONObject.parseObject(message);

                    String msgtype = String.valueOf(msgJson.get("msgtype"));

                    if ("bag".equals(msgtype)) {
                        JSONArray carryBagImgList = msgJson.getJSONArray("carryBagImgList");
                        String transBagImg = msgJson.getString("transBagImg");
                        msgJson.remove("carryBagImgList");
                        msgJson.remove("transBagImg");
                        if (null != carryBagImgList && !carryBagImgList.isEmpty()) {
                            for (Object object : carryBagImgList) {
                                Meta meta = new Meta();
                                String carryBagImg = (String) object;
                                msgJson.put("img", carryBagImg);
                                msgJson.put("bagType", "1");
                                meta.setEventType(msgtype);
                                server.handle(meta, msgJson.toJSONString());
                            }
                        }

                        if (StringUtils.isNotBlank(transBagImg)) {
                            Meta meta = new Meta();
                            msgJson.put("img", transBagImg);
                            msgJson.put("bagType", "2");
                            meta.setEventType(msgtype);
                            server.handle(meta, msgJson.toJSONString());
                        }
                    } else {
                        Meta meta = new Meta();
                        if (null != msgJson.getString("fltDate") || StringUtils.isNotBlank(msgJson.getString("fltDate"))) {
                            String fltDateStr = DateTimeUtil.parseDateStr(msgJson.getString("fltDate"), "yyyy-MM-dd HH:mm:ss", "yyyy-MM-dd");
                            msgJson.put("dpsrCheckinTime", fltDateStr + " " + msgJson.getString("dpsrCheckinTime"));
                        } else {
                            String fltDateStr = DateTimeUtil.parseDateStr(msgJson.getString("dpsrCheckTime"), "yyyy-MM-dd HH:mm:ss", "yyyy-MM-dd");
                            msgJson.put("dpsrCheckinTime", fltDateStr + " " + msgJson.getString("dpsrCheckinTime"));
                        }
                        meta.setEventType(msgtype);
                        server.handle(meta, msgJson.toJSONString());
                    }
                } catch (Exception e) {
                    HaLog.error(log, e, MsgIdConstant.MS_ERR_0001, "消息处理异常");
                }
            }
        }
    }
}
