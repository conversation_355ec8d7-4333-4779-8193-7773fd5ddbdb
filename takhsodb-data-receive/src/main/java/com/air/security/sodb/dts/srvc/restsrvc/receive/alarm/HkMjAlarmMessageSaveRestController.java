package com.air.security.sodb.dts.srvc.restsrvc.receive.alarm;

import com.air.security.sodb.data.core.base.Meta;
import com.air.security.sodb.data.core.constant.MsgIdConstant;
import com.air.security.sodb.data.core.util.HaLog;
import com.air.security.sodb.data.core.util.PropertyUtil;
import com.air.security.sodb.data.core.util.RequestUtil;
import com.air.security.sodb.data.core.util.UuidUtil;
import com.air.security.sodb.dts.receive.server.MqMessageRecvServer;
import com.air.security.sodb.dts.receive.server.MqMessageRecvServerImpl;
import com.air.security.sodb.dts.receive.util.CertificationInfoUtil;
import com.alibaba.fastjson.JSONArray;
import com.alibaba.fastjson.JSONObject;
import org.apache.commons.lang3.StringUtils;
import org.slf4j.Logger;
import org.slf4j.LoggerFactory;
import org.springframework.web.bind.annotation.CrossOrigin;
import org.springframework.web.bind.annotation.RequestMapping;
import org.springframework.web.bind.annotation.RequestMethod;
import org.springframework.web.bind.annotation.RestController;

import javax.servlet.http.HttpServletRequest;
import javax.servlet.http.HttpServletResponse;

/**
 * @Description: 海康门禁报警事件（门禁报警报警）
 * @author: mk
 * @Date: 2020-11-11
 */
@CrossOrigin
@RestController
@RequestMapping("/api/release/mjAlarmMessage")
public class HkMjAlarmMessageSaveRestController {

    private static final Logger log = LoggerFactory.getLogger(HkMjAlarmMessageSaveRestController.class);

    private static String mjAlarmMessage = PropertyUtil.getProperty("hk.mjAlarmMessage.service.code");

    private static String url = PropertyUtil.getProperty("hk.cardNoInfo.url");

    /**
     *
     */
    private MqMessageRecvServer server = new MqMessageRecvServerImpl();

    /**
     * 接口
     */
    @RequestMapping(value = "/execute", method = RequestMethod.POST)
    public void execute(HttpServletRequest request, HttpServletResponse response) {
        JSONObject jsonObj = RequestUtil.requestGetJson(request);
        HaLog.info(log, MsgIdConstant.MS_INF_0001, "门禁报警报警接入接口");
        try {
            // 执行业务处理
            if (null != jsonObj) {
                // 执行业务处理
                this.execute(jsonObj);
            }
            HaLog.info(log, MsgIdConstant.MS_INF_0002, "门禁报警报警接入接口");
        } catch (Exception e) {
            HaLog.error(log, e, MsgIdConstant.MS_ERR_0001);
        }

    }

    /**
     * 执行业务处理
     *
     * @param jsonObj
     */
    private void execute(JSONObject jsonObj) {
        JSONArray events = jsonObj.getJSONObject("params").getJSONArray("events");
        for (Object o : events) {
            JSONObject jsonObject = (JSONObject) o;
            JSONObject data = jsonObject.getJSONObject("data");
            if (data == null) {
                continue;
            }
            String extEventCardNo = data.getString("ExtEventCardNo");
            if (StringUtils.isNotBlank(extEventCardNo)) {
                JSONObject json = new JSONObject();
                json.put("cardNo", extEventCardNo);
                String messageBody = CertificationInfoUtil.send(url, json.toJSONString());
                JSONObject result = JSONObject.parseObject(messageBody);
                JSONObject jsonResult = result.getJSONObject("data");
                if (jsonResult == null) {
                    continue;
                }
                String personName = jsonResult.getString("personName");
                jsonObject.put("Name", personName);
            }
        }
        Meta meta = new Meta();
        meta.setEventType(mjAlarmMessage);
        meta.setRecvSequence(UuidUtil.getUuid32());
        server.handle(meta, events.toString());
    }

}
