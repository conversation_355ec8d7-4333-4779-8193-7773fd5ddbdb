package com.air.security.sodb.dts.srvc.domain.entity;

public class CarInfoEntity {

    private String beginTime;
    private String endTime;
    private String parkID;
    private String personAddress;
    private String personName;
    private String phoneNum;
    private int placeType;
    private String plateNo;

    public String getBeginTime() {
        return beginTime;
    }

    public void setBeginTime(String beginTime) {
        this.beginTime = beginTime;
    }

    public String getEndTime() {
        return endTime;
    }

    public void setEndTime(String endTime) {
        this.endTime = endTime;
    }

    public String getParkID() {
        return parkID;
    }

    public void setParkID(String parkID) {
        this.parkID = parkID;
    }

    public String getPersonAddress() {
        return personAddress;
    }

    public void setPersonAddress(String personAddress) {
        this.personAddress = personAddress;
    }

    public String getPersonName() {
        return personName;
    }

    public void setPersonName(String personName) {
        this.personName = personName;
    }

    public String getPhoneNum() {
        return phoneNum;
    }

    public void setPhoneNum(String phoneNum) {
        this.phoneNum = phoneNum;
    }

    public int getPlaceType() {
        return placeType;
    }

    public void setPlaceType(int placeType) {
        this.placeType = placeType;
    }

    public String getPlateNo() {
        return plateNo;
    }

    public void setPlateNo(String plateNo) {
        this.plateNo = plateNo;
    }

    @Override
    public String toString() {
        return "CarInfoEntity{" +
                "beginTime='" + beginTime + '\'' +
                ", endTime='" + endTime + '\'' +
                ", parkID='" + parkID + '\'' +
                ", personAddress='" + personAddress + '\'' +
                ", personName='" + personName + '\'' +
                ", phoneNum='" + phoneNum + '\'' +
                ", placeType=" + placeType +
                ", plateNo='" + plateNo + '\'' +
                '}';
    }
}
