package com.air.security.sodb.dts.receive.util;

import com.air.security.sodb.data.core.util.HttpRequestUtil;
import com.air.security.sodb.data.core.util.PropertyUtil;
import com.alibaba.fastjson.JSONArray;
import com.alibaba.fastjson.JSONObject;

import java.util.List;
/**
 * 同步获取设备状态实现类
 * <AUTHOR>
public class HkEquStatusCache {

    private static List<HkEquStatus> acsCacheList;

    private static List<HkEquStatus> mjCacheList;

    private static String equStatusRequUrl = PropertyUtil.getProperty("sodb.ec03.equ.status.req.url");

    private static String mjequStatusRequUrl = PropertyUtil.getProperty("sodb.ec02.equ.status.req.url");

    public static void init() {
        if (acsCacheList == null) {
            String result = HttpRequestUtil.sendPost(equStatusRequUrl, "");
            JSONObject resJson = JSONObject.parseObject(result);
            JSONArray cacheArray = resJson.getJSONArray("dataList");
            acsCacheList = JSONArray.parseArray(cacheArray.toJSONString(), HkEquStatus.class);
        }
    }

    public static List<HkEquStatus> getCacheList() {
        if (acsCacheList == null) {
            String result = HttpRequestUtil.sendPost(equStatusRequUrl, "");
            JSONObject resJson = JSONObject.parseObject(result);
            JSONArray cacheArray = resJson.getJSONArray("datalist");
            acsCacheList = JSONArray.parseArray(cacheArray.toJSONString(), HkEquStatus.class);
        }
        return acsCacheList;
    }

    public static List<HkEquStatus> getmjCacheList() {
        if (mjCacheList == null) {
            String result = HttpRequestUtil.sendPost(mjequStatusRequUrl, "");
            JSONObject resJson = JSONObject.parseObject(result);
            JSONArray cacheArray = resJson.getJSONArray("datalist");
            mjCacheList = JSONArray.parseArray(cacheArray.toJSONString(), HkEquStatus.class);
        }
        return mjCacheList;
    }

}
