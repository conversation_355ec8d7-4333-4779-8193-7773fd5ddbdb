package com.air.security.sodb.dts.srvc.restsrvc.receive.car;

import com.air.security.sodb.data.core.base.Meta;
import com.air.security.sodb.data.core.constant.MsgIdConstant;
import com.air.security.sodb.data.core.util.HaLog;
import com.air.security.sodb.data.core.util.RequestUtil;
import com.air.security.sodb.data.core.util.UuidUtil;
import com.air.security.sodb.dts.receive.server.MqMessageRecvServer;
import com.air.security.sodb.dts.receive.server.MqMessageRecvServerImpl;
import com.alibaba.fastjson.JSONObject;
import org.slf4j.Logger;
import org.slf4j.LoggerFactory;
import org.springframework.web.bind.annotation.CrossOrigin;
import org.springframework.web.bind.annotation.RequestMapping;
import org.springframework.web.bind.annotation.RequestMethod;
import org.springframework.web.bind.annotation.RestController;

import javax.servlet.http.HttpServletRequest;
import javax.servlet.http.HttpServletResponse;

@CrossOrigin
@RestController
@RequestMapping("/api/release/carOutfo")
public class CarOutfoMessageSaveRestController {

    private static final Logger log = LoggerFactory.getLogger(CarOutfoMessageSaveRestController.class);

    /**
     *
     */
    private MqMessageRecvServer server = new MqMessageRecvServerImpl();

    /**
     * 接口
     */
    @RequestMapping(value = "/execute", method = RequestMethod.POST)
    public void execute(HttpServletRequest request, HttpServletResponse response) {
        JSONObject jsonObj = RequestUtil.requestGetJson(request);
        HaLog.info(log, MsgIdConstant.MS_INF_0001, "出场数据接入接口");
        try {
            // 执行业务处理
            if (null != jsonObj) {
                Meta meta = new Meta();
                meta.setEventType("CAR_OUTFO");
                meta.setSequence(UuidUtil.getUuid32());
                server.handle(meta, jsonObj.toString());
            }
            HaLog.info(log, MsgIdConstant.MS_INF_0002, "出场数据接入接口");
        } catch (Exception e) {
            HaLog.error(log, e, MsgIdConstant.MS_ERR_0001);
        }

    }
}
