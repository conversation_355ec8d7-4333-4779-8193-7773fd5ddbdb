package com.air.security.sodb.dts.receive.job;

import com.air.security.sodb.data.core.base.Meta;
import com.air.security.sodb.data.core.constant.MsgIdConstant;
import com.air.security.sodb.data.core.exception.SystemBaseException;
import com.air.security.sodb.data.core.quartz.AbstractJob;
import com.air.security.sodb.data.core.util.HaLog;
import com.air.security.sodb.data.core.util.UuidUtil;
import com.air.security.sodb.dts.receive.server.MqMessageRecvServer;
import com.air.security.sodb.dts.receive.server.MqMessageRecvServerImpl;
import com.air.security.sodb.dts.receive.util.CarApiUtil;
import com.air.security.sodb.dts.receive.util.SrvcHelper;
import com.air.security.sodb.dts.srvc.domain.entity.*;
import com.alibaba.fastjson.JSON;
import com.alibaba.fastjson.JSONArray;
import com.alibaba.fastjson.JSONObject;
import org.apache.commons.collections.CollectionUtils;
import org.quartz.JobDataMap;
import org.slf4j.Logger;
import org.slf4j.LoggerFactory;

import java.util.ArrayList;
import java.util.List;

/**
 * 黑白名单停车场job
 */
public class CarBlackWhiteJob extends AbstractJob {

    private static final Logger log = LoggerFactory.getLogger(CarBlackWhiteJob.class);

    private static List<CarInfoEntity> whiteCarInfos = null;

    private static List<CarInfoEntity> blackCarInfos = null;

    /**
     *
     */
    private MqMessageRecvServer server = new MqMessageRecvServerImpl();

    /**
     * 数据接入事件类型-停车数据
     */
    public static final String EVENT_TYPE_CAR_BLACK_INFO = "CAR_BLACK_INFO";

    /**
     * 数据接入事件类型-停车数据
     */
    public static final String EVENT_TYPE_CAR_WHITE_INFO = "CAR_WHITE_INFO";


    @Override
    public void executeJob(JobDataMap paramMap) {

        HaLog.info(log, MsgIdConstant.MS_INF_0001, "获取黑白名单信息定时任务");

        try {
            whiteCarInfos = new ArrayList<>();
            execWhitePost(SrvcHelper.PAGE_SIZE, 0);
            blackCarInfos = new ArrayList<>();
            execBlackPost(SrvcHelper.PAGE_SIZE, 0);
            JSONArray whiteArray = JSONArray.parseArray(JSON.toJSONString(whiteCarInfos));
            if (null != whiteArray && whiteArray.size() > 0) {
                for (Object o : whiteArray) {
                    JSONObject jsonObject = (JSONObject) o;
                    Meta meta = new Meta();
                    meta.setEventType(EVENT_TYPE_CAR_WHITE_INFO);
                    meta.setSequence(UuidUtil.getUuid32());
                    server.handle(meta, jsonObject.toString());
                }
            }

            JSONArray blackArray = JSONArray.parseArray(JSON.toJSONString(blackCarInfos));
            if (null != blackArray && blackArray.size() > 0) {
                for (Object o : blackArray) {
                    JSONObject jsonObject = (JSONObject) o;
                    Meta meta = new Meta();
                    meta.setEventType(EVENT_TYPE_CAR_BLACK_INFO);
                    meta.setSequence(UuidUtil.getUuid32());
                    server.handle(meta, jsonObject.toString());
                }
            }
        } catch (Exception e) {
            throw new SystemBaseException("获取黑白名单信息定时任务失败", e);
        } finally {
            HaLog.info(log, MsgIdConstant.MS_INF_0002, "获取黑白名单信息定时任务");
        }
    }

    /**
     * 获取白名单
     *
     * @param pageSize
     * @param pageNo
     */
    private void execWhitePost(int pageSize, int pageNo) throws Exception {
        BlackWhiteEntity entity = new BlackWhiteEntity();
        entity.setCarType(1);
        entity.setPageNo(pageNo);
        entity.setPageSize(pageSize);
        JsonResultBean jsonResultBean = CarApiUtil.whiteList(entity);
        InfoBean info = jsonResultBean.getInfo();
        boolean success = info.isSuccess();
        if (!success) {
            return;
        }
        List<DataBean> data = info.getData();
        DataBean dataBean = data.get(0);
        List<CarInfoEntity> carInfo = dataBean.getCarInfos();
        if (CollectionUtils.isNotEmpty(carInfo)) {
            whiteCarInfos.addAll(carInfo);
            pageNo++;
            execWhitePost(SrvcHelper.PAGE_SIZE, pageNo);
        }
    }

    /**
     * 获取黑名单
     *
     * @param pageSize
     * @param pageNo
     */
    private void execBlackPost(int pageSize, int pageNo) throws Exception {
        BlackWhiteEntity entity = new BlackWhiteEntity();
        entity.setCarType(2);
        entity.setPageNo(pageNo);
        entity.setPageSize(pageSize);
        JsonResultBean jsonResultBean = CarApiUtil.blackList(entity);
        InfoBean info = jsonResultBean.getInfo();
        boolean success = info.isSuccess();
        if (!success) {
            return;
        }
        List<DataBean> data = info.getData();

        DataBean dataBean = data.get(0);
        List<CarInfoEntity> carInfo = dataBean.getCarInfos();
        if (CollectionUtils.isNotEmpty(carInfo)) {
            blackCarInfos.addAll(carInfo);
            pageNo++;
            execWhitePost(SrvcHelper.PAGE_SIZE, pageNo);
        }
    }

}
