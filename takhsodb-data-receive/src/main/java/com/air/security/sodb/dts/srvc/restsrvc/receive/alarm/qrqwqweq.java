package com.air.security.sodb.dts.srvc.restsrvc.receive.alarm;

import com.alibaba.fastjson.JSON;
import com.alibaba.fastjson.JSONArray;
import com.alibaba.fastjson.JSONObject;

import java.util.HashMap;
import java.util.Map;

public class qrqwqweq {

    public static void main(String[] args) {
        JSONArray eventList = new JSONArray();
        String asd = "开门超时,门被外力开启,正常开门,正常关门,按钮开门,刷卡+指纹认证通过,刷卡+指纹+密码认证通过,指纹+密码认证通过,指纹比对通过,合法卡比对通过,刷卡+密码认证通过,人脸+指纹认证通过,人脸+密码认证通过,人脸+刷卡认证通过,人脸+密码+指纹认证通过,人脸+刷卡+指纹认证通过,人脸认证通过,胁迫卡比对通过,刷卡+密码认证失败,指纹比对失败,刷卡+指纹认证失败,刷卡+指纹+密码认证失败,刷卡+指纹+密码认证超时,指纹+密码认证失败,指纹不存在,卡号过期,无此卡号,密码不匹配,人脸+指纹认证失败,人脸+指纹认证超时,人脸+密码认证失败,人脸+密码认证超时,人脸+刷卡认证失败,人脸+刷卡认证超时,人脸+密码+指纹认证失败,人脸+密码+指纹认证超时,人脸+刷卡+指纹认证失败,人脸+刷卡+指纹认证超时,人脸认证失败,人脸识别失败,胁迫报警";
        Map<String,String> map = new HashMap<>();
        String[] split = asd.split(",");
        for (String s : split) {
            map.put(s,"1");
        }
        String sb = "{\"code\":\"0\",\"msg\":\"acs.acs.eventtype.search.success\",\"data\":{\"0\":[{\"type\":0,\"eventList\":[{\"eventTypeId\":\"0bd1e751-c6f7-4a6f-837a-218600a436d5\",\"eventType\":0,\"code\":196902,\"eventTypeName\":\"M1卡识别未启用\",\"commonUse\":0,\"isUse\":\"10\",\"remark\":null,\"isUsing\":1},{\"eventTypeId\":\"7cffbe4d-f7b1-4605-bd24-ab35a5d1a9fd\",\"eventType\":0,\"code\":196903,\"eventTypeName\":\"CPU卡识别未启用\",\"commonUse\":0,\"isUse\":\"10\",\"remark\":null,\"isUsing\":1},{\"eventTypeId\":\"b16ca720-ab76-4cb5-bf4d-036bebb84384\",\"eventType\":0,\"code\":199708,\"eventTypeName\":\"设备防拆报警\",\"commonUse\":0,\"isUse\":\"10\",\"remark\":null,\"isUsing\":1},{\"eventTypeId\":\"93a9a509-f18d-45aa-a967-f01475593271\",\"eventType\":0,\"code\":199709,\"eventTypeName\":\"设备防拆恢复\",\"commonUse\":0,\"isUse\":\"10\",\"remark\":null,\"isUsing\":1},{\"eventTypeId\":\"a15d7955-c436-43cd-878c-59088e7d3c87\",\"eventType\":0,\"code\":199710,\"eventTypeName\":\"装置离线\",\"commonUse\":0,\"isUse\":\"10\",\"remark\":null,\"isUsing\":1},{\"eventTypeId\":\"8be075d5-230e-4d45-be07-ec4526bcc3a9\",\"eventType\":0,\"code\":199711,\"eventTypeName\":\"装置连线\",\"commonUse\":0,\"isUse\":\"10\",\"remark\":null,\"isUsing\":1},{\"eventTypeId\":\"e34a0364-5c6a-49ca-b06b-f3c68d5d86dd\",\"eventType\":0,\"code\":199712,\"eventTypeName\":\"重新启动\",\"commonUse\":0,\"isUse\":\"10\",\"remark\":null,\"isUsing\":1},{\"eventTypeId\":\"164b8bf9-d413-4a74-8813-9157acc347b2\",\"eventType\":0,\"code\":199713,\"eventTypeName\":\"系统初始化\",\"commonUse\":0,\"isUse\":\"10\",\"remark\":null,\"isUsing\":1},{\"eventTypeId\":\"e0ea18e2-ea52-4795-9192-6b17159f35f6\",\"eventType\":0,\"code\":199714,\"eventTypeName\":\"数据被损坏\",\"commonUse\":0,\"isUse\":\"10\",\"remark\":null,\"isUsing\":1},{\"eventTypeId\":\"6bc325bd-cc2e-4ebd-b7ae-4b7aa37d08ca\",\"eventType\":0,\"code\":199715,\"eventTypeName\":\"呼叫中心\",\"commonUse\":0,\"isUse\":\"10\",\"remark\":null,\"isUsing\":1},{\"eventTypeId\":\"b1c4c5a7-97ea-4c21-aa56-f7e74bdbd22b\",\"eventType\":0,\"code\":199770,\"eventTypeName\":\"与反潜回服务器通信断开\",\"commonUse\":0,\"isUse\":\"10\",\"remark\":null,\"isUsing\":1},{\"eventTypeId\":\"e23d1d79-6145-4f1b-837a-69a2c9698cc5\",\"eventType\":0,\"code\":199771,\"eventTypeName\":\"与反潜回服务器通信恢复\",\"commonUse\":0,\"isUse\":\"10\",\"remark\":null,\"isUsing\":1},{\"eventTypeId\":\"2d466de2-cf48-4ee9-b77e-5cd581a68ffb\",\"eventType\":0,\"code\":199818,\"eventTypeName\":\"M1卡加密验证功能开启\",\"commonUse\":0,\"isUse\":\"10\",\"remark\":null,\"isUsing\":1},{\"eventTypeId\":\"e63ec94a-41c6-4dc3-9632-92b9993b6fca\",\"eventType\":0,\"code\":199819,\"eventTypeName\":\"M1卡加密验证功能关闭\",\"commonUse\":0,\"isUse\":\"10\",\"remark\":null,\"isUsing\":1},{\"eventTypeId\":\"d01b38a9-d895-482c-afd4-41d938f9a43e\",\"eventType\":0,\"code\":200501,\"eventTypeName\":\"摆臂被阻挡\",\"commonUse\":0,\"isUse\":\"10\",\"remark\":null,\"isUsing\":1},{\"eventTypeId\":\"8745bd93-0adc-486c-923d-d2617c0f81a5\",\"eventType\":0,\"code\":200502,\"eventTypeName\":\"摆臂阻挡消除\",\"commonUse\":0,\"isUse\":\"10\",\"remark\":null,\"isUsing\":1},{\"eventTypeId\":\"ff02e14d-6d46-4ede-9f37-53adcdb30a6d\",\"eventType\":0,\"code\":200503,\"eventTypeName\":\"电机或传感器异常\",\"commonUse\":0,\"isUse\":\"10\",\"remark\":null,\"isUsing\":1},{\"eventTypeId\":\"b361d95d-acde-4e53-9ab5-69e0d1fc014f\",\"eventType\":0,\"code\":200512,\"eventTypeName\":\"设备升级本地人脸建模失败\",\"commonUse\":0,\"isUse\":\"10\",\"remark\":null,\"isUsing\":1},{\"eventTypeId\":\"6c9e67cf-48f8-44f6-929f-7f4cee274000\",\"eventType\":0,\"code\":200520,\"eventTypeName\":\"误闯报警\",\"commonUse\":0,\"isUse\":\"10\",\"remark\":null,\"isUsing\":1},{\"eventTypeId\":\"dfdfd59f-24f8-4066-bdaa-85b3f45b7c9f\",\"eventType\":0,\"code\":262403,\"eventTypeName\":\"智能锁防撬\",\"commonUse\":0,\"isUse\":\"10\",\"remark\":null,\"isUsing\":1},{\"eventTypeId\":\"1215e6d4-71bd-4786-bc2d-04a4b26b02c7\",\"eventType\":0,\"code\":262404,\"eventTypeName\":\"智能锁电池欠压\",\"commonUse\":0,\"isUse\":\"10\",\"remark\":null,\"isUsing\":1}]},{\"type\":1,\"eventList\":[{\"eventTypeId\":\"08752318-52d2-4630-b995-0c3e70dc47a1\",\"eventType\":1,\"code\":197390,\"eventTypeName\":\"认证成功等待远程开门\",\"commonUse\":0,\"isUse\":\"10\",\"remark\":null,\"isUsing\":1},{\"eventTypeId\":\"dd4beea3-20ad-4e43-ab71-ae133d6a330b\",\"eventType\":1,\"code\":197391,\"eventTypeName\":\"卡不属于多重认证群组\",\"commonUse\":0,\"isUse\":\"10\",\"remark\":null,\"isUsing\":1},{\"eventTypeId\":\"6c39d412-269d-418d-afdf-96165afeae35\",\"eventType\":1,\"code\":198148,\"eventTypeName\":\"常关时段开始\",\"commonUse\":0,\"isUse\":\"10\",\"remark\":null,\"isUsing\":1},{\"eventTypeId\":\"e506eed2-19bc-4a16-bd08-01da63f5456f\",\"eventType\":1,\"code\":198150,\"eventTypeName\":\"常关时段结束\",\"commonUse\":0,\"isUse\":\"10\",\"remark\":null,\"isUsing\":1},{\"eventTypeId\":\"2e21b804-2353-4f69-9af0-7abafc2f367b\",\"eventType\":1,\"code\":198400,\"eventTypeName\":\"开门超时\",\"commonUse\":0,\"isUse\":\"10\",\"remark\":null,\"isUsing\":1},{\"eventTypeId\":\"699c36a0-6381-44c7-95ab-9551ec45b9af\",\"eventType\":1,\"code\":198401,\"eventTypeName\":\"常开时段开始\",\"commonUse\":0,\"isUse\":\"10\",\"remark\":null,\"isUsing\":1},{\"eventTypeId\":\"edec1d0c-9ee4-4d18-869a-e26267f21e8c\",\"eventType\":1,\"code\":198402,\"eventTypeName\":\"常开时段结束\",\"commonUse\":0,\"isUse\":\"10\",\"remark\":null,\"isUsing\":1},{\"eventTypeId\":\"823c54eb-e1fd-43ae-b1dd-026ffac5a4a9\",\"eventType\":1,\"code\":198404,\"eventTypeName\":\"进入首卡常开状态\",\"commonUse\":0,\"isUse\":\"10\",\"remark\":null,\"isUsing\":1},{\"eventTypeId\":\"3a083b42-ecd9-4608-b349-957ade1fd139\",\"eventType\":1,\"code\":198405,\"eventTypeName\":\"结束首卡常开状态\",\"commonUse\":0,\"isUse\":\"10\",\"remark\":null,\"isUsing\":1},{\"eventTypeId\":\"f484b125-96cd-42ca-8a48-d07f4de26820\",\"eventType\":1,\"code\":198657,\"eventTypeName\":\"门被外力开启\",\"commonUse\":0,\"isUse\":\"10\",\"remark\":null,\"isUsing\":1},{\"eventTypeId\":\"e82481b1-7d4f-4a2a-b829-7e316657438e\",\"eventType\":1,\"code\":198913,\"eventTypeName\":\"正常开门\",\"commonUse\":0,\"isUse\":\"10\",\"remark\":null,\"isUsing\":1},{\"eventTypeId\":\"7c87342b-a1a5-4a5a-b31b-29a52937a9d8\",\"eventType\":1,\"code\":198916,\"eventTypeName\":\"按钮开门\",\"commonUse\":0,\"isUse\":\"10\",\"remark\":null,\"isUsing\":1},{\"eventTypeId\":\"c9fe29b8-dda7-4606-835c-889bb52beb91\",\"eventType\":1,\"code\":198919,\"eventTypeName\":\"远程软件开门\",\"commonUse\":0,\"isUse\":\"10\",\"remark\":null,\"isUsing\":1},{\"eventTypeId\":\"664e7077-1b66-405d-848e-3b9ca867206a\",\"eventType\":1,\"code\":198922,\"eventTypeName\":\"远程软件常开\",\"commonUse\":0,\"isUse\":\"10\",\"remark\":null,\"isUsing\":1},{\"eventTypeId\":\"e9d8d604-312f-4bef-b169-f74642da6402\",\"eventType\":1,\"code\":199169,\"eventTypeName\":\"正常关门\",\"commonUse\":0,\"isUse\":\"10\",\"remark\":null,\"isUsing\":1},{\"eventTypeId\":\"eac96b23-f9e1-4f57-8a5c-5cab57b3d15d\",\"eventType\":1,\"code\":199171,\"eventTypeName\":\"远程软件关门\",\"commonUse\":0,\"isUse\":\"10\",\"remark\":null,\"isUsing\":1},{\"eventTypeId\":\"edae0fff-3d71-4c83-ae56-ba73f18ec5ac\",\"eventType\":1,\"code\":199172,\"eventTypeName\":\"远程软件常闭\",\"commonUse\":0,\"isUse\":\"10\",\"remark\":null,\"isUsing\":1},{\"eventTypeId\":\"6a0caae6-337f-4438-8b59-b79fcb03d06e\",\"eventType\":1,\"code\":199941,\"eventTypeName\":\"门锁打开\",\"commonUse\":0,\"isUse\":\"10\",\"remark\":null,\"isUsing\":1},{\"eventTypeId\":\"ab1f04e0-6191-4267-8d8f-090d9eed8501\",\"eventType\":1,\"code\":199942,\"eventTypeName\":\"门锁关闭\",\"commonUse\":0,\"isUse\":\"10\",\"remark\":null,\"isUsing\":1}]},{\"type\":2,\"eventList\":[{\"eventTypeId\":\"b3a8c05b-4856-4c17-af33-14c3fef473d4\",\"eventType\":2,\"code\":200453,\"eventTypeName\":\"读卡器防拆报警\",\"commonUse\":0,\"isUse\":\"10\",\"remark\":null,\"isUsing\":1},{\"eventTypeId\":\"84e7b46c-f412-42ae-8611-5968e3645576\",\"eventType\":2,\"code\":200454,\"eventTypeName\":\"读卡器防拆报警恢复\",\"commonUse\":0,\"isUse\":\"10\",\"remark\":null,\"isUsing\":1},{\"eventTypeId\":\"b618a95c-16c1-4208-921f-98cc8a90fff2\",\"eventType\":2,\"code\":200455,\"eventTypeName\":\"读卡器掉线报警\",\"commonUse\":0,\"isUse\":\"10\",\"remark\":null,\"isUsing\":1},{\"eventTypeId\":\"04af0ad9-45a4-4e69-bf64-fe3e5ae6fe85\",\"eventType\":2,\"code\":200456,\"eventTypeName\":\"读卡器掉线报警恢复\",\"commonUse\":0,\"isUse\":\"10\",\"remark\":null,\"isUsing\":1}]}],\"1\":[{\"type\":3,\"eventList\":[{\"eventTypeId\":\"89b85b17-0e68-494d-a71d-6636e374c3f9\",\"eventType\":3,\"code\":196868,\"eventTypeName\":\"来宾卡认证通过\",\"commonUse\":0,\"isUse\":\"10\",\"remark\":null,\"isUsing\":1},{\"eventTypeId\":\"f7c2bd0c-4cb0-43b6-b90d-3d35a2c578ce\",\"eventType\":3,\"code\":196885,\"eventTypeName\":\"刷卡+指纹认证通过\",\"commonUse\":0,\"isUse\":\"10\",\"remark\":null,\"isUsing\":1},{\"eventTypeId\":\"55b36e72-71c1-436f-bb4e-dfba8594fcc3\",\"eventType\":3,\"code\":196886,\"eventTypeName\":\"刷卡+指纹+密码认证通过\",\"commonUse\":0,\"isUse\":\"10\",\"remark\":null,\"isUsing\":1},{\"eventTypeId\":\"3b9e4e58-7a8d-4908-855f-6c448440cc6e\",\"eventType\":3,\"code\":196887,\"eventTypeName\":\"指纹+密码认证通过\",\"commonUse\":0,\"isUse\":\"10\",\"remark\":null,\"isUsing\":1},{\"eventTypeId\":\"c8a6cd82-91a1-46d5-af21-74aeacd85f8d\",\"eventType\":3,\"code\":196897,\"eventTypeName\":\"工号+密码认证通过\",\"commonUse\":0,\"isUse\":\"10\",\"remark\":null,\"isUsing\":1},{\"eventTypeId\":\"aaac8146-eef9-431e-aafc-470f9ddfcd11\",\"eventType\":3,\"code\":197127,\"eventTypeName\":\"指纹比对通过\",\"commonUse\":0,\"isUse\":\"10\",\"remark\":null,\"isUsing\":1},{\"eventTypeId\":\"ce0975a1-0c94-442e-b533-4ab8dcc25d3d\",\"eventType\":3,\"code\":198914,\"eventTypeName\":\"合法卡比对通过\",\"commonUse\":0,\"isUse\":\"10\",\"remark\":null,\"isUsing\":1},{\"eventTypeId\":\"bf12274d-3382-4468-ace0-6f07c6e73392\",\"eventType\":3,\"code\":198915,\"eventTypeName\":\"刷卡+密码认证通过\",\"commonUse\":0,\"isUse\":\"10\",\"remark\":null,\"isUsing\":1}]},{\"type\":4,\"eventList\":[{\"eventTypeId\":\"162db6ee-cda5-416d-9b59-df7487103acc\",\"eventType\":4,\"code\":196888,\"eventTypeName\":\"人脸+指纹认证通过\",\"commonUse\":0,\"isUse\":\"10\",\"remark\":null,\"isUsing\":1},{\"eventTypeId\":\"34d899ed-05be-4325-afcc-f11928bcc3e3\",\"eventType\":4,\"code\":196889,\"eventTypeName\":\"人脸+密码认证通过\",\"commonUse\":0,\"isUse\":\"10\",\"remark\":null,\"isUsing\":1},{\"eventTypeId\":\"cca5860d-5c8f-47b0-a822-de5e7a8eca9c\",\"eventType\":4,\"code\":196890,\"eventTypeName\":\"人脸+刷卡认证通过\",\"commonUse\":0,\"isUse\":\"10\",\"remark\":null,\"isUsing\":1},{\"eventTypeId\":\"8ce57bf4-c07f-4029-b4c8-117f8fa9ec6d\",\"eventType\":4,\"code\":196891,\"eventTypeName\":\"人脸+密码+指纹认证通过\",\"commonUse\":0,\"isUse\":\"10\",\"remark\":null,\"isUsing\":1},{\"eventTypeId\":\"46a3457b-5044-4aa2-b5ad-dd3c6d712b95\",\"eventType\":4,\"code\":196892,\"eventTypeName\":\"人脸+刷卡+指纹认证通过\",\"commonUse\":0,\"isUse\":\"10\",\"remark\":null,\"isUsing\":1},{\"eventTypeId\":\"ec272ae4-eaac-4de7-99e6-a49fd25abc16\",\"eventType\":4,\"code\":196893,\"eventTypeName\":\"人脸认证通过\",\"commonUse\":0,\"isUse\":\"10\",\"remark\":null,\"isUsing\":1}]},{\"type\":5,\"eventList\":[{\"eventTypeId\":\"7bc55ca4-1a60-48af-b9e9-2bf0bcca60f6\",\"eventType\":5,\"code\":197162,\"eventTypeName\":\"人证比对通过\",\"commonUse\":0,\"isUse\":\"10\",\"remark\":null,\"isUsing\":1}]},{\"type\":6,\"eventList\":[{\"eventTypeId\":\"c7b4a2c8-eaad-4543-b918-b818252f9161\",\"eventType\":6,\"code\":196874,\"eventTypeName\":\"首卡比对通过\",\"commonUse\":0,\"isUse\":\"10\",\"remark\":null,\"isUsing\":1},{\"eventTypeId\":\"3b5def8b-5ef1-46d1-8485-f1a13ad97870\",\"eventType\":6,\"code\":196875,\"eventTypeName\":\"残疾人卡比对通过\",\"commonUse\":0,\"isUse\":\"10\",\"remark\":null,\"isUsing\":1},{\"eventTypeId\":\"f9ec5575-b280-4bcb-b2c6-768b78bdb4bb\",\"eventType\":6,\"code\":196883,\"eventTypeName\":\"多重认证成功\",\"commonUse\":0,\"isUse\":\"10\",\"remark\":null,\"isUsing\":1},{\"eventTypeId\":\"6c7ab217-ace3-4e3e-9f52-c1668fe2ac41\",\"eventType\":6,\"code\":196884,\"eventTypeName\":\"多重认证超级密码成功\",\"commonUse\":0,\"isUse\":\"10\",\"remark\":null,\"isUsing\":1},{\"eventTypeId\":\"d447abd2-fe36-498e-98bd-521edb43b28b\",\"eventType\":6,\"code\":198921,\"eventTypeName\":\"超级卡比对通过\",\"commonUse\":0,\"isUse\":\"10\",\"remark\":null,\"isUsing\":1},{\"eventTypeId\":\"67947e7d-a6d8-4040-a649-a10b46806ba1\",\"eventType\":6,\"code\":199425,\"eventTypeName\":\"胁迫卡比对通过\",\"commonUse\":0,\"isUse\":\"10\",\"remark\":null,\"isUsing\":1},{\"eventTypeId\":\"2620d897-4042-4cdf-98f0-8ce9236f1384\",\"eventType\":6,\"code\":200516,\"eventTypeName\":\"组合认证通过\",\"commonUse\":0,\"isUse\":\"10\",\"remark\":null,\"isUsing\":1}]},{\"type\":13,\"eventList\":[{\"eventTypeId\":\"ce527b08-4716-452c-a44a-93dd070cc3fb\",\"eventType\":13,\"code\":263432,\"eventTypeName\":\"智能锁中心远程开锁\",\"commonUse\":0,\"isUse\":\"10\",\"remark\":null,\"isUsing\":1}]}],\"2\":[{\"type\":7,\"eventList\":[{\"eventTypeId\":\"89f93963-8251-451a-9233-05ee0ee624d9\",\"eventType\":7,\"code\":196898,\"eventTypeName\":\"非正规Mifare卡认证失败\",\"commonUse\":0,\"isUse\":\"10\",\"remark\":null,\"isUsing\":1},{\"eventTypeId\":\"a02cb36c-8d8d-4241-a60a-c0444c423d46\",\"eventType\":7,\"code\":197121,\"eventTypeName\":\"刷卡+密码认证失败\",\"commonUse\":0,\"isUse\":\"10\",\"remark\":null,\"isUsing\":1},{\"eventTypeId\":\"5a238606-11ba-45ac-8e01-42d412f36e6c\",\"eventType\":7,\"code\":197122,\"eventTypeName\":\"输入卡号错误\",\"commonUse\":0,\"isUse\":\"10\",\"remark\":null,\"isUsing\":1},{\"eventTypeId\":\"1ddc2a38-b03a-408c-9d54-54d8ba617332\",\"eventType\":7,\"code\":197123,\"eventTypeName\":\"输入密码而非卡号\",\"commonUse\":0,\"isUse\":\"10\",\"remark\":null,\"isUsing\":1},{\"eventTypeId\":\"890cc472-4849-4380-acde-ce54a6b027eb\",\"eventType\":7,\"code\":197124,\"eventTypeName\":\"卡号长度错误\",\"commonUse\":0,\"isUse\":\"10\",\"remark\":null,\"isUsing\":1},{\"eventTypeId\":\"4e421e8f-ba64-4201-8144-77097a062c29\",\"eventType\":7,\"code\":197125,\"eventTypeName\":\"卡号数字检查错误\",\"commonUse\":0,\"isUse\":\"10\",\"remark\":null,\"isUsing\":1},{\"eventTypeId\":\"73acf66b-1e69-4130-ae77-013ca72c4647\",\"eventType\":7,\"code\":197128,\"eventTypeName\":\"指纹比对失败\",\"commonUse\":0,\"isUse\":\"10\",\"remark\":null,\"isUsing\":1},{\"eventTypeId\":\"079e6c0f-cfee-457e-9317-2e6ec365df29\",\"eventType\":7,\"code\":197134,\"eventTypeName\":\"刷卡+指纹认证失败\",\"commonUse\":0,\"isUse\":\"10\",\"remark\":null,\"isUsing\":1},{\"eventTypeId\":\"c9600505-bcd2-4f79-a62a-30c9839f03e9\",\"eventType\":7,\"code\":197136,\"eventTypeName\":\"刷卡+指纹+密码认证失败\",\"commonUse\":0,\"isUse\":\"10\",\"remark\":null,\"isUsing\":1},{\"eventTypeId\":\"68887c1d-f62e-4899-8396-734ac8db8da5\",\"eventType\":7,\"code\":197137,\"eventTypeName\":\"刷卡+指纹+密码认证超时\",\"commonUse\":0,\"isUse\":\"10\",\"remark\":null,\"isUsing\":1},{\"eventTypeId\":\"39763196-afe5-48bd-bf7e-877e1013b3f1\",\"eventType\":7,\"code\":197138,\"eventTypeName\":\"指纹+密码认证失败\",\"commonUse\":0,\"isUse\":\"10\",\"remark\":null,\"isUsing\":1},{\"eventTypeId\":\"fb11533b-842d-47b2-b941-35fd7e5faea5\",\"eventType\":7,\"code\":197140,\"eventTypeName\":\"指纹不存在\",\"commonUse\":0,\"isUse\":\"10\",\"remark\":null,\"isUsing\":1},{\"eventTypeId\":\"1c840424-f384-4d86-aa22-60ccfd6a8de3\",\"eventType\":7,\"code\":197158,\"eventTypeName\":\"工号+密码认证失败\",\"commonUse\":0,\"isUse\":\"10\",\"remark\":null,\"isUsing\":1},{\"eventTypeId\":\"99dfaa4c-f22a-4331-8e20-11f54923183f\",\"eventType\":7,\"code\":197398,\"eventTypeName\":\"来宾卡无权限\",\"commonUse\":0,\"isUse\":\"10\",\"remark\":null,\"isUsing\":1},{\"eventTypeId\":\"c12bfa4d-ccc8-4a84-9ec1-b84fc9857f96\",\"eventType\":7,\"code\":197633,\"eventTypeName\":\"卡号过期\",\"commonUse\":0,\"isUse\":\"10\",\"remark\":null,\"isUsing\":1},{\"eventTypeId\":\"ffaf2fdf-5111-40a6-8d97-02bf97fa6461\",\"eventType\":7,\"code\":197634,\"eventTypeName\":\"无此卡号\",\"commonUse\":0,\"isUse\":\"10\",\"remark\":null,\"isUsing\":1},{\"eventTypeId\":\"89baeb7d-3774-48cd-a73f-32101004fd3f\",\"eventType\":7,\"code\":197635,\"eventTypeName\":\"卡未分配权限\",\"commonUse\":0,\"isUse\":\"10\",\"remark\":null,\"isUsing\":1},{\"eventTypeId\":\"716b4baf-acab-4765-bd19-5b93c8d697f0\",\"eventType\":7,\"code\":200519,\"eventTypeName\":\"密码不匹配\",\"commonUse\":0,\"isUse\":\"10\",\"remark\":null,\"isUsing\":1},{\"eventTypeId\":\"5a6a2b45-a8ff-41b9-89fd-cf957d54303a\",\"eventType\":7,\"code\":261952,\"eventTypeName\":\"卡加密校验失败\",\"commonUse\":0,\"isUse\":\"10\",\"remark\":null,\"isUsing\":1}]},{\"type\":8,\"eventList\":[{\"eventTypeId\":\"fbbec124-e581-4b0a-b832-8d01f919d92a\",\"eventType\":8,\"code\":197141,\"eventTypeName\":\"人脸+指纹认证失败\",\"commonUse\":0,\"isUse\":\"10\",\"remark\":null,\"isUsing\":1},{\"eventTypeId\":\"70e7adb7-4271-4ed4-8967-4339ce3840ee\",\"eventType\":8,\"code\":197142,\"eventTypeName\":\"人脸+指纹认证超时\",\"commonUse\":0,\"isUse\":\"10\",\"remark\":null,\"isUsing\":1},{\"eventTypeId\":\"8012f494-5c7a-4aab-ae0c-ff37da2cc05a\",\"eventType\":8,\"code\":197143,\"eventTypeName\":\"人脸+密码认证失败\",\"commonUse\":0,\"isUse\":\"10\",\"remark\":null,\"isUsing\":1},{\"eventTypeId\":\"76fff6cf-5c88-4707-b286-eb5853baeef3\",\"eventType\":8,\"code\":197144,\"eventTypeName\":\"人脸+密码认证超时\",\"commonUse\":0,\"isUse\":\"10\",\"remark\":null,\"isUsing\":1},{\"eventTypeId\":\"5d83db8f-f6f2-4f35-b3c4-566b3cc95fc1\",\"eventType\":8,\"code\":197145,\"eventTypeName\":\"人脸+刷卡认证失败\",\"commonUse\":0,\"isUse\":\"10\",\"remark\":null,\"isUsing\":1},{\"eventTypeId\":\"7ebee947-d920-4696-921c-3301b33f020f\",\"eventType\":8,\"code\":197146,\"eventTypeName\":\"人脸+刷卡认证超时\",\"commonUse\":0,\"isUse\":\"10\",\"remark\":null,\"isUsing\":1},{\"eventTypeId\":\"365d2671-f974-4082-9b22-791b9c1209aa\",\"eventType\":8,\"code\":197147,\"eventTypeName\":\"人脸+密码+指纹认证失败\",\"commonUse\":0,\"isUse\":\"10\",\"remark\":null,\"isUsing\":1},{\"eventTypeId\":\"dd34fd75-2761-4ff8-9091-e6a4664a36ff\",\"eventType\":8,\"code\":197148,\"eventTypeName\":\"人脸+密码+指纹认证超时\",\"commonUse\":0,\"isUse\":\"10\",\"remark\":null,\"isUsing\":1},{\"eventTypeId\":\"1e65cca9-2296-4e54-8ff3-737a702c4048\",\"eventType\":8,\"code\":197149,\"eventTypeName\":\"人脸+刷卡+指纹认证失败\",\"commonUse\":0,\"isUse\":\"10\",\"remark\":null,\"isUsing\":1},{\"eventTypeId\":\"0dc02cd5-d902-4e49-a360-0b89dfce1582\",\"eventType\":8,\"code\":197150,\"eventTypeName\":\"人脸+刷卡+指纹认证超时\",\"commonUse\":0,\"isUse\":\"10\",\"remark\":null,\"isUsing\":1},{\"eventTypeId\":\"ce6d99db-8494-4c9d-b70f-bb98fbc400bb\",\"eventType\":8,\"code\":197151,\"eventTypeName\":\"人脸认证失败\",\"commonUse\":0,\"isUse\":\"10\",\"remark\":null,\"isUsing\":1},{\"eventTypeId\":\"a0bd4279-8c28-438e-a123-5b864250f10b\",\"eventType\":8,\"code\":197160,\"eventTypeName\":\"人脸识别失败\",\"commonUse\":0,\"isUse\":\"10\",\"remark\":null,\"isUsing\":1},{\"eventTypeId\":\"7c1f4917-71a4-4d56-aa35-c8bb61177130\",\"eventType\":8,\"code\":197161,\"eventTypeName\":\"真人检测失败\",\"commonUse\":0,\"isUse\":\"10\",\"remark\":null,\"isUsing\":1}]},{\"type\":9,\"eventList\":[{\"eventTypeId\":\"e42cb47b-a12c-463f-b6d3-26cd580174d0\",\"eventType\":9,\"code\":197163,\"eventTypeName\":\"人证比对失败\",\"commonUse\":0,\"isUse\":\"10\",\"remark\":null,\"isUsing\":1}]},{\"type\":10,\"eventList\":[{\"eventTypeId\":\"15363bf1-4099-4b31-b888-ca8d9d0e6831\",\"eventType\":10,\"code\":197383,\"eventTypeName\":\"反潜回认证失败\",\"commonUse\":0,\"isUse\":\"10\",\"remark\":null,\"isUsing\":1},{\"eventTypeId\":\"6c74849b-bb6b-4cc3-a81e-9b831e01e11e\",\"eventType\":10,\"code\":197392,\"eventTypeName\":\"卡不在多重认证时段内\",\"commonUse\":0,\"isUse\":\"10\",\"remark\":null,\"isUsing\":1},{\"eventTypeId\":\"4654b244-4a93-4d8b-bf49-c4cd1a7c7bea\",\"eventType\":10,\"code\":197393,\"eventTypeName\":\"多重认证超级密码错误\",\"commonUse\":0,\"isUse\":\"10\",\"remark\":null,\"isUsing\":1},{\"eventTypeId\":\"7e4bbd23-2ddc-4a73-a3ad-d8893c73f40c\",\"eventType\":10,\"code\":197394,\"eventTypeName\":\"多重认证远程认证失败\",\"commonUse\":0,\"isUse\":\"10\",\"remark\":null,\"isUsing\":1},{\"eventTypeId\":\"e62a0c72-fb1e-46cf-921a-4f63a22eac27\",\"eventType\":10,\"code\":197395,\"eventTypeName\":\"残疾人卡未分配权限\",\"commonUse\":0,\"isUse\":\"10\",\"remark\":null,\"isUsing\":1},{\"eventTypeId\":\"11c62c79-2f2f-4f36-834e-263d65b80af4\",\"eventType\":10,\"code\":197396,\"eventTypeName\":\"胁迫卡未分配权限\",\"commonUse\":0,\"isUse\":\"10\",\"remark\":null,\"isUsing\":1},{\"eventTypeId\":\"0a75de59-8f5e-43d1-a820-5399604b2567\",\"eventType\":10,\"code\":197397,\"eventTypeName\":\"超级卡未分配权限\",\"commonUse\":0,\"isUse\":\"10\",\"remark\":null,\"isUsing\":1},{\"eventTypeId\":\"bcc6e678-f64a-4bdc-83d6-2e0073d41ca0\",\"eventType\":10,\"code\":197400,\"eventTypeName\":\"多重认证重复认证\",\"commonUse\":0,\"isUse\":\"10\",\"remark\":null,\"isUsing\":1},{\"eventTypeId\":\"bf37a9f4-6d91-43ff-8434-ae9cc39b4bde\",\"eventType\":10,\"code\":197401,\"eventTypeName\":\"多重认证超时\",\"commonUse\":0,\"isUse\":\"10\",\"remark\":null,\"isUsing\":1},{\"eventTypeId\":\"7f7d8aae-17fe-47a0-b130-0cf3bf665218\",\"eventType\":10,\"code\":198146,\"eventTypeName\":\"互锁中无法开门\",\"commonUse\":0,\"isUse\":\"10\",\"remark\":null,\"isUsing\":1},{\"eventTypeId\":\"bac1ffde-6a72-44ce-aa83-0d50e5593ead\",\"eventType\":10,\"code\":198149,\"eventTypeName\":\"反潜回读卡器刷卡无效\",\"commonUse\":0,\"isUse\":\"10\",\"remark\":null,\"isUsing\":1},{\"eventTypeId\":\"d9b6586b-b9ac-464b-9df5-6b378ed43778\",\"eventType\":10,\"code\":200513,\"eventTypeName\":\"门状态常闭或休眠状态认证失败\",\"commonUse\":0,\"isUse\":\"10\",\"remark\":null,\"isUsing\":1},{\"eventTypeId\":\"e46fc56f-4231-452b-bfdf-1246db211442\",\"eventType\":10,\"code\":200514,\"eventTypeName\":\"认证计划休眠模式认证失败\",\"commonUse\":0,\"isUse\":\"10\",\"remark\":null,\"isUsing\":1},{\"eventTypeId\":\"7164484e-d68d-4a66-9d54-90f7116d25e1\",\"eventType\":10,\"code\":200517,\"eventTypeName\":\"组合认证超时\",\"commonUse\":0,\"isUse\":\"10\",\"remark\":null,\"isUsing\":1}]},{\"type\":14,\"eventList\":[{\"eventTypeId\":\"fa9c8058-d23f-4c40-9280-dc8151ec3aea\",\"eventType\":14,\"code\":197361,\"eventTypeName\":\"智能锁认证错误次数过多\",\"commonUse\":0,\"isUse\":\"10\",\"remark\":null,\"isUsing\":1}]},{\"type\":11,\"eventList\":[{\"eventTypeId\":\"ac8e6cf1-4436-4de5-99fa-e44640896280\",\"eventType\":11,\"code\":197889,\"eventTypeName\":\"黑名单事件\",\"commonUse\":0,\"isUse\":\"10\",\"remark\":null,\"isUsing\":1},{\"eventTypeId\":\"93e324a4-bfcf-4d20-a1de-b33bfb2978da\",\"eventType\":11,\"code\":198658,\"eventTypeName\":\"互锁门未关闭\",\"commonUse\":0,\"isUse\":\"10\",\"remark\":null,\"isUsing\":1},{\"eventTypeId\":\"b0dc3713-cce9-451d-9ddf-8db6e2c0ee97\",\"eventType\":11,\"code\":199428,\"eventTypeName\":\"胁迫报警\",\"commonUse\":0,\"isUse\":\"10\",\"remark\":null,\"isUsing\":1},{\"eventTypeId\":\"e497b9d3-2646-4a97-bd85-93c79b1f5de4\",\"eventType\":11,\"code\":199429,\"eventTypeName\":\"卡号认证超次报警\",\"commonUse\":0,\"isUse\":\"10\",\"remark\":null,\"isUsing\":1},{\"eventTypeId\":\"89495765-4da0-4bb8-88ea-61c651822384\",\"eventType\":11,\"code\":199681,\"eventTypeName\":\"解除警报\",\"commonUse\":0,\"isUse\":\"10\",\"remark\":null,\"isUsing\":1}]},{\"type\":12,\"eventList\":[{\"eventTypeId\":\"259e4e18-2dea-4072-b49a-79d8ea535b86\",\"eventType\":12,\"code\":197377,\"eventTypeName\":\"权限不合\",\"commonUse\":0,\"isUse\":\"10\",\"remark\":null,\"isUsing\":1},{\"eventTypeId\":\"bc5608b5-2183-4c67-8030-da108d123e49\",\"eventType\":12,\"code\":197378,\"eventTypeName\":\"假期权限不合\",\"commonUse\":0,\"isUse\":\"10\",\"remark\":null,\"isUsing\":1},{\"eventTypeId\":\"f16022df-3452-4057-831f-5c5c1a6424da\",\"eventType\":12,\"code\":197384,\"eventTypeName\":\"时段组错误\",\"commonUse\":0,\"isUse\":\"10\",\"remark\":null,\"isUsing\":1},{\"eventTypeId\":\"f1fca5ea-84b7-4758-b2eb-9ffc558784ef\",\"eventType\":12,\"code\":200496,\"eventTypeName\":\"尾随通行\",\"commonUse\":0,\"isUse\":\"10\",\"remark\":null,\"isUsing\":1},{\"eventTypeId\":\"6cd60fdc-87b0-45dc-a2a5-013b1a94ae10\",\"eventType\":12,\"code\":200497,\"eventTypeName\":\"反向闯入\",\"commonUse\":0,\"isUse\":\"10\",\"remark\":null,\"isUsing\":1},{\"eventTypeId\":\"86c82257-f707-47d8-9202-675961daaaae\",\"eventType\":12,\"code\":200498,\"eventTypeName\":\"外力冲撞\",\"commonUse\":0,\"isUse\":\"10\",\"remark\":null,\"isUsing\":1},{\"eventTypeId\":\"4b8ab014-9e6b-43bb-8575-0cf719c90c34\",\"eventType\":12,\"code\":200499,\"eventTypeName\":\"翻越\",\"commonUse\":0,\"isUse\":\"10\",\"remark\":null,\"isUsing\":1},{\"eventTypeId\":\"79fc5b5a-ceac-4794-86e1-e76620aaaf7b\",\"eventType\":12,\"code\":200500,\"eventTypeName\":\"通行超时\",\"commonUse\":0,\"isUse\":\"10\",\"remark\":null,\"isUsing\":1}]}]}}";
        JSONObject parse = (JSONObject) JSONObject.parse(sb);
        JSONArray data = parse.getJSONObject("data").getJSONArray("0");
        for (Object datum : data) {
            JSONObject jsonObject = JSON.parseObject(datum.toString());
            String type = jsonObject.getString("type");
            if ("1".equals(type)){
                JSONArray eventList1 = jsonObject.getJSONArray("eventList");
                for (Object o : eventList1) {
                    JSONObject jsonObject1 = JSON.parseObject(o.toString());
                    String eventTypeName = jsonObject1.getString("eventTypeName");
                    String s1 = map.get(eventTypeName);
                    if ("1".equals(s1)){
                        String code = jsonObject1.getString("code");
                        map.remove(eventTypeName);
                        map.put(code,eventTypeName);
                    }
                }
            }
        }
        JSONArray data1 = parse.getJSONObject("data").getJSONArray("1");
        for (Object datum1 : data1) {
            JSONObject jsonObject1 = JSON.parseObject(datum1.toString());
            JSONArray eventList2 = jsonObject1.getJSONArray("eventList");
            for (Object o : eventList2) {
                JSONObject jsonObject2 = JSON.parseObject(o.toString());
                String eventTypeName = jsonObject2.getString("eventTypeName");
                String s1 = map.get(eventTypeName);
                if ("1".equals(s1)){
                    String code = jsonObject2.getString("code");
                    map.remove(eventTypeName);
                    map.put(code,eventTypeName);
                }
            }
        }

        JSONArray data2 = parse.getJSONObject("data").getJSONArray("2");
        for (Object datum2 : data2) {
            JSONObject jsonObject1 = JSON.parseObject(datum2.toString());
            JSONArray eventList2 = jsonObject1.getJSONArray("eventList");
            for (Object o : eventList2) {
                JSONObject jsonObject2 = JSON.parseObject(o.toString());
                String eventTypeName = jsonObject2.getString("eventTypeName");
                String s1 = map.get(eventTypeName);
                if ("1".equals(s1)){
                    String code = jsonObject2.getString("code");
                    map.remove(eventTypeName);
                    map.put(code,eventTypeName);
                }
            }
        }
        System.out.println(map);
        
        for (String s1 : map.keySet()) {
            System.out.println(s1);
        }
        for (String value : map.values()) {
            System.out.println(value);
        }
    }
}
