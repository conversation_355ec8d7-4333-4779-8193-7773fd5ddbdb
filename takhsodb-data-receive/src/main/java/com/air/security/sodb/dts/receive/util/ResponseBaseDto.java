package com.air.security.sodb.dts.receive.util;

/**
 * <AUTHOR>
 */
public class ResponseBaseDto<T> {

    private String returnCode;
    private String returnMsg;

    private T data;

    public ResponseBaseDto() {
    }

    public ResponseBaseDto(String returnCode, String returnMsg, T data) {
        this.returnCode = returnCode;
        this.returnMsg = returnMsg;
        this.data = data;
    }

    public ResponseBaseDto(String returnCode, String returnMsg) {
        this(returnCode, returnMsg, null);
    }

    public ResponseBaseDto(String message) {
        this("0", message);
    }

    public ResponseBaseDto(String message, T data) {
        this("0", message, data);
    }

    public String getReturnCode() {
        return returnCode;
    }

    public void setReturnCode(String returnCode) {
        this.returnCode = returnCode;
    }

    public String getReturnMsg() {
        return returnMsg;
    }

    public void setReturnMsg(String returnMsg) {
        this.returnMsg = returnMsg;
    }

    public T getData() {
        return data;
    }

    public void setData(T data) {
        this.data = data;
    }

}
