package com.air.security.sodb.dts.receive.job;

import com.air.security.sodb.data.core.base.Meta;
import com.air.security.sodb.data.core.constant.MsgIdConstant;
import com.air.security.sodb.data.core.exception.SystemBaseException;
import com.air.security.sodb.data.core.quartz.AbstractJob;
import com.air.security.sodb.data.core.util.HaLog;
import com.air.security.sodb.data.core.util.HttpRequestUtil;
import com.air.security.sodb.data.core.util.PropertyUtil;
import com.air.security.sodb.data.core.util.UuidUtil;
import com.air.security.sodb.dts.receive.server.MqMessageRecvServer;
import com.air.security.sodb.dts.receive.server.MqMessageRecvServerImpl;
import com.air.security.sodb.dts.receive.util.DaHuaTokenDTO;
import com.air.security.sodb.dts.receive.util.DahuaToken;
import com.air.security.sodb.dts.receive.util.PageInfo;
import com.air.security.sodb.dts.receive.util.SrvcHelper;
import com.alibaba.fastjson.JSONObject;
import org.quartz.JobDataMap;
import org.slf4j.Logger;
import org.slf4j.LoggerFactory;

import java.io.IOException;

public class DHAcsEquRestReceiveSchedulerJob extends AbstractJob {
    private static final Logger log = LoggerFactory.getLogger(DHAcsEquRestReceiveSchedulerJob.class);

    private static String dhDeviceRestServiceCode = PropertyUtil.getProperty("dh.device.service.code");

    /**
     * 获取大华通道设备信息定时任务URI
     */
    private static String getChannelURI = PropertyUtil.getProperty("rest.dh.getChannel.uri");

    /**
     *
     */
    private MqMessageRecvServer server = new MqMessageRecvServerImpl();

    @Override
    public void executeJob(JobDataMap paramMap) {
        HaLog.info(log, MsgIdConstant.MS_INF_0001, "获取大华门禁设备信息定时任务");

        DaHuaTokenDTO daClient = DahuaToken.getDaClient();
        try {
            execPost(SrvcHelper.DH_SRVC_HOST + getChannelURI + "?userId="+daClient.getId()+
                            "&userName="+daClient.getLoginName()+"&token=" + daClient.getToken(),
                    SrvcHelper.PAGE_SIZE, 1);
        } catch (Exception e) {
            throw new SystemBaseException("获取大华门禁设备信息失败", e);
        } finally {
            HaLog.info(log, MsgIdConstant.MS_INF_0002, "获取大华门禁设备信息定时任务");
        }

    }

    /**
     * @param url
     * @param pageSize
     * @param pageNo
     * @throws IOException
     */
    private void execPost(String url, int pageSize, int pageNo) throws IOException {
        JSONObject jsonObject = SrvcHelper.getPageParam(pageNo, pageSize);

        String messageBody = HttpRequestUtil.send(url, jsonObject, "utf-8");

        System.out.println(messageBody);

        // 获取分页数据
        PageInfo page = SrvcHelper.getPageInfo(messageBody);
        if (page.getTotalRows() > 0) {

            Meta meta = new Meta();
            meta.setEventType(dhDeviceRestServiceCode);
            meta.setRecvSequence(UuidUtil.getUuid32());
            server.handle(meta, messageBody);

            if (page.hasNextPage()) {
                // 查询下一页
                this.execPost(url, pageSize, page.getCurrentPage() + 1);
            }
        }
    }
}
