package com.air.security.sodb.dts.receive.service;

import com.air.security.sodb.data.core.base.Meta;
import com.air.security.sodb.data.core.exception.SystemBaseException;
import com.alibaba.fastjson.JSONObject;

/**
 * @Description: 消息处理接口
 * @author: zhangc
 * @Date: 2018年12月6日
 */
public interface MessageRecvService {

    /**
     * 消息处理
     *
     * @param meta
     * @param messageBody
     * @throws SystemBaseException
     */
    void execute(Meta meta, String messageBody) throws Exception;

    /**
     * 获取json消息消息
     *
     * @return JSON消息消息
     */
    JSONObject pollSendMessage();

    /**
     * 设置json消息消息
     *
     * @param sendMessage JSON消息消息
     */
    void putSendMessage(JSONObject sendMessage);

}
