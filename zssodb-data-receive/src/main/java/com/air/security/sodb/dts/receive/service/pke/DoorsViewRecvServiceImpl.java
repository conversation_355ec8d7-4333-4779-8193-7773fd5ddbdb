package com.air.security.sodb.dts.receive.service.pke;

import com.air.security.sodb.data.core.base.Meta;
import com.air.security.sodb.data.core.constant.MsgIdConstant;
import com.air.security.sodb.data.core.exception.SystemBaseException;
import com.air.security.sodb.data.core.util.HaLog;
import com.air.security.sodb.dts.receive.service.AbstractMessageRecvService;
import com.alibaba.fastjson.JSONObject;
import org.slf4j.Logger;
import org.slf4j.LoggerFactory;
import org.springframework.stereotype.Service;

/**
 * @Description:设备信息处理
 * <AUTHOR>
@Service("DoorsViewRecvServiceImpl")
public class DoorsViewRecvServiceImpl extends AbstractMessageRecvService {
    private static final Logger log = LoggerFactory.getLogger(DoorsViewRecvServiceImpl.class);

    @Override
    public void execute(Meta meta, String messageBody) throws SystemBaseException {
        HaLog.info(log, MsgIdConstant.MS_INF_0001, meta.getEventType());

        // 获取到的json业务数据
        JSONObject jsonObject = new JSONObject();

        // 响应消息头
        jsonObject.put("meta", meta);

        // 响应消息体
        jsonObject.put("body", messageBody);

        // 转换为JSON格式，并发送消息到指定主题
        super.putSendMessage(jsonObject);
        HaLog.info(log, MsgIdConstant.MS_INF_0002, meta.getEventType());
    }
}
