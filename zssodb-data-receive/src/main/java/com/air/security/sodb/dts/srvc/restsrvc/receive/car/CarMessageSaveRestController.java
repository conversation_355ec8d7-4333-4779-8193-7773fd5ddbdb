package com.air.security.sodb.dts.srvc.restsrvc.receive.car;

import com.air.security.sodb.data.core.base.Meta;
import com.air.security.sodb.data.core.constant.MsgIdConstant;
import com.air.security.sodb.data.core.util.HaLog;
import com.air.security.sodb.data.core.util.PropertyUtil;
import com.air.security.sodb.data.core.util.RequestUtil;
import com.air.security.sodb.data.core.util.UuidUtil;
import com.air.security.sodb.dts.receive.server.MqMessageRecvServer;
import com.air.security.sodb.dts.receive.server.MqMessageRecvServerImpl;
import com.alibaba.fastjson.JSONArray;
import com.alibaba.fastjson.JSONObject;
import org.apache.commons.lang.StringUtils;
import org.slf4j.Logger;
import org.slf4j.LoggerFactory;
import org.springframework.web.bind.annotation.CrossOrigin;
import org.springframework.web.bind.annotation.RequestMapping;
import org.springframework.web.bind.annotation.RequestMethod;
import org.springframework.web.bind.annotation.RestController;

import javax.servlet.http.HttpServletRequest;
import javax.servlet.http.HttpServletResponse;

/**
 * @Description: 停车场数据接入
 * @author: mk
 * @Date: 2020-08-13
 */
@CrossOrigin
@RestController
@RequestMapping("/api/release/carMessage")
public class CarMessageSaveRestController {

    private static final Logger log = LoggerFactory.getLogger(CarMessageSaveRestController.class);

    private static String CAR_ALARM = PropertyUtil.getProperty("zs.car.alarm.service.code");

    private static String CAR_PARK_INFO = PropertyUtil.getProperty("zs.car.park.info.service.code");

    private static String CAR_BLACK_INFO = PropertyUtil.getProperty("zs.car.black.info.service.code");

    private static String CAR_WHITE_INFO = PropertyUtil.getProperty("zs.car.white.info.service.code");

    /**
     *
     */
    private MqMessageRecvServer server = new MqMessageRecvServerImpl();

    /**
     * 接口
     */
    @RequestMapping(value = "/execute", method = RequestMethod.POST)
    public CarDataResponseDto execute(HttpServletRequest request, HttpServletResponse response) {
        JSONObject jsonObj = RequestUtil.requestGetJson(request);
        HaLog.info(log, MsgIdConstant.MS_INF_0001, "停车场数据接入接口");
        CarDataResponseDto responseDto = new CarDataResponseDto();
        try {
            if (null != jsonObj) {
                String message = jsonObj.toJSONString();

                // 执行业务处理
                this.doExecute(message);
            }
            HaLog.info(log, MsgIdConstant.MS_INF_0002, "停车场数据接入接口");
            return responseDto;
        } catch (Exception e) {
            responseDto.failed();
            HaLog.error(log, e, MsgIdConstant.MS_ERR_0001);
            return responseDto;
        }
    }

    /**
     *
     * @param message
     *            请求信息
     * @throws Exception
     */
    protected void doExecute(String message) throws Exception{
        JSONObject json = JSONObject.parseObject(message);
        String eventType = json.getJSONObject("meta").getString("eventType");
        JSONObject body = json.getJSONObject("body");
        Meta meta = new Meta();
        meta.setRecvSequence(UuidUtil.getUuid32());
        if ("CAR_ALARM".equals(eventType)){
            meta.setEventType(CAR_ALARM);
        }else if ("CAR_PARK_INFO".equals(eventType)){
            meta.setEventType(CAR_PARK_INFO);
        }else if ("CAR_BLACK_INFO".equals(eventType)){
            meta.setEventType(CAR_BLACK_INFO);
        }else if ("CAR_WHITE_INFO".equals(eventType)){
            meta.setEventType(CAR_WHITE_INFO);
        }
        if (null!=body && body.size()!=0){
            server.handle(meta, body.toString());
        }
    }

}
