package com.air.security.sodb.dts.receive.util;

import com.air.security.sodb.data.core.exception.SystemBaseException;
import com.air.security.sodb.data.core.util.PropertyUtil;
import com.dahuatech.h8900.oauth.http.DefaultClient;
import com.dahuatech.h8900.oauth.http.H8900Token;
import com.dahuatech.h8900.oauth.http.IClient;

public class DahuaToken {

    private static String DAHUA_HOST = PropertyUtil.getProperty("dahua.host");
    private static String DAHUA_USERNAME = PropertyUtil.getProperty("dahua.username");
    private static String DAHUA_PASSWORD = PropertyUtil.getProperty("dahua.password");

    /**
     * 获取对应的token userId userName 信息
     * @return
     */
    public static DaHuaTokenDTO getDaClient() {
        try {
            IClient client = new DefaultClient(DAHUA_HOST, DAHUA_USERNAME, DAHUA_PASSWORD);
            H8900Token accessToken = client.getAccessToken();
            //验证调用成功
            if (!"true".equals(accessToken.getSuccess())) {
                throw new SystemBaseException(accessToken.getErrMsg());
            }
            DaHuaTokenDTO entity = new DaHuaTokenDTO();
            entity.setToken(accessToken.getToken());
            entity.setId(accessToken.getId());
            entity.setLoginName(accessToken.getLoginName());
            return entity;
        } catch (Exception e) {
            // TODO Auto-generated catch block
            if(e instanceof SystemBaseException) {
                throw (SystemBaseException) e;
            }
            throw new SystemBaseException("获取token失败", e);
        }
    }
}
