#çº¿ç¨æ± å¤§å°
threadpool.count=20
rest.dh.pageSize=200
#kafkaConsumer kafkaæ¶è´¹èéç½®æä»¶è·¯å¾
#kafka.consumer.config.path=D:\\config\\kafkaConsumer.properties
kafka.consumer.config.path=/server/dtsServer/xjjcDataRecv/config/kafkaConsumer.properties
#kafkaProducer kafkaæ¶è´¹èéç½®æä»¶è·¯å¾
#kafka.producer.config.path=D:\\config\\kafkaProducer.properties
kafka.producer.config.path=/server/dtsServer/xjjcDataRecv/config/kafkaProducer.properties
#è®°å½æ°æ®ä¼ è¾æ¥å¿çtopic
kafka.msg.transfer.log.topic=msgTranLog
zssodb.stop.port=20032

# å¤§åè·åtokenå°å
dahua.host=**********
# å¤§åç»å½å
dahua.username=ABPT
# å¤§åè·åtokenå°åå¯ç 
dahua.password=Admin123


# å¤§åæå¡å°å
rest.dh.host=http://**********
# å¤§åééæå¡URI
rest.dh.getChannel.uri=/CardSolution/card/accessControl/channel/bycondition/combined
# å¤§åå·å¡æå¡URI
rest.dh.getRecordCard.uri=/CardSolution/card/accessControl/swingCardRecord/bycondition/combined

# å¤§åè®¾å¤ä¿¡æ¯æå¡code
dh.device.service.code=DahuaDeviceInfo
# å¤§åééç¶ææå¡code
dh.channelstatus.service.code=DahuaChannelEquStatus
# å¤§åå·å¡æ°æ®æå¡code
dh.recordcard.service.code=DahuaRecordCard

sodb.equ.status.req.url=http://***********/api/bs/equ/info/open/getEquInfoByCode/EC02

#ææ£æ°æ®æ¥å¥ä¸»é¢
psgr.service=true
kafka.psgr.msg.input.topics=relsTopic1

#è´§æ£åéæ¥å£
cargo.service=true
kafka.cargo.msg.output.topic = relsTopic2

#èªç­æ°æ®æ¥å¥ä¸»é¢
flight.service=true
kafka.flight.msg.input.topics=relsTopic3

########################  æä»¶æå¡å¨minioåæ°ç¸å³éç½®   ########################
#minioåæ°
endpoint = http://10.6.26.152:9000
access_key = miniominio
secret_key = hayc@123
bucket = datarecv

########################  åè½¦åºæ¥å¥åæ°ç¸å³éç½®   ########################
#åè½¦åºæ¥è­¦æ¥å¥
zs.car.alarm.service.code = carAlarm
#åè½¦åºè¿åºè½¦è¾æ¥å¥
zs.car.park.info.service.code = carParkInfo
#åè½¦åºé»ååæ¥å¥
zs.car.black.info.service.code = carBlackInfo
#åè½¦åºç½ååæ¥å¥
zs.car.white.info.service.code = carWhiteInfo