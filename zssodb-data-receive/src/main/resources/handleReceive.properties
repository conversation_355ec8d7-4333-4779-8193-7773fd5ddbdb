#æ¶æ¯æ ååå¤çServiceéç½®
# å¤§åé¨ç¦è®¾å¤ä¿¡æ¯
DahuaDeviceInfo=DahuaDeviceInfoRecvServiceImpl,cContainerEqu,ACS_EQUINFO_UE,EQU
# å¤§åé¨ç¦è®¾å¤ç¶æ
DahuaChannelEquStatus=DahuaChannelStatusRecvServiceImpl,cC<PERSON>rEqu,ACS_EQU_STATUS_UE,EQU_STATUS
# å¤§åå·å¡è®°å½
DahuaRecordCard=DahuaRecordCardRecvServiceImpl,cContainerRecord,ACS_RECORD_CARD,RECORD_CARD

#è´§è¿å®æ£ç³»ç»-è¿ååºæ¬ä¿¡æ¯
WAY_BILL_LOG=MessageRecvFormatServiceImpl,cContainerCargo,WAY_BILL_LOG,CARGO
#è´§è¿å®æ£ç³»ç»-è¿åå¼åä¿¡æ¯
OPEN_LOG=CargoOpenMessageRecvFormatServiceImpl,cContainerCargo,OPEN_LOG,CARGO
#è´§è¿å®æ£ç³»ç»-è¿åå®æ£ä¿¡æ¯
SECURITY_LOG=MessageRecvFormatServiceImpl,cContainerCargo,SECURITY_LOG,CARGO
#è´§è¿å®æ£ç³»ç»-å®æ£ééçå¼æ¾ä¿¡æ¯
CHECKSTATUS_EQU=MessageRecvFormatServiceImpl,cContainerCargo,CHECKSTATUS_EQU,CARGO

#ææ£æ°æ®
PRSC=ScimsImgMessageRecvFormatServiceImpl,cContainerPassenger,PSGR_SECURITY,PSGR_DATA
check=ScimsImgMessageRecvFormatServiceImpl,cContainerPassenger,PRSCJSON_SECURITY,PSGR_DATA
bag=ScimsImgMessageRecvFormatServiceImpl,cContainerPassenger,PSGR_OPEN_INFO,PSGR_DATA

flight=MessageRecvFormatServiceImpl,cContainerFlight,FLIGHT_DYN,FLIGHT_DATA
weather=MessageRecvFormatServiceImpl,cContainerWeather,WEATHER_DYN,WEATHER_DATA

########################  åè½¦åºæ¥å¥åæ°ç¸å³éç½®   ########################
#åè½¦åºæ¥è­¦æ¥å¥
carAlarm = MessageRecvFormatServiceImpl,cContainerAlarm,CAR_ALARM,ALARM
#åè½¦åºè¿åºè½¦è¾æ¥å¥
carParkInfo = MessageRecvFormatServiceImpl,cContainerCar,CAR_PARK_INFO,CAR_DATA
#åè½¦åºé»ååæ¥å¥
carBlackInfo = MessageRecvFormatServiceImpl,cContainerCar,CAR_BLACK_INFO,CAR_DATA
#åè½¦åºç½ååæ¥å¥
carWhiteInfo = MessageRecvFormatServiceImpl,cContainerCar,CAR_WHITE_INFO,CAR_DATA