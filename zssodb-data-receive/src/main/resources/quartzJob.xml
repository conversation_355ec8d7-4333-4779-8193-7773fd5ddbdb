<?xml version="1.0" encoding="UTF-8"?>
<!--定时任务配置文件： -->
<quartz>
    <job>
        <job-detail>
            <name>dhChannleStatus-Job</name>
            <group>dhChannleStatusGroup</group>
            <description>大华门禁状态采集任务Job</description>
            <job-class>com.air.security.sodb.dts.receive.job.DHChannelRestReceiveSchedulerJob</job-class>
        </job-detail>
        <trigger>
            <switch>on</switch>
            <name>dhChannleStatus-Job-trigger</name>
            <group>dhChannleStatusGroup</group>
            <!-- 每分钟执行一次 -->
            <cron-expression>0 */1 * * * ?</cron-expression>
        </trigger>
    </job>

    <job>
        <job-detail>
            <name>dhAcsEqu-Job</name>
            <group>dhAcsEquGroup</group>
            <description>大华门禁设备采集任务Job</description>
            <job-class>com.air.security.sodb.dts.receive.job.DHAcsEquRestReceiveSchedulerJob</job-class>
        </job-detail>
        <trigger>
            <switch>on</switch>
            <name>dhAcsEqu-Job-trigger</name>
            <group>dhAcsEquGroup</group>
            <!-- 每4小时执行一次 -->
            <cron-expression>0 0 */4 * * ?</cron-expression>
        </trigger>
    </job>

    <job>
        <job-detail>
            <name>dhAcsRecord-Job</name>
            <group>dhAcsRecordGroup</group>
            <description>大华门禁刷卡记录采集任务Job</description>
            <job-class>com.air.security.sodb.dts.receive.job.DHRecordCardRestReceiveSchedulerJob</job-class>
        </job-detail>
        <trigger>
            <switch>on</switch>
            <name>dhAcsRecord-Job-trigger</name>
            <group>dhAcsRecordGroup</group>
            <!-- 每分钟执行一次 -->
            <cron-expression>0 0/1 * * * ?</cron-expression>
        </trigger>
    </job>
</quartz>