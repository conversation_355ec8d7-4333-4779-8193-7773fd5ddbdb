<?xml version="1.0" encoding="UTF-8"?>
<project xmlns="http://maven.apache.org/POM/4.0.0"
         xmlns:xsi="http://www.w3.org/2001/XMLSchema-instance"
         xsi:schemaLocation="http://maven.apache.org/POM/4.0.0 http://maven.apache.org/xsd/maven-4.0.0.xsd">
    <parent>
        <artifactId>sodb-data-receive</artifactId>
        <groupId>com.air.security.sodb</groupId>
        <version>1.0</version>
    </parent>
    <modelVersion>4.0.0</modelVersion>

    <artifactId>hkjc-data-receive</artifactId>

    <properties>
        <project.build.sourceEncoding>UTF-8</project.build.sourceEncoding>
        <maven.compiler.source>1.8</maven.compiler.source>
        <maven.compiler.target>1.8</maven.compiler.target>
        <spring.version>4.3.14.RELEASE</spring.version>
    </properties>

    <dependencies>
        <dependency>
            <groupId>com.air.security.sodb</groupId>
            <artifactId>receive-common</artifactId>
            <version>1.0</version>
        </dependency>

        <!-- 对接凯亚添加 -->
        <dependency>
            <groupId>org.springframework</groupId>
            <artifactId>spring-jdbc</artifactId>
            <version>${spring.version}</version>
        </dependency>
        <dependency>
            <groupId>org.springframework.amqp</groupId>
            <artifactId>spring-amqp</artifactId>
            <version>1.4.5.RELEASE</version>
        </dependency>
        <dependency>
            <groupId>org.springframework</groupId>
            <artifactId>spring-aop</artifactId>
            <version>${spring.version}</version>
        </dependency>
        <dependency>
            <groupId>org.springframework</groupId>
            <artifactId>spring-core</artifactId>
            <version>${spring.version}</version>
        </dependency>
        <dependency>
            <groupId>org.springframework</groupId>
            <artifactId>spring-expression</artifactId>
            <version>${spring.version}</version>
        </dependency>
        <dependency>
            <groupId>org.springframework</groupId>
            <artifactId>spring-messaging</artifactId>
            <version>${spring.version}</version>
        </dependency>
        <dependency>
            <groupId>org.springframework</groupId>
            <artifactId>spring-orm</artifactId>
            <version>${spring.version}</version>
        </dependency>
        <dependency>
            <groupId>org.springframework.amqp</groupId>
            <artifactId>spring-rabbit</artifactId>
            <version>1.4.5.RELEASE</version>
        </dependency>
        <dependency>
            <groupId>org.springframework.retry</groupId>
            <artifactId>spring-retry</artifactId>
            <version>1.1.2.RELEASE</version>
        </dependency>
        <dependency>
            <groupId>org.springframework</groupId>
            <artifactId>spring-tx</artifactId>
            <version>${spring.version}</version>
        </dependency>
        <dependency>
            <groupId>org.springframework</groupId>
            <artifactId>spring-web</artifactId>
            <version>${spring.version}</version>
        </dependency>
        <dependency>
            <groupId>org.springframework</groupId>
            <artifactId>spring-webmvc</artifactId>
            <version>${spring.version}</version>
        </dependency>
        <dependency>
            <groupId>com.caucho</groupId>
            <artifactId>hessian</artifactId>
            <version>4.0.7</version>
        </dependency>
        <!-- 对接凯亚添加 -->
        <!-- spring end-->

        <dependency>
            <groupId>org.apache.cxf</groupId>
            <artifactId>cxf-rt-frontend-jaxws</artifactId>
            <version>3.3.4</version>
        </dependency>

        <dependency>
            <groupId>org.apache.cxf</groupId>
            <artifactId>cxf-rt-transports-http</artifactId>
            <version>3.3.4</version>
        </dependency>

        <dependency>
            <groupId>xerces</groupId>
            <artifactId>xercesImpl</artifactId>
            <version>2.12.0</version>
        </dependency>

        <dependency>
            <groupId>xml-apis</groupId>
            <artifactId>xml-apis</artifactId>
            <version>1.4.01</version>
        </dependency>

        <dependency>
            <groupId>com.rabbitmq</groupId>
            <artifactId>amqp-client</artifactId>
            <version>3.3.4</version>
        </dependency>

        <!-- imf mq依赖 start -->
        <dependency>
            <groupId>com.ibm.disthub2</groupId>
            <artifactId>CL3Export</artifactId>
            <version>1.0</version>
            <scope>system</scope>
            <systemPath>${basedir}/lib/CL3Export.jar</systemPath>
        </dependency>

        <dependency>
            <groupId>com.ibm.disthub2</groupId>
            <artifactId>CL3Noneexport</artifactId>
            <version>1.0</version>
            <scope>system</scope>
            <systemPath>${basedir}/lib/CL3Nonexport.jar</systemPath>
        </dependency>

        <dependency>
            <groupId>com.ibm</groupId>
            <artifactId>com.ibm.mq</artifactId>
            <version>1.0</version>
            <scope>system</scope>
            <systemPath>${basedir}/lib/com.ibm.mq.jar</systemPath>
        </dependency>

        <dependency>
            <groupId>com.ibm</groupId>
            <artifactId>com.ibm.mq.headers</artifactId>
            <version>1.0</version>
            <scope>system</scope>
            <systemPath>${basedir}/lib/com.ibm.mq.headers.jar</systemPath>
        </dependency>

        <dependency>
            <groupId>com.ibm</groupId>
            <artifactId>com.ibm.mq.commonservices</artifactId>
            <version>1.0</version>
            <scope>system</scope>
            <systemPath>${basedir}/lib/com.ibm.mq.commonservices.jar</systemPath>
        </dependency>

        <dependency>
            <groupId>com.ibm</groupId>
            <artifactId>com.ibm.mq.jmqi</artifactId>
            <version>1.0</version>
            <scope>system</scope>
            <systemPath>${basedir}/lib/com.ibm.mq.jmqi.jar</systemPath>
        </dependency>

        <dependency>
            <groupId>com.ibm</groupId>
            <artifactId>com.ibm.mq.jms.Nojndi</artifactId>
            <version>1.0</version>
            <scope>system</scope>
            <systemPath>${basedir}/lib/com.ibm.mq.jms.Nojndi.jar</systemPath>
        </dependency>

        <dependency>
            <groupId>com.ibm</groupId>
            <artifactId>com.ibm.mq.pcf</artifactId>
            <version>1.0</version>
            <scope>system</scope>
            <systemPath>${basedir}/lib/com.ibm.mq.pcf.jar</systemPath>
        </dependency>

        <dependency>
            <groupId>com.ibm</groupId>
            <artifactId>com.ibm.mq.soap</artifactId>
            <version>1.0</version>
            <scope>system</scope>
            <systemPath>${basedir}/lib/com.ibm.mq.soap.jar</systemPath>
        </dependency>

        <dependency>
            <groupId>com.ibm</groupId>
            <artifactId>com.ibm.mq.tools.ras</artifactId>
            <version>1.0</version>
            <scope>system</scope>
            <systemPath>${basedir}/lib/com.ibm.mq.tools.ras.jar</systemPath>
        </dependency>

        <dependency>
            <groupId>com.ibm</groupId>
            <artifactId>com.ibm.mqjms</artifactId>
            <version>1.0</version>
            <scope>system</scope>
            <systemPath>${basedir}/lib/com.ibm.mqjms.jar</systemPath>
        </dependency>

        <dependency>
            <groupId>javax.resource</groupId>
            <artifactId>connector</artifactId>
            <version>1.3</version>
            <scope>system</scope>
            <systemPath>${basedir}/lib/connector.jar</systemPath>
        </dependency>

        <dependency>
            <groupId>com.ibm.disthub2</groupId>
            <artifactId>dhbcore</artifactId>
            <version>1.0</version>
            <scope>system</scope>
            <systemPath>${basedir}/lib/dhbcore.jar</systemPath>
        </dependency>

        <dependency>
            <groupId>com.sun.jndi</groupId>
            <artifactId>fscontext</artifactId>
            <version>1.2-beta-3</version>
            <scope>system</scope>
            <systemPath>${basedir}/lib/fscontext.jar</systemPath>
        </dependency>

        <dependency>
            <groupId>com.tsystems.aviation.esb</groupId>
            <artifactId>imf</artifactId>
            <version>2.0</version>
            <scope>system</scope>
            <systemPath>${basedir}/lib/Imf_2.0.jar</systemPath>
        </dependency>

        <dependency>
            <groupId>javax.jms</groupId>
            <artifactId>jms</artifactId>
            <version>1.0</version>
            <scope>system</scope>
            <systemPath>${basedir}/lib/jms.jar</systemPath>
        </dependency>

        <dependency>
            <groupId>jndi</groupId>
            <artifactId>jndi</artifactId>
            <version>1.2.1</version>
            <scope>system</scope>
            <systemPath>${basedir}/lib/jndi.jar</systemPath>
        </dependency>

        <dependency>
            <groupId>javax.transaction</groupId>
            <artifactId>jta</artifactId>
            <version>1.1</version>
            <scope>system</scope>
            <systemPath>${basedir}/lib/jta.jar</systemPath>
        </dependency>

        <dependency>
            <groupId>junit</groupId>
            <artifactId>junit</artifactId>
            <version>1.0</version>
            <scope>system</scope>
            <systemPath>${basedir}/lib/junit.jar</systemPath>
        </dependency>

        <dependency>
            <groupId>ldap</groupId>
            <artifactId>ldap</artifactId>
            <version>1.0</version>
            <scope>system</scope>
            <systemPath>${basedir}/lib/ldap.jar</systemPath>
        </dependency>

        <dependency>
            <groupId>com.sun.jndi</groupId>
            <artifactId>providerutil</artifactId>
            <version>1.2</version>
            <scope>system</scope>
            <systemPath>${basedir}/lib/providerutil.jar</systemPath>
        </dependency>

        <dependency>
            <groupId>rmm</groupId>
            <artifactId>rmm</artifactId>
            <version>1.0</version>
            <scope>system</scope>
            <systemPath>${basedir}/lib/rmm.jar</systemPath>
        </dependency>
        <!-- imf mq依赖 end -->

        <dependency>
            <groupId>org.apache.activemq</groupId>
            <artifactId>activemq-client</artifactId>
            <version>5.15.9</version>
        </dependency>

        <dependency>
            <groupId>com.xmky.xmcaresip</groupId>
            <artifactId>xmcaresip-common</artifactId>
            <version>3.0.4-SNAPSHOT</version>
            <scope>system</scope>
            <systemPath>${basedir}/lib/xmcaresip-common-2.5.3-SNAPSHOT.jar</systemPath>
        </dependency>

        <dependency>
            <groupId>com.xmky.xmcaresip.framework</groupId>
            <artifactId>xmcaresip-framework</artifactId>
            <version>3.0.4-SNAPSHOT</version>
            <scope>system</scope>
            <systemPath>${basedir}/lib/xmcaresip-framework-2.5.3-SNAPSHOT.jar</systemPath>
        </dependency>

        <dependency>
            <groupId>com.rcu.ais</groupId>
            <artifactId>ais-interface</artifactId>
            <version>1.0</version>
            <scope>system</scope>
            <systemPath>${basedir}/lib/ais-interface.jar</systemPath>
        </dependency>

    </dependencies>

    <build>
        <finalName>hkjc-data-receive</finalName>
        <resources>
            <resource>
                <directory>src/main/resources</directory>
            </resource>
            <resource>
                <directory>${project.basedir}/lib</directory>
                <targetPath>${project.build.directory}/lib/</targetPath>
                <includes>
                    <include>**/*.jar</include>
                </includes>
            </resource>
        </resources>
        <plugins>
            <plugin>
                <groupId>org.apache.maven.plugins</groupId>
                <artifactId>maven-compiler-plugin</artifactId>
                <version>3.1</version>
                <configuration>
                    <source>1.8</source>
                    <target>1.8</target>
                </configuration>
            </plugin>
            <plugin>
                <groupId>org.apache.maven.plugins</groupId>
                <artifactId>maven-dependency-plugin</artifactId>
                <version>2.8</version>
                <executions>
                    <execution>
                        <id>copy-dependencies</id>
                        <phase>package</phase>
                        <goals>
                            <goal>copy-dependencies</goal>
                        </goals>
                        <configuration>
                            <outputDirectory>${project.build.directory}/lib</outputDirectory>
                        </configuration>
                    </execution>
                </executions>
            </plugin>
            <plugin>
                <groupId>org.apache.maven.plugins</groupId>
                <artifactId>maven-jar-plugin</artifactId>
                <version>2.4</version>
                <configuration>
                    <archive>
                        <addMavenDescriptor>false</addMavenDescriptor>
                        <manifest>
                            <useUniqueVersions>false</useUniqueVersions>
                            <addClasspath>true</addClasspath>
                            <classpathPrefix>lib/</classpathPrefix>
                            <mainClass>com.air.security.sodb.data.RunStartAll</mainClass>
                        </manifest>
                        <manifestEntries>
                            <Class-Path>lib/CL3Export.jar lib/CL3Nonexport.jar lib/com.ibm.mq.commonservices.jar lib/com.ibm.mq.headers.jar lib/com.ibm.mq.jar lib/com.ibm.mq.jmqi.jar lib/com.ibm.mq.jms.Nojndi.jar lib/com.ibm.mq.pcf.jar lib/com.ibm.mq.soap.jar lib/com.ibm.mq.tools.ras.jar lib/com.ibm.mqjms.jar lib/connector.jar lib/dhbcore.jar lib/fscontext.jar lib/Imf_2.0.jar lib/jms.jar lib/jndi.jar lib/jta.jar lib/junit.jar lib/ldap.jar lib/providerutil.jar lib/rmm.jar lib/xmcares-common-encrypt-3.0.3-SNAPSHOT.jar lib/xmcaresip-common-2.5.3-SNAPSHOT.jar lib/xmcaresip-framework-2.5.3-SNAPSHOT.jar lib/ais-interface.jar</Class-Path>
                        </manifestEntries>
                    </archive>
                </configuration>
            </plugin>
        </plugins>
    </build>

</project>