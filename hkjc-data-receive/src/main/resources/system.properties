#çº¿ç¨æ± å¤§å°
threadpool.count=20

#åºç¨å³é­çæ§ç«¯å£
bjjc.data.receive.stop.port=20007

#kafkaConsumer kafkaæ¶è´¹èéç½®æä»¶è·¯å¾
#kafka.consumer.config.path=D:\\xjjcDataRecv\\config\\kafkaConsumer.properties
kafka.consumer.config.path=/server/dtsServer/hkjcDataRecv/config/kafkaConsumer.properties

#kafkaProducer kafkaæ¶è´¹èéç½®æä»¶è·¯å¾
#kafka.producer.config.path=D:\\xjjcDataRecv\\config\\kafkaProducer.properties
kafka.producer.config.path=/server/dtsServer/hkjcDataRecv/config/kafkaProducer.properties

#è®°å½æ°æ®ä¼ è¾æ¥å¿çtopic
kafka.msg.transfer.log.topic=msgTranLog

#æµåªä½åéæ¥å£
video.status.service=false
video.rabbit.mq.queue=sodb.alarm
#video.rabbit.mq.uri=**************************************
video.rabbit.mq.uri=**************************************
video.rabiit.mq.exchange=cctv.notify
video.rabiit.mq.routeKey=cctv.notify.key

#ææ£æ°æ®æ¥å£
psgr.service=false
psgr.rabbit.mq.queue=Q_AFJC
#video.rabbit.mq.uri=amqp://test:test@10.30.35.24:5672
psgr.rabbit.mq.uri=amqp://test:test@10.30.35.24:5672
psgr.rabiit.mq.exchange=E_AFJC
psgr.rabiit.mq.exchangeType=direct
psgr.rabiit.mq.routeKey=E_AFJC
psgr.message.key=qweasdzx

#T2å´çç³»ç»activemqéç½®
t2.wall.service=true
t2.wall.active.mq.url=failover://tcp://172.18.2.5:61616
t2.wall.active.mq.username=admin
t2.wall.active.mq.pwd=admin
t2.wall.active.mq.ajQueue=topic.ais
t2.wall.active.mq.ajTopic=topic.ais

#å´çç³»ç»activemqéç½®
t1.wall.service=true
t1.wall.active.mq.url=failover://tcp://172.18.2.5:61616
t1.wall.active.mq.username=admin
t1.wall.active.mq.pwd=admin
t1.wall.active.mq.ajQueue=topic.ais
t1.wall.active.mq.ajTopic=topic.ais

#è½¦è¾ä½ç½®æ°æ®mqéç½®
car.service=false
car.active.mq.url=failover://tcp://172.19.255.9:61616
car.active.mq.username=admin
car.active.mq.pwd=admin
car.active.mq.ajQueue=locationVehicle.external 
car.active.mq.ajTopic=locationVehicle.external 

#è½¦è¾åºç¡æ°æ®è¯·æ±æ¥å£
car.basic.data.receive.url=http://10.30.3.4:8088/LocationWebservice/services/vehicleDataService?wsdl
car.basic.data.receive.method=getVehicles

flight.service=false

#sodbæµåªä½èµæºç¶æè¯·æ±æ¥å£
sodb.video.equ.status.req.url=http://172.28.6.125/api/bs/equ/info/open/getEquInfoByCode/EC01

#èªç­æ¶æ¯åå¸æå¡ä¸»é¢
kafka.flight.msg.topic=iSodbFlight