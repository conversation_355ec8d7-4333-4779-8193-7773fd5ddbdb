#æ¶æ¯æ ååå¤çServiceéç½®
#èªç­å¨ææ°æ®
FSS1=FlightDynMessageRecvFormatServiceImpl,iSodbFlight,FLIGHT_DYN,FLIGHT_DATA
FSS2=FlightDynMessageRecvFormatServiceImpl,iSodbFlight,FLIGHT_DYN,FLIGHT_DATA
#èªç­åºç¡æ°æ®
#æºåºæ°æ®
Airport=FlightBasicMessageRecvFormatServiceImpl,cContainerFlight,Airport,FLIGHT_BASIC
#å»¶è¯¯ä»£ç æ°æ®
DelayCode=FlightBasicMessageRecvFormatServiceImpl,cContainerFlight,DelayCode,FLIGHT_BASIC
#æºä½
Stand=FlightBasicMessageRecvFormatServiceImpl,cContainerFlight|cContainerEqu,Stand|JW_EQU,FLIGHT_BASIC|EQU
#ç»æºé¨
Gate=FlightBasicMessageRecvFormatServiceImpl,cContainerFlight|cContainerEqu,Gate|DJM_EQU,FLIGHT_BASIC|EQU

#ææ£æ°æ®
#å®æ£ç»ç«¯å¼æ¾
DCOP=ScimsMessageRecvFormatServiceImpl,cContainerPassenger,SCIMS_DEVICE_ON,EQU_STATUS
#å®æ£ç»ç«¯å³é­
DCCL=ScimsMessageRecvFormatServiceImpl,cContainerPassenger,SCIMS_DEVICE_OFF,EQU_STATUS
#æå®¢è¿æ£æ°æ®
PRSC=ScimsMessageRecvFormatServiceImpl,cContainerPassenger,PSGR_SECURITY,PSGR_DATA
#æå®¢éèº«è¡æå¼åæ°æ®
SBSC=ScimsMessageRecvFormatServiceImpl,cContainerPassenger,PSGR_BAG_OPEN,PSGR_DATA

#å´çæ¥è­¦
WallAlarm=MessageRecvFormatServiceImpl,cContainerAlarm,WALL_ALARM,ALARM
T1WallAlarm=MessageRecvFormatServiceImpl,cContainerAlarm,T1_WALL_ALARM,ALARM

#è½¦è¾ä½ç½®æ°æ®
CarLocation=MessageRecvFormatServiceImpl,cContainerLocation,CAR_LOCATION,CAR_DATA
CarBasicData=CarBasicDataMessageRecvFormatServiceImpl,cContainerCar,CAR_BASIC_DATA,CAR_DATA