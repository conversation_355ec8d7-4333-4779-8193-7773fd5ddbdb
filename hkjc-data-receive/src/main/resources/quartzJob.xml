<?xml version="1.0" encoding="UTF-8"?>
<!--定时任务配置文件： -->
<quartz>
	<job>
		<job-detail>
			<name>carBasicDataReceive-Job</name>
			<group>carBasicDataReceiveJobGroup</group>
			<description>车辆基础采集任务Job</description>
			<job-class>com.air.security.sodb.data.receive.job.CarBasicDataReceiveSchedulerJob</job-class>
		</job-detail>
		<trigger>
			<!-- JOB开关，on:开启 off:关闭 -->
			<switch>on</switch>
			<name>carBasicDataReceive-Job-trigger</name>
			<group>carBasicDataReceiveJobGroup</group>
			<!-- <cron-expression>0/60 * * * * ?</cron-expression> -->
			<cron-expression>0 0 */4 * * ?</cron-expression>
		</trigger>
	</job>

</quartz>