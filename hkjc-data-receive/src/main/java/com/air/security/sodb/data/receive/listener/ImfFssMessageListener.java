package com.air.security.sodb.data.receive.listener;

import com.air.security.sodb.data.core.base.Meta;
import com.air.security.sodb.data.core.constant.MsgIdConstant;
import com.air.security.sodb.data.core.util.EditMetaUtil;
import com.air.security.sodb.data.core.util.HaLog;
import com.air.security.sodb.data.core.util.XstreamUtil;
import com.air.security.sodb.data.ifdomain.flight.SysInfo;
import com.air.security.sodb.data.receive.server.MqMessageRecvServer;
import com.air.security.sodb.data.receive.server.MqMessageRecvServerImpl;
import com.tsystems.aviation.imf.client.message.ImfMessageListener;
import org.apache.commons.lang.StringUtils;
import org.dom4j.Document;
import org.dom4j.DocumentException;
import org.dom4j.DocumentHelper;
import org.dom4j.Node;
import org.slf4j.Logger;
import org.slf4j.LoggerFactory;

import java.text.ParseException;

/**
 * @Description: 消息的接收实现类
 * @author: mk
 * @Date: 2020月6月30号
 */
public class ImfFssMessageListener extends ImfMessageListener {

    private static final long serialVersionUID = 5186107590632918901L;

    private static final Logger log = LoggerFactory.getLogger(ImfFssMessageListener.class);

    private static final String XML_META_PATH = "IMFRoot/SysInfo";

    private static final String BASIC_DATA_CATEGORY_PATH = "IMFRoot/Data/PrimaryKey/BasicDataKey/BasicDataCategory";
    /**
     * 消息分发处理器
     */
    private MqMessageRecvServer server = new MqMessageRecvServerImpl();

    @Override
    public void handleMessage(String message) {

        HaLog.info(log, MsgIdConstant.MS_INF_0009, "航班信息接入开始", message);

        Document document = null;
        Node metaNode = null;
        try {
            document = DocumentHelper.parseText(message);
            metaNode = document.selectSingleNode(XML_META_PATH);
            if (null == metaNode) {
                return;
            }

            // 消息头标签
            SysInfo sysinfo = (SysInfo) XstreamUtil.fromXml(SysInfo.class, metaNode.asXML());

            if (sysinfo != null && StringUtils.isNotBlank(sysinfo.getOperationMode())
                    && !StringUtils.equals("SYS", sysinfo.getOperationMode())) {
                Meta meta = new Meta();
                EditMetaUtil.editMeta(sysinfo, meta);
                String bss = "BSS";
                if (sysinfo.getServiceType().startsWith(bss)) {
                    Node basicDataCategory = document.selectSingleNode(BASIC_DATA_CATEGORY_PATH);
                    meta.setEventType(basicDataCategory.getStringValue());
                }
                server.handle(meta, message);
                HaLog.info(log, MsgIdConstant.MS_INF_0009, "航班信息接入结束", message);
            }

        } catch (DocumentException | ParseException e) {
            e.printStackTrace();
        }

    }

}
