package com.air.security.sodb.data.receive.listener;

import com.air.security.sodb.data.core.base.Meta;
import com.air.security.sodb.data.core.constant.MsgIdConstant;
import com.air.security.sodb.data.core.spring.SpringUtil;
import com.air.security.sodb.data.core.util.HaLog;
import com.air.security.sodb.data.core.util.PropertyUtil;
import com.air.security.sodb.data.receive.server.MqMessageRecvServer;
import com.air.security.sodb.data.receive.server.MqMessageRecvServerImpl;
import com.alibaba.fastjson.JSONObject;
import com.rabbitmq.client.*;
import org.apache.commons.io.FileUtils;
import org.slf4j.Logger;
import org.slf4j.LoggerFactory;

import java.io.File;
import java.io.IOException;

/**
 * 流媒体mq主题消费-资源状态数据
 *
 * <AUTHOR>
 */
public class VideoRabbitMqConsumer {

    private static final Logger log = LoggerFactory.getLogger(VideoRabbitMqConsumer.class);

    private static String queneName = PropertyUtil.getProperty("video.rabbit.mq.queue");

    private static String uri = PropertyUtil.getProperty("video.rabbit.mq.uri");

    private static String routeKey = PropertyUtil.getProperty("video.rabiit.mq.routeKey");

    /**
     * 消息接收处理器
     */
    private static MqMessageRecvServer server = new MqMessageRecvServerImpl();

    public void receiveData() {
        String exchangeName = "cctv.notify";

        ConnectionFactory factory = new ConnectionFactory();
        try {
            factory.setUri(uri);
            Connection connection = factory.newConnection();
            Channel channel = connection.createChannel();

            // 声明交换机类型
            channel.exchangeDeclare(queneName, "direct", true);
            channel.queueBind(queneName, exchangeName, routeKey);

            Consumer consumer = new DefaultConsumer(channel) {
                @Override
                public void handleDelivery(String consumerTag, Envelope envelope, AMQP.BasicProperties properties,
                                           byte[] body) throws IOException {
                    String message = new String(body, "UTF-8");
                    JSONObject msgJson = JSONObject.parseObject(message);
                    String infoType = msgJson.getString("INFOTYPE");
                    Meta meta = new Meta();
                    meta.setEventType(infoType);
                    HaLog.info(log, MsgIdConstant.MS_INF_0009, "流媒体服务变更通知信息", message);
                }
            };

            channel.basicConsume(queneName, true, consumer);
        } catch (Exception e) {
            HaLog.error(log, MsgIdConstant.MS_ERR_0001, e, "");
        }
    }

    public static void main(String[] args) throws IOException {
        // 初始化spring
        SpringUtil.initSpring();
        String message = FileUtils.readFileToString(new File("E:\\testData\\OPEN_LOG.json"));
        JSONObject msgJson = JSONObject.parseObject(message);
        String infoType = msgJson.getString("INFOTYPE");
        Meta meta = new Meta();
        meta.setEventType(infoType);
        server.handle(meta, message);
        HaLog.info(log, MsgIdConstant.MS_INF_0009, "货检信息", message);
    }

}
