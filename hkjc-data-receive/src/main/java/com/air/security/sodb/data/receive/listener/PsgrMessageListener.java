package com.air.security.sodb.data.receive.listener;

import com.air.security.sodb.data.core.base.Meta;
import com.air.security.sodb.data.core.constant.MsgIdConstant;
import com.air.security.sodb.data.core.util.HaLog;
import com.air.security.sodb.data.core.util.PropertyUtil;
import com.air.security.sodb.data.receive.server.MqMessageRecvServer;
import com.air.security.sodb.data.receive.server.MqMessageRecvServerImpl;
import com.rabbitmq.client.*;
import org.dom4j.Document;
import org.dom4j.DocumentHelper;
import org.dom4j.Node;
import org.slf4j.Logger;
import org.slf4j.LoggerFactory;

import java.io.IOException;

/**
 * 旅检数据监听
 * <AUTHOR>
public class PsgrMessageListener {

    private static final Logger log = LoggerFactory.getLogger(PsgrMessageListener.class);

    private static String queneName = PropertyUtil.getProperty("psgr.rabbit.mq.queue");

    private static String uri = PropertyUtil.getProperty("psgr.rabbit.mq.uri");

    private static String routeKey = PropertyUtil.getProperty("psgr.rabiit.mq.routeKey");

    private static String exchange = PropertyUtil.getProperty("psgr.rabiit.mq.exchange");

    private static String exchangeType = PropertyUtil.getProperty("psgr.rabiit.mq.exchangeType");

    private static String aesKey = PropertyUtil.getProperty("psgr.message.key");

    /**
     * 消息接收处理器
     */
    private static MqMessageRecvServer server = new MqMessageRecvServerImpl();

    public void receiveData() {
        ConnectionFactory factory = new ConnectionFactory();
        try {
            HaLog.info(log, MsgIdConstant.MS_INF_0001, "创建旅检数据监听");
            factory.setUri(uri);
            Connection connection = factory.newConnection();
            Channel channel = connection.createChannel();

            // 声明交换机类型
            channel.exchangeDeclare(queneName, exchangeType, true);
            channel.queueBind(queneName, exchange, routeKey);
            HaLog.info(log, MsgIdConstant.MS_INF_0002, "创建旅检数据监听");
            Consumer consumer = new DefaultConsumer(channel) {
                @Override
                public void handleDelivery(String consumerTag, Envelope envelope, AMQP.BasicProperties properties,
                                           byte[] body) throws IOException {
                    String message = new String(body, "UTF-8");
                    try {
                        HaLog.info(log, MsgIdConstant.MS_INF_0009, "旅检原始数据", message);
                        Meta meta = new Meta();
                        Document document = DocumentHelper.parseText(message);
                        Node metaNode = document.selectSingleNode("Envelope/Header/MessageType");
                        meta.setEventType(metaNode.getStringValue());
                        server.handle(meta, message);
                        HaLog.info(log, MsgIdConstant.MS_INF_0009, "旅检数据", message);
                    } catch (Exception e) {
                        HaLog.error(log, e, MsgIdConstant.MS_ERR_0001, "");
                    }

                }
            };

            // channel绑定队列、消费者，autoAck为true表示一旦收到消息则自动回复确认消息
            channel.basicConsume(queneName, true, consumer);
        } catch (Exception e) {
            HaLog.error(log, e, MsgIdConstant.MS_ERR_0001, "");
        }
    }

}
