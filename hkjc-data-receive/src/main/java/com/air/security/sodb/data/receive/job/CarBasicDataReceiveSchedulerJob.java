package com.air.security.sodb.data.receive.job;

import com.air.security.sodb.data.core.base.Meta;
import org.apache.cxf.endpoint.Client;
import org.apache.cxf.jaxws.endpoint.dynamic.JaxWsDynamicClientFactory;
import org.quartz.JobDataMap;
import org.slf4j.Logger;
import org.slf4j.LoggerFactory;

import com.air.security.sodb.data.core.constant.MsgIdConstant;
import com.air.security.sodb.data.core.exception.SystemBaseException;
import com.air.security.sodb.data.core.quartz.AbstractJob;
import com.air.security.sodb.data.core.util.HaLog;
import com.air.security.sodb.data.core.util.PropertyUtil;
import com.air.security.sodb.data.receive.server.MqMessageRecvServer;
import com.air.security.sodb.data.receive.server.MqMessageRecvServerImpl;
import com.alibaba.fastjson.JSONObject;

/**
 * <AUTHOR>
 * @Description: 车辆基础数据接入
 */
public class CarBasicDataReceiveSchedulerJob extends AbstractJob {

    private static final Logger log = LoggerFactory.getLogger(CarBasicDataReceiveSchedulerJob.class);

    /**
     * 获取车辆基础-系统报警日志服务URI
     */
    private static String url = PropertyUtil.getProperty("car.basic.data.receive.url");

    private static String method = PropertyUtil.getProperty("car.basic.data.receive.method");

    private MqMessageRecvServer server = new MqMessageRecvServerImpl();

    @Override
    public void executeJob(JobDataMap paramMap) {
        HaLog.info(log, MsgIdConstant.MS_INF_0001, "获取车辆基础数据定时任务");

        try {

            Client client = JaxWsDynamicClientFactory.newInstance().createClient(url);
            String result = JSONObject.toJSONString(client.invoke(method)[0]);
            Meta meta = new Meta();
            meta.setEventType("CarBasicData");
            server.handle(meta, result);
        } catch (Exception e) {
            throw new SystemBaseException("获取大华告警信息失败", e);
        } finally {
            HaLog.info(log, MsgIdConstant.MS_INF_0002, "获取车辆基础数据定时任务");
        }

    }

    public static void main(String[] args) {
        CarBasicDataReceiveSchedulerJob job = new CarBasicDataReceiveSchedulerJob();
        job.executeJob(null);
    }

}
