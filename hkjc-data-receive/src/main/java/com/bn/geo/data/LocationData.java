package com.bn.geo.data;

import java.io.Serializable;
import java.util.Date;

/**
 * <p>Title: 博能位置数据服务器 - LocationData</p>
 *
 * <p>Description:
 * 位置数据对象，系统会根据位置数据类型，通过字符串构造出LocationData对象，并将这个对象发布到不同的AMQ的topic中
 * </p>
 *
 * <p>Copyright: Copyright bnkj(c) 2018</p>
 *
 * <p>Company: 北京博能科技股份有限公司</p>
 *
 * <AUTHOR>
 * @version 1.0
 */
public class LocationData implements Serializable {
    /**
     * serialVersionUID
     */
    private static final long serialVersionUID = 10000L;

    /**
     * 位置类型，01-航空器，02-车辆，03-施工人员,04-无动力设备,05-旅客。。。。。。
     */
    private String locationType;

    /**
     * 设备编号，如航班流水号、车辆编号以及其它定位设备编号
     */
    private String id;

    /**
     * 定位数据获取时间
     */
    private Date locationTime;

    /**
     * 定位数据x值
     */
    private double x;

    /**
     * 定位数据y值
     */
    private double y;

    /**
     * 定位数据地理坐标dx值
     */
    private double dx;

    /**
     * 定位数据地理坐标dy值
     */
    private double dy;

    /**
     * 定位数据z值
     */
    private double z;

    /**
     * 水平方向速度，通过vx和vy可以计算出speed和direct
     */
    private double vx;

    /**
     * 垂直方向速度，通过vx和vy可以计算出speed和direct
     */
    private double vy;

    /**
     * 当前速度
     */
    private double speed;

    /**
     * 当前方向
     */
    private double direct;

    /**
     * 定位数据的wkid
     */
    private int wkid;

    /**
     * 定位状态：通常定位状态为1的才有效(通过定位质量计算出来)
     */
    private int state;

    /**
     * 是否在线
     */
    private boolean online;

    /**
     * 数据提供方编号
     */
    private String provider;

    /**
     * 定位扩展属性(保留)
     */
    private String ext;

    public String getLocationType() {
        return locationType;
    }

    public void setLocationType(String locationType) {
        this.locationType = locationType;
    }

    public String getId() {
        return id;
    }

    public void setId(String id) {
        this.id = id;
    }

    public Date getLocationTime() {
        return locationTime;
    }

    public void setLocationTime(Date locationTime) {
        this.locationTime = locationTime;
    }

    public double getX() {
        return x;
    }

    public void setX(double x) {
        this.x = x;
    }

    public double getY() {
        return y;
    }

    public void setY(double y) {
        this.y = y;
    }

    public double getDx() {
        return dx;
    }

    public void setDx(double dx) {
        this.dx = dx;
    }

    public double getDy() {
        return dy;
    }

    public void setDy(double dy) {
        this.dy = dy;
    }

    public double getZ() {
        return z;
    }

    public void setZ(double z) {
        this.z = z;
    }

    public double getSpeed() {
        return speed;
    }

    public void setSpeed(double speed) {
        this.speed = speed;
    }

    public double getDirect() {
        return direct;
    }

    public void setDirect(double direct) {
        this.direct = direct;
    }

    public int getWkid() {
        return wkid;
    }

    public void setWkid(int wkid) {
        this.wkid = wkid;
    }

    public String getExt() {
        return ext;
    }

    public void setExt(String ext) {
        this.ext = ext;
    }

    public int getState() {
        return state;
    }

    public void setState(int state) {
        this.state = state;
    }

    public double getVx() {
        return vx;
    }

    public void setVx(double vx) {
        this.vx = vx;
    }

    public double getVy() {
        return vy;
    }

    public void setVy(double vy) {
        this.vy = vy;
    }

    public boolean isOnline() {
        return online;
    }

    public void setOnline(boolean online) {
        this.online = online;
    }

    public String getProvider() {
        return provider;
    }

    public void setProvider(String provider) {
        this.provider = provider;
    }

    @Override
    public String toString() {
        return "LocationData [locationType=" + locationType + ", id=" + id + ", locationTime=" + locationTime + ", x="
                + x + ", y=" + y + ", dx=" + dx + ", dy=" + dy + ", z=" + z + ", vx=" + vx + ", vy=" + vy + ", speed="
                + speed + ", direct=" + direct + ", wkid=" + wkid + ", state=" + state + ", online=" + online
                + ", provider=" + provider + ", ext=" + ext + "]";
    }


}
