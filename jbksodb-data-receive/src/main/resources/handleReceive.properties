#\u6D77\u5EB7\u624B\u52A8\u62A5\u8B66\u8D44\u6E90\u4FE1\u606F
hkhandEquInfo=MessageRecvFormatServiceImpl,cContainerEqu,HK_SD_EQU,EQU
hkhandEquState=MessageRecvFormatServiceImpl,cContainerEqu,HK_SD_EQU_STATUS_UE,EQU_STATUS
#\u6D77\u5EB7\u624B\u52A8\u62A5\u8B66\u8D44\u6E90\u4FE1\u606F
hkmjEquInfo=MessageRecvFormatServiceImpl,cContainerEqu,HK_MJ_EQU,EQU

#\u6D77\u5EB7\u624B\u52A8\u62A5\u8B66\u8D44\u6E90\u53D8\u66F4\u4FE1\u606F
hkmjEquState=MessageRecvFormatServiceImpl,cContainerEqu,HK_MJ_EQU_STATUS_UE,EQU_STATUS
#\u6D77\u5EB7\u5165\u4FB5\u62A5\u8B66\u4E8B\u4EF6\uFF08\u624B\u52A8\u62A5\u8B66\u62A5\u8B66\uFF09
handAlarmMessage=MessageRecvFormatServiceImpl,cContainerAlarm,HK_SD_ALARM,ALARM
HK_ANALYSIS_ALARM=MessageRecvFormatServiceImpl,cContainerAlarm,HK_ANALYSIS_ALARM,ALARM
#\u95E8\u7981\u62A5\u8B66\u548C\u95E8\u7981\u5237\u5361
mjAlarmMessage=MessageRecvFormatServiceImpl,cContainerAlarm|cContainerRecord,HK_MJ_ALARM|HK_MJ_RECORD_CARD,ALARM|RECORD_CARD
#\u65C5\u68C0\u6570\u636E
check=ScimsImgMessageRecvFormatServiceImpl,cContainerPassenger,PRSCJSON_SECURITY,PSGR_DATA
bag=ScimsImgMessageRecvFormatServiceImpl,cContainerPassenger,PSGR_OPEN_INFO,PSGR_DATA
flight=MessageRecvFormatServiceImpl,cContainerFlight,FLIGHT_DYN,FLIGHT_DATA
#\u8D27\u8FD0\u5B89\u68C0\u7CFB\u7EDF-\u8FD0\u5355\u57FA\u672C\u4FE1\u606F
WAY_BILL_LOG=MessageRecvFormatServiceImpl,cContainerCargo,WAY_BILL_LOG,CARGO
#\u8D27\u8FD0\u5B89\u68C0\u7CFB\u7EDF-\u8FD0\u5355\u5F00\u5305\u4FE1\u606F
OPEN_LOG=CargoOpenMessageRecvFormatServiceImpl,cContainerCargo,OPEN_LOG,CARGO
#\u8D27\u8FD0\u5B89\u68C0\u7CFB\u7EDF-\u8FD0\u5355\u5B89\u68C0\u4FE1\u606F
SECURITY_LOG=MessageRecvFormatServiceImpl,cContainerCargo,SECURITY_LOG,CARGO
#\u8D27\u8FD0\u5B89\u68C0\u7CFB\u7EDF-\u5B89\u68C0\u901A\u9053\u7684\u5F00\u653E\u4FE1\u606F
CHECKSTATUS_EQU=MessageRecvFormatServiceImpl,cContainerCargo,CHECKSTATUS_EQU,CARGO
#\u9053\u53E3\u8FC7\u8F66\u6570\u636E
crossingCarPass=MessageRecvFormatServiceImpl,cContainerCar,CROSSING_CAR_PASS_INFO,CAR_DATA
#\u9053\u53E3\u8BBE\u5907\u53D8\u66F4
crossEquState=MessageRecvFormatServiceImpl,cContainerEqu,CROSSING_EQU_STATUS_UE,EQU_STATUS
HK_CAR_INFO=MessageRecvFormatServiceImpl,cContainerCar,HK_CAR_INFO,CAR
CAR_EQU_INFO=MessageRecvFormatServiceImpl,cContainerEqu,CAR_EQU_INFO,EQU
HK_CAR_WHITE_INFO=MessageRecvFormatServiceImpl,cContainerCar,HK_CAR_WHITE_INFO,CAR
HK_CAR_BLACK_INFO=MessageRecvFormatServiceImpl,cContainerCar,HK_CAR_BLACK_INFO,CAR
HK_CAR_ALARM=MessageRecvFormatServiceImpl,cContainerAlarm,HK_CAR_ALARM,ALARM

