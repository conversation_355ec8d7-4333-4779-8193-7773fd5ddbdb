<beans xmlns="http://www.springframework.org/schema/beans"
       xmlns:xsi="http://www.w3.org/2001/XMLSchema-instance" xmlns:mvc="http://www.springframework.org/schema/mvc"
       xmlns:context="http://www.springframework.org/schema/context"
       xsi:schemaLocation="http://www.springframework.org/schema/beans http://www.springframework.org/schema/beans/spring-beans-4.0.xsd http://www.springframework.org/schema/mvc http://www.springframework.org/schema/mvc/spring-mvc.xsd http://www.springframework.org/schema/context http://www.springframework.org/schema/context/spring-context.xsd">

    <import resource="classpath*:spring-*.xml"/>

    <context:annotation-config/>
    <context:component-scan base-package="com.air.security.sodb"/>
    <context:property-placeholder location="classpath*:*.properties" file-encoding="UTF-8" ignore-unresolvable="true"/>

    <!-- <context:annotation-config/>
    <context:component-scan base-package="com.air.security.sodb"/> -->

    <mvc:annotation-driven>
        <mvc:message-converters register-defaults="true">
            <!-- StringHttpMessageConverter编码为UTF-8，防止乱码 -->
            <bean class="org.springframework.http.converter.StringHttpMessageConverter">
                <constructor-arg value="UTF-8"/>
                <property name="supportedMediaTypes">
                    <list>
                        <bean class="org.springframework.http.MediaType">
                            <constructor-arg index="0" value="text"/>
                            <constructor-arg index="1" value="plain"/>
                            <constructor-arg index="2" value="UTF-8"/>
                        </bean>
                        <bean class="org.springframework.http.MediaType">
                            <constructor-arg index="0" value="*"/>
                            <constructor-arg index="1" value="*"/>
                            <constructor-arg index="2" value="UTF-8"/>
                        </bean>
                    </list>
                </property>
            </bean>
            <!-- 避免IE执行AJAX时,返回JSON出现下载文件 -->
            <bean id="fastJsonHttpMessageConverter"
                  class="com.alibaba.fastjson.support.spring.FastJsonHttpMessageConverter">
                <property name="supportedMediaTypes">
                    <list>
                        <value>text/html; charset=UTF-8</value>
                        <value>application/json;charset=UTF-8</value>
                    </list>
                </property>
                <property name="fastJsonConfig" ref="fastJsonConfig"/>
            </bean>
        </mvc:message-converters>
    </mvc:annotation-driven>

    <bean id="fastJsonConfig" class="com.alibaba.fastjson.support.config.FastJsonConfig">
        <property name="charset" value="UTF-8"/>
        <property name="serializerFeatures">
            <list>
                <value>DisableCircularReferenceDetect</value><!-- 解决json ref 问题 -->
                <value>WriteMapNullValue</value>  <!-- 输出null值 -->
                <value>QuoteFieldNames</value><!-- key 使用双引号 -->
                <value>WriteNullListAsEmpty</value><!-- list为null时转成[] -->
                <value>WriteNullStringAsEmpty</value><!-- 字符串为null时转成“” -->
                <value>WriteNullNumberAsZero</value><!-- 数值字段如果为null,输出为0,而非null -->
            </list>
        </property>
    </bean>
    <bean id="DisableCircularReferenceDetect"
          class="org.springframework.beans.factory.config.FieldRetrievingFactoryBean">
        <property name="staticField"
                  value="com.alibaba.fastjson.serializer.SerializerFeature.DisableCircularReferenceDetect"></property>
    </bean>

    <bean id="restTemplate" class="org.springframework.web.client.RestTemplate"/>

    <!-- redis config-->
    <bean id="jedisPoolConfig" class="redis.clients.jedis.JedisPoolConfig">
        <property name="maxIdle" value="${redis.maxIdle}"/>
        <property name="maxTotal" value="${redis.maxTotal}"/>
        <property name="maxWaitMillis" value="${redis.maxWaitMillis}"/>
        <property name="testOnBorrow" value="${redis.testOnBorrow}"/>
    </bean>

    <bean id="connectionFactory" class="org.springframework.data.redis.connection.jedis.JedisConnectionFactory">
        <property name="poolConfig" ref="jedisPoolConfig"/>
        <property name="hostName" value="${redis.host}"/>
        <property name="port" value="${redis.port}"/>
        <property name="password" value="${redis.pass}"/>
        <property name="timeout" value="${redis.timeout}"/>
    </bean>

    <bean id="redisTemplate" class="org.springframework.data.redis.core.RedisTemplate">
        <property name="connectionFactory" ref="connectionFactory"/>
    </bean>

    <!--初始化appliationUtil类，并完成ApplicationContext的注入-->
    <bean id="applicationContextUtil" class="com.air.security.sodb.data.core.spring.ApplicationContextUtil"></bean>

</beans>