package com.air.security.sodb.dts.srvc.restsrvc.platform.demo.form;

import java.util.Date;

import com.air.security.sodb.data.core.annotation.EntityField;
import com.air.security.sodb.data.core.annotation.TableEntity;
import com.air.security.sodb.dts.srvc.domain.entity.DemoEntity;

/**
 * <AUTHOR>
 */
@TableEntity(target = DemoEntity.class)
public class DemoForm {

    @EntityField
    private String uuid;

    @EntityField
    private String name;

    @EntityField
    private String password;
    @EntityField
    private Date birth;
    @EntityField
    private String gender;
    @EntityField
    private String nickName;
    @EntityField
    private String createId;
    @EntityField
    private String isDelete;

    public String getUuid() {
        return uuid;
    }

    public void setUuid(String uuid) {
        this.uuid = uuid;
    }

    public String getName() {
        return name;
    }

    public void setName(String name) {
        this.name = name;
    }

    public String getPassword() {
        return password;
    }

    public void setPassword(String password) {
        this.password = password;
    }

    public Date getBirth() {
        return birth;
    }

    public void setBirth(Date birth) {
        this.birth = birth;
    }

    public String getGender() {
        return gender;
    }

    public void setGender(String gender) {
        this.gender = gender;
    }

    public String getNickName() {
        return nickName;
    }

    public void setNickName(String nickName) {
        this.nickName = nickName;
    }

    public String getCreateId() {
        return createId;
    }

    public void setCreateId(String createId) {
        this.createId = createId;
    }

    public String getIsDelete() {
        return isDelete;
    }

    public void setIsDelete(String isDelete) {
        this.isDelete = isDelete;
    }

}
