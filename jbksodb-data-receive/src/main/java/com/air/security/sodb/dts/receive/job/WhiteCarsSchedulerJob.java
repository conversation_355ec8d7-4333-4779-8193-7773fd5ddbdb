package com.air.security.sodb.dts.receive.job;

import com.air.security.sodb.data.core.base.Meta;
import com.air.security.sodb.data.core.constant.MsgIdConstant;
import com.air.security.sodb.data.core.exception.SystemBaseException;
import com.air.security.sodb.data.core.quartz.AbstractJob;
import com.air.security.sodb.data.core.util.HaLog;
import com.air.security.sodb.data.core.util.UuidUtil;
import com.air.security.sodb.dts.receive.server.MqMessageRecvServer;
import com.air.security.sodb.dts.receive.server.MqMessageRecvServerImpl;
import com.air.security.sodb.dts.receive.util.CertificationInfoUtil;
import com.air.security.sodb.dts.receive.util.PageInfo;
import com.air.security.sodb.dts.receive.util.SrvcHelper;
import com.alibaba.fastjson.JSONArray;
import com.alibaba.fastjson.JSONObject;
import org.quartz.JobDataMap;
import org.slf4j.Logger;
import org.slf4j.LoggerFactory;

import java.io.IOException;

/**
 * 车辆-白名单信息获取任务
 *
 * <AUTHOR>
 * @date 2018-07-10
 */
public class WhiteCarsSchedulerJob extends AbstractJob {

    private static final Logger log = LoggerFactory.getLogger(WhiteCarsSchedulerJob.class);

    private static final String URL = "/api/pms/v1/car/charge/page";
    /**
     *
     */
    private MqMessageRecvServer server = new MqMessageRecvServerImpl();

    @Override
    public void executeJob(JobDataMap paramMap) {
        HaLog.info(log, MsgIdConstant.MS_INF_0001, "获取车辆-白名单信息定时任务");

        try {
            execPost(URL, SrvcHelper.PAGE_SIZE, 1);
        } catch (Exception e) {
            throw new SystemBaseException("获取车辆-白名单信息定时任务", e);
        } finally {
            HaLog.info(log, MsgIdConstant.MS_INF_0002, "获取车辆-白名单信息定时任务");
        }

    }
    /**
     * @param url
     * @param pageSize
     * @param pageNo
     * @throws IOException
     */
    private void execPost(String url, int pageSize, int pageNo) throws IOException {
        JSONObject jsonObject = SrvcHelper.getPageParam(pageNo, pageSize);
        String messageBody = CertificationInfoUtil.send(url, jsonObject.toJSONString());

        // 获取分页数据
        PageInfo page = SrvcHelper.getPageInfo(messageBody);
        if (page.getTotalRows() > 0) {
            JSONObject json = JSONObject.parseObject(messageBody);
            JSONArray jsonJsonArray = json.getJSONObject("data").getJSONArray("list");
            Meta meta = new Meta();
            meta.setEventType("HK_CAR_WHITE_INFO");
            meta.setRecvSequence(UuidUtil.getUuid32());
            server.handle(meta, jsonJsonArray.toString());

            if (page.hasNextPage()) {
                // 查询下一页
                this.execPost(url, pageSize, page.getCurrentPage() + 1);
            }
        }
    }

}
