package com.air.security.sodb.dts.receive.server;

import com.air.security.sodb.data.core.base.Meta;
import com.air.security.sodb.data.core.exception.SystemBaseException;

/**
 * @Description: 消息数据的分发处理接口
 * @author: zhangc
 * @Date: 2018年12月5日
 */
public interface MqMessageRecvServer {

    /**
     * 消息分发处理
     *
     * @param meta
     * @param messageBody
     * @throws SystemBaseException
     */
    void handle(Meta meta, String messageBody) throws SystemBaseException;
}
