package com.air.security.sodb.dts.srvc.restsrvc.receive.car;

import com.air.security.sodb.data.core.base.Meta;
import com.air.security.sodb.data.core.constant.MsgIdConstant;
import com.air.security.sodb.data.core.util.HaLog;
import com.air.security.sodb.data.core.util.RequestUtil;
import com.air.security.sodb.data.core.util.UuidUtil;
import com.air.security.sodb.dts.receive.server.MqMessageRecvServer;
import com.air.security.sodb.dts.receive.server.MqMessageRecvServerImpl;
import com.alibaba.fastjson.JSONArray;
import com.alibaba.fastjson.JSONObject;
import org.slf4j.Logger;
import org.slf4j.LoggerFactory;
import org.springframework.web.bind.annotation.CrossOrigin;
import org.springframework.web.bind.annotation.RequestMapping;
import org.springframework.web.bind.annotation.RequestMethod;
import org.springframework.web.bind.annotation.RestController;

import javax.servlet.http.HttpServletRequest;
import javax.servlet.http.HttpServletResponse;

/**
 * @Description: 海康事件（门禁报警报警）
 * @author: mk
 * @Date: 2020-11-11
 */
@CrossOrigin
@RestController
@RequestMapping("/api/release/carInfoMessage")
public class HkCarInfoMessageSaveRestController {

    private static final Logger log = LoggerFactory.getLogger(HkCarInfoMessageSaveRestController.class);


    /**
     *
     */
    private MqMessageRecvServer server = new MqMessageRecvServerImpl();
    private static final Logger log1 = LoggerFactory.getLogger("msgTranLog");
    /**
     * 接口
     */
    @RequestMapping(value = "/execute", method = RequestMethod.POST)
    public void execute(HttpServletRequest request, HttpServletResponse response) {
        HaLog.info(log, MsgIdConstant.MS_INF_0001, "海康停车场进出报警接入接口");
        JSONObject jsonObj = RequestUtil.requestGetJson(request);
        HaLog.infoJson(log1, "海康停车场进出报警接入初始化数据》》》》》》》》"+JSONObject.toJSONString(jsonObj));
        try {
            // 执行业务处理
            if (null != jsonObj) {
                // 执行业务处理
                this.execute(jsonObj);
            }
            HaLog.info(log, MsgIdConstant.MS_INF_0002, "门禁报警报警接入接口");
        } catch (Exception e) {
            HaLog.error(log, e, MsgIdConstant.MS_ERR_0001);
        }

    }

    /**
     * 执行业务处理
     *
     * @param jsonObj
     */
    private void execute(JSONObject jsonObj) {
        JSONArray events = jsonObj.getJSONObject("params").getJSONArray("events");
        for (Object o : events) {
            JSONObject jsonObject = (JSONObject) o;
            JSONObject data = jsonObject.getJSONObject("data");
            if (data == null) {
                continue;
            }
            JSONObject picUrl = data.getJSONObject("picUrl");
            if (picUrl!=null){
                String vehiclePicUrl = picUrl.getString("vehiclePicUrl");
                data.put("vehiclePicUrl", "https://10.8.25.10/"+vehiclePicUrl);
            }
            Meta meta = new Meta();
            meta.setEventType("HK_CAR_INFO");
            meta.setRecvSequence(UuidUtil.getUuid32());
            server.handle(meta, data.toString());
        }
    }
}
