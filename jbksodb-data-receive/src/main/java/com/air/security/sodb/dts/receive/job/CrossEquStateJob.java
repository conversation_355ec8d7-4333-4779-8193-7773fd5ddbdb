package com.air.security.sodb.dts.receive.job;

import com.air.security.sodb.data.core.base.Meta;
import com.air.security.sodb.data.core.constant.GlobalCodeConstant;
import com.air.security.sodb.data.core.constant.MsgIdConstant;
import com.air.security.sodb.data.core.exception.SystemBaseException;
import com.air.security.sodb.data.core.quartz.AbstractJob;
import com.air.security.sodb.data.core.util.DateTimeUtil;
import com.air.security.sodb.data.core.util.HaLog;
import com.air.security.sodb.data.core.util.PropertyUtil;
import com.air.security.sodb.data.core.util.UuidUtil;
import com.air.security.sodb.dts.receive.server.MqMessageRecvServer;
import com.air.security.sodb.dts.receive.server.MqMessageRecvServerImpl;
import com.air.security.sodb.dts.receive.util.HkEquStatus;
import com.air.security.sodb.dts.receive.util.HkEquStatusCache;
import com.alibaba.fastjson.JSONObject;
import org.apache.commons.lang3.StringUtils;
import org.quartz.JobDataMap;
import org.slf4j.Logger;
import org.slf4j.LoggerFactory;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.web.client.RestTemplate;

import java.io.IOException;
import java.util.HashMap;
import java.util.List;
import java.util.Map;

/**
 * 道口资源变更信息获取任务
 *
 * <AUTHOR>
 */
public class CrossEquStateJob extends AbstractJob {

    private static final Logger log = LoggerFactory.getLogger(CrossEquStateJob.class);

    private static String crossEquState = PropertyUtil.getProperty("crossEquState.service.code");

    private static String url = PropertyUtil.getProperty("crossEquState.url");

    private static String username = PropertyUtil.getProperty("username");

    private static String password = PropertyUtil.getProperty("password");

    @Autowired
    private RestTemplate restTemplate;

    /**
     *
     */
    private MqMessageRecvServer server = new MqMessageRecvServerImpl();

    @Override
    public void executeJob(JobDataMap paramMap) {
        HaLog.info(log, MsgIdConstant.MS_INF_0001, "获取道口资源变更信息定时任务");

        try {
            execGet();
        } catch (Exception e) {
            throw new SystemBaseException("获取道口资源变更信息失败", e);
        } finally {
            HaLog.info(log, MsgIdConstant.MS_INF_0002, "获取道口资源变更信息定时任务");
        }

    }

    /**
     * @throws IOException
     */
    private void execGet() {

        //查询数据库中的资源编码和状态
        List<HkEquStatus> cacheList = HkEquStatusCache.getmjCacheList();
        for (int i = 0; i < cacheList.size(); i++) {
            HkEquStatus hkEquStatus = cacheList.get(i);
            //资源编码
            String equCode = hkEquStatus.getEquCode();
            //资源状态
            String timeStateId = hkEquStatus.getTimeStateId();
            String url1=url+"username={username}&password={password}&sbbm={sbbm}";
            Map<String, Object> params = new HashMap<>();
            params.put("username", username);
            params.put("password", password);
            params.put("sbbm", equCode);
            JSONObject result = restTemplate.getForObject(url1, JSONObject.class, params);

            String resultmsg = result.getString("resultmsg");
            String status = result.getString("status");

            if (!"OK".equals(resultmsg)){
                continue;
            }

            //资源编码转换
            status = formatStatus(status);
            if (!StringUtils.equals(timeStateId, status)) {
                HkEquStatusCache.getmjCacheList().get(i).setTimeStateId(status);
                JSONObject json = new JSONObject();
                json.put("operateTime", DateTimeUtil.getCurrentTimestampStr("yyyy-MM-dd HH:mm:ss"));
                json.put("timeStateId", status);
                //编辑消息头
                Meta meta = new Meta();
                meta.setEventType(crossEquState);
                meta.setRecvSequence(UuidUtil.getUuid32());
                server.handle(meta, json.toString());
            }
        }
    }

    /**
     * 资源编码转换
     *
     * @param onlineStatus 状态
     * @return
     */
    private String formatStatus(String onlineStatus) {
        if (GlobalCodeConstant.ZERO.equalsIgnoreCase(onlineStatus)) {
            onlineStatus = "ES01";
        }
        if (GlobalCodeConstant.ONE.equalsIgnoreCase(onlineStatus)) {
            onlineStatus = "ES01";
        }
        if (GlobalCodeConstant.TWO.equalsIgnoreCase(onlineStatus)) {
            onlineStatus = "ES01";
        }
        return onlineStatus;
    }


}
