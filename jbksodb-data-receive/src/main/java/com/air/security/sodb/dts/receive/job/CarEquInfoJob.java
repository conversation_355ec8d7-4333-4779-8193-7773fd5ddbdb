package com.air.security.sodb.dts.receive.job;

import com.air.security.sodb.data.core.base.Meta;
import com.air.security.sodb.data.core.quartz.AbstractJob;
import com.air.security.sodb.data.core.util.HaLog;
import com.air.security.sodb.dts.receive.server.MqMessageRecvServer;
import com.air.security.sodb.dts.receive.server.MqMessageRecvServerImpl;
import com.air.security.sodb.dts.receive.util.CertificationInfoUtil;
import com.alibaba.fastjson.JSON;
import com.alibaba.fastjson.JSONArray;
import com.alibaba.fastjson.JSONObject;
import org.quartz.JobDataMap;
import org.slf4j.Logger;
import org.slf4j.LoggerFactory;

import java.util.stream.Collectors;

public class CarEquInfoJob extends AbstractJob {
    private static final Logger log = LoggerFactory.getLogger(CarEquInfoJob.class);

    private static MqMessageRecvServer server = new MqMessageRecvServerImpl();

    private static final String PARK_LIST_URL = "/api/resource/v1/park/parkList";
    private static final String ENTRANCE_LIST_URL = "/api/resource/v1/entrance/entranceList";
    private static final String ROADWAY_LIST_URL = "/api/resource/v1/roadway/roadwayList";

    @Override
    public void executeJob(JobDataMap paramMap) {
        HaLog.info(log, "------------------CarEquInfoJob 开始-----------------------");
        try {
            getAllCars();
        } catch (Exception e) {
            log.error("Error during CarEquInfoJob execution: ", e);
        }
        HaLog.info(log, "------------------CarEquInfoJob 结束-----------------------");
    }

    /**
     * 获取所有车辆信息
     */
    private void getAllCars() {
        JSONObject json = new JSONObject();
        // 发送请求获取停车场列表的JSON响应
        String parkListResponseJson = CertificationInfoUtil.send(PARK_LIST_URL, json.toJSONString());
        // 将响应JSON解析为JSONObject对象
        JSONObject parkJson = JSONObject.parseObject(parkListResponseJson);
        // 获取停车场数据列表
        JSONArray parkData = parkJson.getJSONArray("data");
        // 如果停车场数据为空或为空列表，直接返回
        if (parkData == null || parkData.isEmpty()) {
            return;
        }

        // 将停车场指数代码拼接为逗号分隔的字符串
        String parkIndexCodes = parkData.stream().map(obj -> ((JSONObject) obj).getString("parkIndexCode")).collect(Collectors.joining(","));

        // 构建请求停车场入口列表的JSON对象
        JSONObject entranceRequest = new JSONObject();
        entranceRequest.put("parkIndexCodes", parkIndexCodes);
        // 发送请求获取停车场入口列表的JSON响应
        String entranceListResponseJson = CertificationInfoUtil.send(ENTRANCE_LIST_URL, entranceRequest.toJSONString());
        // 将响应JSON解析为JSONObject对象
        JSONObject entranceJson = JSONObject.parseObject(entranceListResponseJson);
        // 获取停车场入口数据列表
        JSONArray entranceData = entranceJson.getJSONArray("data");
        // 如果停车场入口数据为空或为空列表，直接返回
        if (entranceData == null || entranceData.isEmpty()) {
            return;
        }

        // 将停车场入口指数代码拼接为逗号分隔的字符串
        String entranceIndexCodes = entranceData.stream().map(obj -> ((JSONObject) obj).getString("entranceIndexCode")).collect(Collectors.joining(","));

        // 构建请求道路列表的JSON对象
        JSONObject roadwayRequest = new JSONObject();
        roadwayRequest.put("entranceIndexCodes", entranceIndexCodes);
        // 发送请求获取道路列表的JSON响应
        String roadwayListResponseJson = CertificationInfoUtil.send(ROADWAY_LIST_URL, roadwayRequest.toJSONString());
        // 将响应JSON解析为JSONObject对象
        JSONObject roadwayJson = JSONObject.parseObject(roadwayListResponseJson);
        // 获取道路数据列表
        JSONArray roadwayData = roadwayJson.getJSONArray("data");
        // 如果道路数据为空或为空列表，直接返回
        if (roadwayData == null || roadwayData.isEmpty()) {
            return;
        }

        // 创建事件元数据对象
        Meta meta = new Meta();
        // 设置事件类型为CAR_EQU_INFO
        meta.setEventType("CAR_EQU_INFO");
        // 调用server处理方法处理道路数据并发送到客户端
        server.handle(meta, JSON.toJSONString(roadwayData));
    }

}
