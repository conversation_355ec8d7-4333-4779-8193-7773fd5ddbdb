package com.air.security.sodb.dts.receive.service;

import com.alibaba.fastjson.JSONObject;

/**
 * @Description: 消息处理接口的抽象类
 * @author: zhangc
 * @Date: 2018年12月6日
 */
public abstract class AbstractMessageRecvService implements MessageRecvService {

    /**
     * JSON消息消息
     */
    private JSONObject sendMessage;

    /**
     * @return JSON消息消息
     */
    @Override
    public synchronized JSONObject pollSendMessage() {
        return this.sendMessage;
    }

    /**
     * @param JSON消息消息
     */
    @Override
    public synchronized void putSendMessage(JSONObject sendMessage) {
        this.sendMessage = sendMessage;
    }

}
