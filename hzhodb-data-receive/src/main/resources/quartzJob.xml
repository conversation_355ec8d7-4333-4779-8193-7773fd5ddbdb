<?xml version="1.0" encoding="UTF-8"?>
<!--定时任务配置文件： -->
<quartz>
    <job>
        <job-detail>
            <name>weatherMessage-Job</name>
            <group>weatherMessageGroup</group>
            <description>获取天气数据采集定时任务Job</description>
            <job-class>com.air.security.sodb.dts.receive.job.WeatherMessageJob</job-class>
        </job-detail>
        <trigger>
            <!-- JOB开关，ON:开启 OFF:关闭 -->
            <switch>off</switch>
            <name>weatherMessage-Job-trigger</name>
            <group>weatherMessageGroup</group>
            <cron-expression>0 0/5 * * * ?</cron-expression>
        </trigger>
    </job>
    <job>
        <job-detail>
            <name>pmsHbklMessage-Job</name>
            <group>pmsHbklMessageGroup</group>
            <description>获取航班客流数据定时任务Job</description>
            <job-class>com.air.security.sodb.dts.receive.job.PmsHbklMessageJob</job-class>
        </job-detail>
        <trigger>
            <!-- JOB开关，ON:开启 on:关闭 -->
            <switch>off</switch>
            <name>pmsHbklMessage-Job-trigger</name>
            <group>pmsHbklMessageGroup</group>
            <cron-expression>0 15 10 * * ?</cron-expression>
        </trigger>
    </job>
</quartz>