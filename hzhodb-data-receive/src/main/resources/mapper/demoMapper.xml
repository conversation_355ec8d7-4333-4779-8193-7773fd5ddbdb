<?xml version="1.0" encoding="UTF-8"?>
<!DOCTYPE mapper PUBLIC "-//mybatis.org//DTD Mapper 3.0//EN" "http://mybatis.org/dtd/mybatis-3-mapper.dtd" >
<mapper namespace="demo">

    <select id="getById" parameterType="java.util.Map"
            resultType="com.air.security.sodb.dts.srvc.restsrvc.platform.demo.dto.DemoDto">
        select
        UUID,
        NAME,
        PASSWORD,
        BIRTH,
        GENDER,
        NICK_NAME nickName,
        CREATE_TIME createTime,
        CREATE_ID createId,
        IS_DELETE isDelete,
        DELETE_TIME deleteTime
        from dts_demo
        <where>
            uuid = #{uuid}
        </where>
    </select>

    <select id="getList" parameterType="java.util.Map"
            resultType="com.air.security.sodb.dts.srvc.restsrvc.platform.demo.dto.DemoDto">
        select
        UUID,
        NAME,
        PASSWORD,
        BIRTH,
        GENDER,
        NICK_NAME nickName,
        CREATE_TIME createTime,
        CREATE_ID createId,
        IS_DELETE isDelete,
        DELETE_TIME deleteTime
        from dts_demo
        <where>
            <if test="name != null">and name like #{name}</if>
            <if test="nickName != null">and nick_name like #{nickName}</if>
        </where>
    </select>

</mapper>