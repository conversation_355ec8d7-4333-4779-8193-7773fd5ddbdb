#\u7EC4\u7EC7\u673A\u6784\u4FE1\u606F
BMFS=MessageOrganizationFormatServiceImpl,cContainerErp,ORGANIZATION_ERP,ERP
#\u4EBA\u5458\u4FE1\u606F
YGTB=MessagePersonnelFormatServiceImpl,cContainerErp,PERSONNEL_ERP,ERP
#\u5929\u6C14\u4FE1\u606F\u6570\u636E
TOPIC_HQ2SYS_WEATHER=MessageRecvFormatServiceImpl,cContainerWeather,WEATHER_DYN,WEATHER_DATA
#\u822A\u73ED\u5BA2\u6D41\u6570\u636E
pms_hbkl_info=MessageRecvFormatServiceImpl,cContainerFlight,PMS_HBKL_INFO,FLIGHT_DATA
#\u5F53\u524D\u51FA\u79DF\u8F66\u84C4\u8F66\u573A\u5269\u4F59\u8F66\u4F4D\u6570\u91CF
CZC_SYCW=MessageRecvFormatServiceImpl,cContainerPark,CZC_SYCW,PARK
#\u5F53\u524D\u51FA\u79DF\u8F66\u84C4\u8F66\u573A\u5185\u8F66\u8F86\u6570\u91CF
CZC_CLS=MessageRecvFormatServiceImpl,cContainerPark,CZC_CLS,PARK
#\u51FA\u79DF\u8F66\u84C4\u8F66\u573A\u8FC7\u8F66\u6570\u636E
CZC_GCSJ=MessageRecvFormatServiceImpl,cContainerPassCar,CZC_GCSJ,PARK
#\u83B7\u53D6\u505C\u8F66\u573A\u8FC7\u8F66\u8BB0\u5F55
PMS_GCJL=MessageRecvFormatServiceImpl,cContainerPassCar,PMS_GCJL,PARK
#\u83B7\u53D6\u84C4\u8F66\u573A\u57FA\u7840\u6570\u636E
XCQ_BASE=MessageRecvFormatServiceImpl,cContainerPark,XCQ_BASE,PARK
#\u83B7\u53D6\u505C\u8F66\u573A\u57FA\u7840\u6570\u636E
TCL_BASE=MessageRecvFormatServiceImpl,cContainerPark,TCL_BASE,PARK
#\u83B7\u53D6\u505C\u8F66\u573A\u5269\u4F59\u8F66\u4F4D\u6570
TCL_CWS=MessageRecvFormatServiceImpl,cContainerPark,TCL_CWS,PARK
#\u83B7\u53D6\u505C\u8F66\u573A\u51FA\u5165\u53E3\u6D41\u91CF
TCL_LL=MessageRecvFormatServiceImpl,cContainerPark,TCL_LL,PARK
#\u83B7\u53D6\u5730\u94C1\u6570\u636E
SUBWAY_PASSENGER_FLOW=MessageRecvFormatServiceImpl,cContainerSubway,SUBWAY_PASSENGER_FLOW,SUBWAY
#\u83B7\u53D6\u9053\u95F8\u6570\u636E
VEHICLE_RECORD=MessageRecvFormatServiceImpl,cContainerPark,VEHICLE_RECORD,PARK
#\u957F\u9014\u5927\u5DF4\u8F66\u552E\u7968\u4FE1\u606F\u6570\u636E
TOPIC_HQ2SYS_SPXX=MessageRecvFormatServiceImpl,iSodbPark,TOPIC_HQ2SYS_SPXX,PARK
#\u957F\u9014\u5927\u5DF4\u8F66\u73ED\u6B21\u4FE1\u606F\u6570\u636E
TOPIC_HQ2SYS_BCXX=MessageRecvFormatServiceImpl,iSodbPark,TOPIC_HQ2SYS_BCXX,PARK
flight=FightMessageRecvFormatServiceImpl,cContainerFlight,FLIGHT_DYN,FLIGHT_DATA
#\u62A5\u4FEE\u5B8C\u6210
TOPIC_HQ2ZHJT_BX=MaintenanceRecvFormatServiceImpl,cContainerMaintenance,TOPIC_HQ2ZHJT_BX,REPAIR

PARK_LOT_LIST=MessageRecvFormatServiceImpl,cContainerPark,PARK_LOT_LIST,PARK
PARKING_HIS_RECORD=MessageRecvFormatServiceImpl,cContainerPark,PARKING_HIS_RECORD,PARK
PRE_VEHICLE_STATISTICS=MessageRecvFormatServiceImpl,cContainerPark,PRE_VEHICLE_STATISTICS,PARK
SD_VEHICLE_PASS_INFO=MessageRecvFormatServiceImpl,cContainerPark,SD_VEHICLE_PASS_INFO,PARK
VEH_CARDINFO=MessageRecvFormatServiceImpl,cContainerPark,VEH_CARDINFO,PARK
YX_VIP_VEHICLE_STATISTICS=MessageRecvFormatServiceImpl,cContainerPark,YX_VIP_VEHICLE_STATISTICS,PARK


