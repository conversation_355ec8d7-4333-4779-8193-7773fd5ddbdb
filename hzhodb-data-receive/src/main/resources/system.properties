kafka.listener.config.path =F:\\hzHodbRecv\\config\\kafkaListener.json
kafka.consumer.config.path =F:\\hzHodbRecv\\config\\kafkaConsumer.properties
kafka.producer.config.path =F:\\hzHodbRecv\\config\\kafkaProducer.properties
rocketMq.producer.config.path =F:\\hzHodbRecv\\config\\rocketMQProducer.properties
rocketMq.consumer.config.path =F:\\hzHodbRecv\\config\\rocketMQConsumer.properties

#\u7EBF\u7A0B\u6C60\u5927\u5C0FS
threadpool.count=20

#\u822A\u73ED\u5BA2\u6D41\u6570\u636E
pms_hbkl_url=http://hcss.hiacloud.com/zhjt/pms_hbkl
############################################################
#\u51FA\u79DF\u8F66\u84C4\u8F66\u573A\u5269\u4F59\u8F66\u4F4D\u6570
storage.yard.remaining.parking.space.url=http://hcss.hiacloud.com/zhjt/czc_sycw
storage.yard.remaining.parking.space.event.type=CZC_SYCW
#\u84C4\u8F66\u573A\u573A\u5185\u8F66\u8F86\u6570
storage.yard.remaining.parking.car.number.url=http://hcss.hiacloud.com/zhjt/czc_cls
storage.yard.remaining.parking.car.number.event.type=CZC_CLS
#\u83B7\u53D6\u84C4\u8F66\u573A\u8FC7\u8F66\u8BB0\u5F55
storage.yard.card.record.url=http://hcss.hiacloud.com/zhjt/czc_gcsj
storage.yard.card.record.event.type=CZC_GCSJ
#\u83B7\u53D6\u505C\u8F66\u573A\u8FC7\u8F66\u8BB0\u5F55
park.car.record.url=http://hcss.hiacloud.com/zhjt/pms_gcjl
park.car.record.event.type=PMS_GCJL
#\u83B7\u53D6\u84C4\u8F66\u573A\u57FA\u7840\u6570\u636E
storage.yard.basic.url=http://hcss.hiacloud.com/zhjt/xcq_base
storage.yard.basic.event.type=XCQ_BASE
#\u83B7\u53D6\u505C\u8F66\u573A\u57FA\u7840\u6570\u636E
park.basic.url=http://hcss.hiacloud.com/zhjt/tcl_base
park.basic.event.type=TCL_BASE
#\u83B7\u53D6\u505C\u8F66\u573A\u5269\u4F59\u8F66\u4F4D\u6570
park.remaining.parking.space.url=http://hcss.hiacloud.com/zhjt/tcl_cws
park.remaining.parking.space.event.type=TCL_CWS
#\u83B7\u53D6\u505C\u8F66\u573A\u51FA\u5165\u53E3\u6D41\u91CF
park.passageway.flow.url=http://hcss.hiacloud.com/zhjt/tcl_ll
park.passageway.flow.event.type=TCL_LL
#\u83B7\u53D6\u5730\u94C1\u6570\u636E
subway.passenger.flow.url=http://hcss.hiacloud.com/zhjt/subway/passengerflow
subway.passenger.flow.event.type=SUBWAY_PASSENGER_FLOW
#
barrier.gate.car.record.url=http://hcss.hiacloud.com/zhjt/dzxt
barrier.gate.car.record.event.type=VEHICLE_RECORD
barrier.gate.car.record.minute=5
barrier.gate.car.record.page.size=200

account=
signKey=
dataKey=
aesIv=
url=

bucket=datarecv
endpoint=http://192.168.112.191:9000
access_key=miniominio
secret_key=hayc@123