package com.air.security.sodb.dts.receive.job;

import com.air.security.sodb.data.core.base.Meta;
import com.air.security.sodb.data.core.mq.MyProducer;
import com.air.security.sodb.data.core.mq.impl.SyncRocketMQProducer;
import com.air.security.sodb.data.core.quartz.AbstractJob;
import com.air.security.sodb.data.core.util.HttpRequestUtil;
import com.air.security.sodb.data.core.util.PropertyUtil;
import com.air.security.sodb.data.core.util.UuidUtil;
import com.air.security.sodb.dts.receive.server.MqMessageRecvServer;
import com.air.security.sodb.dts.receive.server.MqMessageRecvServerImpl;
import com.air.security.sodb.dts.receive.util.AesUtil;
import com.air.security.sodb.dts.receive.util.SignUtil;
import com.alibaba.fastjson.JSON;
import com.alibaba.fastjson.JSONArray;
import com.alibaba.fastjson.JSONObject;
import org.apache.commons.httpclient.util.DateUtil;
import org.quartz.JobDataMap;
import org.slf4j.Logger;
import org.slf4j.LoggerFactory;

import java.util.Date;
import java.util.HashMap;
import java.util.Map;

public class ParklotListJob extends AbstractJob {

    private static final Logger logger = LoggerFactory.getLogger(ParklotListJob.class);

    private static final String ACCOUNT = PropertyUtil.getProperty("account");
    private static final String SIGN_KEY = PropertyUtil.getProperty("signKey");
    private static final String DATA_KEY = PropertyUtil.getProperty("dataKey");
    private static final String AES_IV = PropertyUtil.getProperty("aesIv");
    private static final String URL = PropertyUtil.getProperty("url");

    private final MyProducer producer = new SyncRocketMQProducer();

    @Override
    public void executeJob(JobDataMap paramMap) {
        fetchParkingHistoryRecords();
    }

    private void fetchParkingHistoryRecords() {
        String timeStamp = DateUtil.formatDate(new Date(), "yyyyMMddHHmmss");
        Map<String, String> dataMap = new HashMap<>();

        Map<String, String> contentMap = new HashMap<>();
        contentMap.put("method", "queryParklotList");
        contentMap.put("param", JSON.toJSONString(dataMap));

        String content = JSON.toJSONString(contentMap);

        Map<String, String> param = new HashMap<String, String>();
        param.put("account", ACCOUNT);
        param.put("timeStamp", timeStamp);
        param.put("sign", SignUtil.createSign(ACCOUNT, timeStamp, SIGN_KEY));
        try {
            String encryptedContent = AesUtil.encryptCBC(content, DATA_KEY, AES_IV);
            param.put("content", encryptedContent);
        } catch (Exception e) {
            logger.error("Failed to encrypt content", e);
            return;
        }

        String responseJson = HttpRequestUtil.sendPost(URL, param);
        JSONObject responseObj = JSON.parseObject(responseJson);

        if (!"000000".equals(responseObj.getString("code"))) {
            logger.error("Failed to fetch parking history records: {}", responseObj.getString("msg"));
            return;
        }

        JSONArray dataObj = responseObj.getJSONArray("data");
        if (null != null && dataObj.size() != 0) {
            for (int i = 0; i < dataObj.size(); i++) {
                JSONObject record = dataObj.getJSONObject(i);
                // 处理每条记录
                processRecord(record);
            }
        }
    }

    private void processRecord(JSONObject record) {
        // 处理每条记录的逻辑
        String parklotCode = record.getString("parklotCode");
        String parklotName = record.getString("parklotName");

        JSONObject json = new JSONObject();
        json.put("parklotCode", parklotCode);
        json.put("parklotName", parklotName);

        // 获取消息头标签
        Meta meta = new Meta();
        meta.setEventType("PARK_LOT_LIST");
        meta.setRecvSequence(UuidUtil.getUuid32());
        MqMessageRecvServer server = new MqMessageRecvServerImpl(producer);
        server.handle(meta, json.toString());
    }
}