package com.air.security.sodb.dts.receive.service.erp;

import com.air.security.sodb.data.core.base.Meta;
import com.air.security.sodb.data.core.constant.MsgIdConstant;
import com.air.security.sodb.data.core.util.HaLog;
import com.air.security.sodb.dts.receive.service.AbstractMessageRecvService;
import com.air.security.sodb.dts.receive.util.XmlToJsonUtils;
import com.alibaba.fastjson.JSONObject;
import org.slf4j.Logger;
import org.slf4j.LoggerFactory;
import org.springframework.stereotype.Service;

/**
 * <AUTHOR>
 * @date 2021/11/12
 * Erp组织机构信息消息格式化的Service实现
 */
@Service("MessagePersonnelFormatServiceImpl")
public class MessagePersonnelFormatServiceImpl extends AbstractMessageRecvService {
    private static final Logger log = LoggerFactory.getLogger(MessagePersonnelFormatServiceImpl.class);


    @Override
    public void execute(Meta meta, String messageBody) throws Exception {
        HaLog.info(log, MsgIdConstant.MS_INF_0001, meta.getEventType());


        String inputJson = XmlToJsonUtils.xml2json(messageBody);
        JSONObject inputObject = JSONObject.parseObject(inputJson);
        JSONObject bmfsObject = inputObject.getJSONObject("MSG").getJSONObject("YGTB");

        // 响应json消息
        JSONObject outputMsg = new JSONObject();

        // 响应消息头
        outputMsg.put("meta", meta);

        // 响应消息体
        outputMsg.put("body", bmfsObject);

        // 转换为JSON格式，并发送消息到指定主题
        super.putSendMessage(outputMsg);

        HaLog.info(log, MsgIdConstant.MS_INF_0002, meta.getEventType());
    }
}
