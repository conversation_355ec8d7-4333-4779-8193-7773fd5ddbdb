package com.air.security.sodb.dts.receive.service.maintenance;

import com.air.security.sodb.data.core.base.Meta;
import com.air.security.sodb.data.core.constant.MsgIdConstant;
import com.air.security.sodb.data.core.util.Base64Util;
import com.air.security.sodb.data.core.util.HaLog;
import com.air.security.sodb.data.core.util.MinioUtil;
import com.air.security.sodb.data.core.util.PropertyUtil;
import com.air.security.sodb.dts.receive.service.AbstractMessageRecvService;
import com.alibaba.fastjson.JSONObject;
import org.apache.commons.lang3.StringUtils;
import org.slf4j.Logger;
import org.slf4j.LoggerFactory;
import org.springframework.stereotype.Service;

import java.io.InputStream;
import java.util.Arrays;
import java.util.List;
import java.util.stream.Collectors;

@Service("MaintenanceRecvFormatServiceImpl")
public class MaintenanceRecvFormatServiceImpl extends AbstractMessageRecvService {

    private static final Logger log = LoggerFactory.getLogger(MaintenanceRecvFormatServiceImpl.class);

    private static final String BUCKET = PropertyUtil.getProperty("bucket");
    private static final String AIRPORT_IATA = PropertyUtil.getProperty("airport.iata");

    @Override
    public void execute(Meta meta, String messageBody) throws Exception {
        HaLog.info(log, MsgIdConstant.MS_INF_0001, meta.getEventType());

        // 获取到的json业务数据
        JSONObject jsonObject = new JSONObject();
        JSONObject json1 = JSONObject.parseObject(messageBody);
        HaLog.info(log, MsgIdConstant.MS_INF_0002, "Received message: " + json1.toJSONString());

        // 上传图片到服务器，并将返回的url放入json对象中
        List<String> fields = Arrays.asList("photobase1", "photobase2", "photobase3");
        for (String field : fields) {
            uploadPicture(json1, meta, field);
        }

        // 获取 photobase1, photobase2, photobase3 字段的值并合并
        String combinedPhotos = fields.stream().map(json1::getString).filter(StringUtils::isNotBlank).collect(Collectors.joining(";"));

        // 将新的字段放入JSON对象
        json1.put("combinedPhotos", combinedPhotos);

        // 添加响应消息头和消息体
        jsonObject.put("meta", meta);
        jsonObject.put("body", json1);

        // 转换为JSON格式，并发送消息到指定主题
        super.putSendMessage(jsonObject);
        HaLog.info(log, MsgIdConstant.MS_INF_0002, "Processed event: " + meta.getEventType());
    }

    /**
     * 上传图片到服务器，返回url，拼接json
     *
     * @param json    JSON对象
     * @param meta    元数据
     * @param blobKey 字段
     * @throws Exception
     */
    private void uploadPicture(JSONObject json, Meta meta, String blobKey) throws Exception {
        String imgName = json.getString(blobKey);
        if (StringUtils.isNotBlank(imgName)) {
            String eventType = meta.getEventType();
            String orderNo = json.getString("orderNo");
            String name = StringUtils.isNotBlank(orderNo) ? orderNo + "_" : "";
            String objectKey = MinioUtil.splicingPath(AIRPORT_IATA, eventType, name);

            try (InputStream stream = Base64Util.generateIs(imgName)) {
                int available = stream.available();
                String imgNameUrl = MinioUtil.uploadInputStream(BUCKET, objectKey, stream, available);
                json.put(blobKey, imgNameUrl);
                HaLog.info(log, MsgIdConstant.MS_INF_0009, "Uploaded maintenance photo to Minio", imgNameUrl);
            }
        }
    }



}
