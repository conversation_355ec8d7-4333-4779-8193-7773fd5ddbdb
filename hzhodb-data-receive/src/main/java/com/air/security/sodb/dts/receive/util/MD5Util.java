package com.air.security.sodb.dts.receive.util;

import com.air.security.sodb.data.core.util.HaLog;
import org.slf4j.Logger;
import org.slf4j.LoggerFactory;
import org.springframework.util.Base64Utils;

import java.security.MessageDigest;
import java.security.NoSuchAlgorithmException;

/**
 * MD5加码 生成32位md5码 
 * <AUTHOR>
 *
 */
public class MD5Util {
	private static final Logger log = LoggerFactory.getLogger(MD5Util.class);
    public static String string2MD5(String password){
    	String result = password;
    	try {  
            // 1,获取MD5摘要算法的MessageDigest对象  
            MessageDigest instance = MessageDigest.getInstance("MD5");
            // 2,对字符串加密,返回字节数组  
            byte[] digest = instance.digest(password.getBytes());  
            StringBuffer sb = new StringBuffer();
            for (byte b : digest) {  
                // 3,获取字节的低八位有效值  
                int i = b & 0xff;  
                // 4,将整数转为16进制  
                String hexString = Integer.toHexString(i);
                // 5,如果是1位的话,补0  
                if (hexString.length() < 2) {  
                    hexString = "0" + hexString;  
                }  
                // 6,把密文添加到缓存中  
                sb.append(hexString);  
                result = sb.toString();
            }  
            } catch (NoSuchAlgorithmException e) {
            	HaLog.error(log, "加密失败",e);
            }  
    	return result;
    }  
    public static void main(String[] args) {
		
		String userName="ABPTtest";
		String accessCode = "HsNMELv1cYgg28GMjWcg";
		String pwd = "Abpt123456";
		
		String base64UserName = Base64Utils.encodeToString(userName.getBytes());
		System.out.println(base64UserName);
		String loginSignature = MD5Util.string2MD5(base64UserName + accessCode + MD5Util.string2MD5(pwd));
		System.out.println(loginSignature);
	}
}
