package com.air.security.sodb.dts.srvc.restsrvc.receive.demo;

import com.air.security.sodb.data.core.constant.MsgIdConstant;
import com.air.security.sodb.data.core.service.ServiceOutDTO;
import com.air.security.sodb.data.core.util.HaLog;
import com.air.security.sodb.data.core.util.RequestUtil;
import com.air.security.sodb.dts.srvc.domain.service.DemoService;
import com.air.security.sodb.dts.srvc.restsrvc.platform.demo.dto.DemoDto;
import com.air.security.sodb.dts.srvc.restsrvc.platform.demo.form.DemoForm;
import com.alibaba.fastjson.JSONObject;
import org.slf4j.Logger;
import org.slf4j.LoggerFactory;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.web.bind.annotation.CrossOrigin;
import org.springframework.web.bind.annotation.RequestMapping;
import org.springframework.web.bind.annotation.RequestMethod;
import org.springframework.web.bind.annotation.RestController;

import javax.servlet.http.HttpServletRequest;
import javax.servlet.http.HttpServletResponse;
import java.util.List;

/**
 * demo服务
 *
 * <AUTHOR>
 */
@CrossOrigin
@RestController
@RequestMapping("/api/release/demoSearchRelease")
public class DemoSearchReleaseController {

    private static final Logger log = LoggerFactory.getLogger(DemoSearchReleaseController.class);

    @Autowired
    DemoService service;

    @RequestMapping(value = "/execute", method = RequestMethod.POST)
    public Object execute(HttpServletRequest request, HttpServletResponse response) {
        HaLog.info(log, MsgIdConstant.MS_INF_0001, "demo查询服务");
        try {
            JSONObject jsonObj = RequestUtil.requestGetJson(request);
            DemoForm demoForm = JSONObject.parseObject(jsonObj.toJSONString(), DemoForm.class);
            ServiceOutDTO<List<DemoDto>> list = service.getList(demoForm);

            HaLog.info(log, MsgIdConstant.MS_INF_0002, "demo查询服务");
            return list;

        } catch (Exception e) {
            HaLog.error(log, e, MsgIdConstant.MS_ERR_0001);
            return null;
        }
    }

}
