#\u6D88\u606F\u6807\u51C6\u5316\u5904\u7406Service\u914D\u7F6E
#\u65C5\u68C0\u6570\u636E
PRSC=ScimsImgMessageRecvFormatServiceImpl,cC<PERSON>r<PERSON>assenger,PSGR_SECURITY,PSGR_DATA
check=ScimsImgMessageRecvFormatServiceImpl,cContainerPassenger,PRSCJSON_SECURITY,PSGR_DATA
bag=ScimsImgMessageRecvFormatServiceImpl,cContainerPassenger,PSGR_OPEN_INFO,PSGR_DATA
#\u822A\u73ED\u6570\u636E
flight=MessageRecvFormatServiceImpl,cContainerFlight,FLIGHT_DYN,FLIGHT_DATA
#\u8D27\u8FD0\u5B89\u68C0\u7CFB\u7EDF-\u8FD0\u5355\u57FA\u672C\u4FE1\u606F
WAY_BILL_LOG=MessageRecvFormatServiceImpl,cContainerCargo,WAY_BILL_LOG,CARGO
#\u8D27\u8FD0\u5B89\u68C0\u7CFB\u7EDF-\u8FD0\u5355\u5F00\u5305\u4FE1\u606F
OPEN_LOG=CargoOpenMessageRecvFormatServiceImpl,cContainerCargo,OPEN_LOG,CARGO
#\u8D27\u8FD0\u5B89\u68C0\u7CFB\u7EDF-\u8FD0\u5355\u5B89\u68C0\u4FE1\u606F
SECURITY_LOG=MessageRecvFormatServiceImpl,cContainerCargo,SECURITY_LOG,CARGO
#\u8D27\u8FD0\u5B89\u68C0\u7CFB\u7EDF-\u5B89\u68C0\u901A\u9053\u7684\u5F00\u653E\u4FE1\u606F
CHECKSTATUS_EQU=MessageRecvFormatServiceImpl,cContainerCargo,CHECKSTATUS_EQU,CARGO
#\u5B87\u89C6\u624B\u52A8\u62A5\u8B66\u62A5\u8B66\u4FE1\u606F
YS_ALARM=MessageRecvFormatServiceImpl,cContainerAlarm,YS_ALARM,ALARM
#\u505C\u8F66\u573A\u9ED1\u540D\u5355\u62A5\u8B66\u4FE1\u606F
CAR_ALARM=MessageRecvFormatServiceImpl,cContainerAlarm,CAR_ALARM,ALARM
#\u505C\u8F66\u573A\u8FDB\u51FA\u8F66\u8F86\u63A5\u5165
CAR_PARK_INFO = CarMessageRecvFormatServiceImpl,cContainerCar,CAR_PARK_INFO,CAR_DATA
CAR_PARK_OUT = CarMessageRecvFormatServiceImpl,cContainerCar,CAR_PARK_OUT,CAR_DATA
#\u505C\u8F66\u573A\u9ED1\u540D\u5355\u63A5\u5165
CAR_EQU_INFO = MessageRecvFormatServiceImpl,cContainerEqu,CAR_EQU_INFO,EQU
#\u505C\u8F66\u573A\u9ED1\u540D\u5355\u63A5\u5165
CAR_BLACK_INFO = MessageRecvFormatServiceImpl,cContainerCar,CAR_BLACK_INFO,CAR_DATA
#\u505C\u8F66\u573A\u767D\u540D\u5355\u63A5\u5165
CAR_WHITE_INFO = MessageRecvFormatServiceImpl,cContainerCar,CAR_WHITE_INFO,CAR_DATA

