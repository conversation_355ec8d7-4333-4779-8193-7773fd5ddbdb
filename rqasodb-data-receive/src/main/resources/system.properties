#\u7EBF\u7A0B\u6C60\u5927\u5C0F
threadpool.count=20

#kafkaConsumer kafka\u6D88\u8D39\u8005\u914D\u7F6E\u6587\u4EF6\u8DEF\u5F84
#kafka.consumer.config.path=D:\\config\\kafkaConsumer.properties
kafka.consumer.config.path=/server/dtsServer/xjjcDataRecv/config/kafkaConsumer.properties
#kafkaProducer kafka\u6D88\u8D39\u8005\u914D\u7F6E\u6587\u4EF6\u8DEF\u5F84
#kafka.producer.config.path=D:\\config\\kafkaProducer.properties
kafka.producer.config.path=/server/dtsServer/xjjcDataRecv/config/kafkaProducer.properties
#\u8BB0\u5F55\u6570\u636E\u4F20\u8F93\u65E5\u5FD7\u7684topic
kafka.msg.transfer.log.topic=msgTranLog
rqasodb.stop.port=20032

#\u65C5\u68C0\u6570\u636E\u63A5\u5165\u4E3B\u9898
psgr.service=true
kafka.psgr.msg.input.topics=relsTopic1
#\u8D27\u68C0\u53D1\u9001\u63A5\u53E3
cargo.service=true
kafka.cargo.msg.output.topic=relsTopic2
#\u822A\u73ED\u6570\u636E\u63A5\u5165\u4E3B\u9898
flight.service=true
kafka.flight.msg.input.topics=relsTopic3

########################  \u6587\u4EF6\u670D\u52A1\u5668minio\u53C2\u6570\u76F8\u5173\u914D\u7F6E   ########################
#minio\u53C2\u6570
endpoint = http://10.29.25.102:9000
access_key = miniominio
secret_key = hayc@123
bucket = datarecv
#\u673A\u573A\u4E09\u5B57\u7801
airport.iata=RQA












