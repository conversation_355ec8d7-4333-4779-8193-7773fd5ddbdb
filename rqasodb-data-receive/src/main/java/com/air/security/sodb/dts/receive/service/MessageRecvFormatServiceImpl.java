package com.air.security.sodb.dts.receive.service;

import org.slf4j.Logger;
import org.slf4j.LoggerFactory;
import org.springframework.stereotype.Service;

import com.air.security.sodb.data.core.base.Meta;
import com.air.security.sodb.data.core.constant.MsgIdConstant;
import com.air.security.sodb.data.core.exception.SystemBaseException;
import com.air.security.sodb.data.core.util.HaLog;
import com.alibaba.fastjson.JSON;
import com.alibaba.fastjson.JSONObject;

/**
 * @Description: 消息格式化的Service实现
 * @author: zhangc
 * @Date: 2018年12月26日
 */
@Service("MessageRecvFormatServiceImpl")
public class MessageRecvFormatServiceImpl extends AbstractMessageRecvService {

    private static final Logger log = LoggerFactory.getLogger(MessageRecvFormatServiceImpl.class);

    /**
     * 业务处理
     */
    @Override
    public void execute(Meta meta, String messageBody) throws SystemBaseException {
        HaLog.info(log, MsgIdConstant.MS_INF_0001, meta.getEventType());

        // 获取到的json业务数据
        Object inputJson = JSON.parse(messageBody);

        // 响应json消息
        JSONObject outputMsg = new JSONObject();

        // 响应消息头
        outputMsg.put("meta", meta);

        // 响应消息体
        outputMsg.put("body", inputJson);

        // 转换为JSON格式，并发送消息到指定主题
        super.putSendMessage(outputMsg);

        HaLog.info(log, MsgIdConstant.MS_INF_0002, meta.getEventType());
    }

}
