package com.air.security.sodb.dts.receive.server.scims;

import com.air.security.sodb.data.core.base.Meta;
import com.air.security.sodb.data.core.constant.GlobalCodeConstant;
import com.air.security.sodb.data.core.constant.MsgIdConstant;
import com.air.security.sodb.data.core.util.*;
import com.air.security.sodb.dts.receive.service.AbstractMessageRecvService;
import com.alibaba.fastjson.JSONObject;
import org.apache.commons.lang3.StringUtils;
import org.slf4j.Logger;
import org.slf4j.LoggerFactory;
import org.springframework.stereotype.Service;

import java.io.InputStream;

@Service("ScimsImgMessageRecvFormatServiceImpl")
public class ScimsImgMessageRecvFormatServiceImpl extends AbstractMessageRecvService {

    private static final Logger log = LoggerFactory.getLogger(ScimsImgMessageRecvFormatServiceImpl.class);

    private static final String BUCKET = PropertyUtil.getProperty("bucket");

    @Override
    public void execute(Meta meta, String messageBody) throws Exception {
        HaLog.info(log, MsgIdConstant.MS_INF_0001, meta.getEventType());

        JSONObject json = JSONObject.parseObject(messageBody);

        // 上传图片到服务器，返回url，拼接json
        if ("PSGR_OPEN_INFO".equals(meta.getEventType())) {
            String airportIata = json.getString("airportCode");
            this.uploadPicture(json, meta, airportIata, "img");
        } else if ("PRSCJSON_SECURITY".equals(meta.getEventType())) {
            String airportIata = json.getString("dpsrDeparture");
            this.uploadPicture(json, meta, airportIata, "dpsrCardPhoto");
            this.uploadPicture(json, meta, airportIata, "dpscCheckPhoto");
            String dpsrCardNumber = json.getString("dpsrCardNumber");
            if (StringUtils.isNotBlank(dpsrCardNumber)) {
                json.put("dpsrCardNumber", HaAesCryptUtil.crypt(dpsrCardNumber, GlobalCodeConstant.AES_KEY_PSGR));
            } else {
                json.put("dpsrCardNumber", "");
            }

        }

        // 响应json消息
        JSONObject outputMsg = new JSONObject();

        // 响应消息头
        outputMsg.put("meta", meta);

        // 响应消息体
        outputMsg.put("body", json);

        // 转换为JSON格式，并发送消息到指定主题
        super.putSendMessage(outputMsg);

        HaLog.info(log, MsgIdConstant.MS_INF_0002, meta.getEventType());
    }

    /**
     * 上传图片到服务器，返回url，拼接json
     *
     * @param json
     * @param meta
     * @param blobKey 字段
     * @throws Exception
     */
    private void uploadPicture(JSONObject json, Meta meta, String airportIata, String blobKey) throws Exception {

        // base64图片
        String imgName = json.getString(blobKey);
        if (StringUtils.isNotBlank(imgName)) {

            // 事件类型
            String eventType = meta.getEventType();

            String name = "";

            //验证ID
            String dpscDpsrId = json.getString("dpscDpsrId");

            if (StringUtils.isNotBlank(dpscDpsrId)) {
                name = dpscDpsrId + "_";
            }

            // 拼接路径
            String objectKey = MinioUtil.splicingPath(airportIata, eventType, name);

            InputStream stream = Base64Util.generateIs(imgName);
            int available = stream.available();
            // 上传图片至图片服务器
            String imgNameUrl = MinioUtil.uploadInputStream(BUCKET, objectKey, stream, available);

            // 图片url
            json.put(blobKey, imgNameUrl);

            stream.close();

            HaLog.info(log, MsgIdConstant.MS_INF_0009, "上传旅客图片到minio", imgNameUrl);
        }

    }
}
