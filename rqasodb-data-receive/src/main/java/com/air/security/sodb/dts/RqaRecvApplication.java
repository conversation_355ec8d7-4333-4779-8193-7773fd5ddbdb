package com.air.security.sodb.dts;

import com.air.security.sodb.data.core.constant.MsgIdConstant;
import com.air.security.sodb.data.core.quartz.SchedulerManager;
import com.air.security.sodb.data.core.util.HaLog;
import com.air.security.sodb.data.core.util.PropertyUtil;
import com.air.security.sodb.data.core.util.ThreadUtil;
import com.air.security.sodb.dts.receive.listener.KafkaCargoListener;
import com.air.security.sodb.dts.receive.listener.KafkaFlightMsgListener;
import com.air.security.sodb.dts.receive.listener.KafkaPsgrMsgListener;
import org.slf4j.Logger;
import org.slf4j.LoggerFactory;
import org.springframework.boot.ApplicationArguments;
import org.springframework.boot.ApplicationRunner;
import org.springframework.boot.SpringApplication;
import org.springframework.boot.autoconfigure.SpringBootApplication;
import org.springframework.context.annotation.ImportResource;

import java.io.*;
import java.net.ServerSocket;
import java.net.Socket;

/**
 * <AUTHOR> @Description 若羌数据接入服务
 */
@ImportResource("classpath:/applicationContext.xml")
@SpringBootApplication(scanBasePackages = "com.air.security.sodb")
public class RqaRecvApplication implements ApplicationRunner {

    private static final Logger log = LoggerFactory.getLogger(RqaRecvApplication.class);

    private static boolean cargoService = Boolean.parseBoolean(PropertyUtil.getProperty("cargo.service"));
    private static boolean psgrService = Boolean.parseBoolean(PropertyUtil.getProperty("psgr.service"));
    private static boolean flightService = Boolean.parseBoolean(PropertyUtil.getProperty("flight.service"));


    private static ServerSocket server;

    public static void main(String[] args) {

        SpringApplication.run(RqaRecvApplication.class, args);

    }

    /**
     * 启动服务
     *
     * @param args
     * @throws Exception
     */
    @Override
    public void run(ApplicationArguments args) throws Exception {
        // 启动定时任务
        runQuartzJob();

        if (cargoService) {
            ThreadUtil.runWithNewThread(new KafkaCargoListener());
        }

        if (psgrService) {
            ThreadUtil.runWithNewThread(new KafkaPsgrMsgListener());
        }

        if (flightService) {
            ThreadUtil.runWithNewThread(new KafkaFlightMsgListener());
        }

        stopJob(Integer.valueOf(PropertyUtil.getProperty("rqasodb.stop.port")), "rqasodb");

    }


    /**
     * 监听停止服务命令
     *
     * @param port        端口号
     * @param projectName 工程名
     */
    private static void stopJob(int port, String projectName) {
        Socket commandSocket = null;
        BufferedReader reader = null;

        try {
            server = new ServerSocket(port);
            commandSocket = server.accept();
            reader = new BufferedReader(new InputStreamReader(commandSocket.getInputStream()));
        } catch (IOException e) {
            HaLog.info(log, MsgIdConstant.MS_ERR_0001, e);
            System.exit(0);
        }

        while (true) {
            try {
                String commandStr = reader.readLine();
                if ("STOP".equals(commandStr)) {

                    // 关闭数据流
                    reader.close();
                    commandSocket.close();
                    server.close();

                    ThreadUtil.shutdown();

                    HaLog.info(log, MsgIdConstant.MS_INF_0002, projectName);
                    System.exit(0);
                }

                Thread.sleep(1000);
            } catch (IOException e) {
                HaLog.info(log, MsgIdConstant.MS_ERR_0001, e);
            } catch (InterruptedException e) {
                HaLog.info(log, MsgIdConstant.MS_ERR_0001, e);
            }
        }
    }

    /**
     * 启动定时任务
     */
    private static void runQuartzJob() {
        HaLog.info(log, MsgIdConstant.MS_INF_0001, "quartz定时任务");
        SchedulerManager manager = new SchedulerManager();
        manager.runJob("quartzJob.xml");
        HaLog.info(log, MsgIdConstant.MS_INF_0002, "quartz定时任务");
    }
}
