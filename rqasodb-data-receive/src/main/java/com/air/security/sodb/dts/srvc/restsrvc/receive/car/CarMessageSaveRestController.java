package com.air.security.sodb.dts.srvc.restsrvc.receive.car;

import com.air.security.sodb.data.core.base.Meta;
import com.air.security.sodb.data.core.constant.MsgIdConstant;
import com.air.security.sodb.data.core.util.HaLog;
import com.air.security.sodb.data.core.util.RequestUtil;
import com.air.security.sodb.data.core.util.UuidUtil;
import com.air.security.sodb.dts.receive.server.MqMessageRecvServer;
import com.air.security.sodb.dts.receive.server.MqMessageRecvServerImpl;
import com.alibaba.fastjson.JSONObject;
import org.apache.commons.lang3.StringUtils;
import org.slf4j.Logger;
import org.slf4j.LoggerFactory;
import org.springframework.web.bind.annotation.CrossOrigin;
import org.springframework.web.bind.annotation.RequestMapping;
import org.springframework.web.bind.annotation.RequestMethod;
import org.springframework.web.bind.annotation.RestController;

import javax.servlet.http.HttpServletRequest;

/**
 * @Description: 停车场数据接入
 * @author: mk
 * @Date: 2020-08-13
 */
@CrossOrigin
@RestController
@RequestMapping("/api/release/carMessage")
public class CarMessageSaveRestController {

    private static final Logger log = LoggerFactory.getLogger(CarMessageSaveRestController.class);

    /**
     *
     */
    private MqMessageRecvServer server = new MqMessageRecvServerImpl();

    /**
     * 接口
     */
    @RequestMapping(value = "/execute", method = RequestMethod.POST)
    public CarDataResponseDto execute(HttpServletRequest request) {
        JSONObject jsonObj = RequestUtil.requestGetJson(request);
        HaLog.info(log, MsgIdConstant.MS_INF_0001, "停车场数据接入接口");
        CarDataResponseDto responseDto = new CarDataResponseDto();
        try {
            if (null != jsonObj) {
                // 执行业务处理
                this.doExecute(jsonObj);
            }
            HaLog.info(log, MsgIdConstant.MS_INF_0002, "停车场数据接入接口");
            responseDto.success();
            return responseDto;
        } catch (Exception e) {
            responseDto.fail();
            HaLog.error(log, e, MsgIdConstant.MS_ERR_0001);
            return responseDto;
        }
    }

    /**
     * @param jsonObj 请求信息
     * @throws Exception
     */
    protected void doExecute(JSONObject jsonObj) {

        Meta meta = new Meta();
        String enterExitType = jsonObj.getString("enterExitType");
        if ("1".equals(enterExitType)) {
            meta.setEventType("CAR_ALARM");
        } else if ("3".equals(enterExitType)) {
            meta.setEventType("CAR_PARK_INFO");
        } else if ("4".equals(enterExitType)) {
            meta.setEventType("CAR_PARK_OUT");
        }
        if (StringUtils.isNotBlank(meta.getEventType())) {
            meta.setRecvSequence(UuidUtil.getUuid32());
            server.handle(meta, jsonObj.toJSONString());
        }
    }

}
