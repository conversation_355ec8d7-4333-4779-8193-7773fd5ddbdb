package com.air.security.sodb.dts;

import com.air.security.sodb.data.core.constant.MsgIdConstant;
import com.air.security.sodb.data.core.util.HaLog;
import com.air.security.sodb.data.core.util.PropertyUtil;
import org.slf4j.Logger;
import org.slf4j.LoggerFactory;

import java.io.BufferedWriter;
import java.io.IOException;
import java.io.OutputStreamWriter;
import java.net.Socket;

/**
 * @Description:
 * @author: zhangc
 * @Date: 2019年3月19日
 */
public class RunStop {
    private static final Logger log = LoggerFactory.getLogger(RunStop.class);

    public static void main(String[] args) {
        HaLog.info(log, MsgIdConstant.MS_INF_0001, "job停止处理");
        Socket command = null;
        BufferedWriter writer = null;
        try {

            // 创建Socket
            command = new Socket("127.0.0.1", Integer.valueOf(PropertyUtil.getProperty("rqasodb.stop.port")));
            writer = new BufferedWriter(new OutputStreamWriter(command.getOutputStream()));

            // 发送STOP命令
            writer.write("STOP");
            writer.newLine();
            writer.flush();
        } catch (Exception e) {
            HaLog.info(log, MsgIdConstant.MS_ERR_0001, e);
        } finally {
            if (writer != null) {
                try {
                    writer.close();
                } catch (IOException e) {
                    HaLog.info(log, MsgIdConstant.MS_ERR_0001, e);
                }
            }
            if (command != null) {
                try {
                    command.close();
                } catch (IOException e) {
                    HaLog.info(log, MsgIdConstant.MS_ERR_0001, e);
                }
            }
        }

        HaLog.info(log, MsgIdConstant.MS_INF_0002, "job停止处理");
        System.exit(0);
    }
}
