package com.air.security.sodb.dts.srvc.restsrvc.receive.car;

public class CarDataResponseDto {
	
	private Integer status;
	
	private String msg;

	private Object data;

	public CarDataResponseDto() {
	}

	public void success() {
		this.status = 1;
		this.msg = "成功";
		this.data = null;
	}

	public void fail() {
		this.status = 0;
		this.msg = "失败";
		this.data = null;
	}

	public Integer getStatus() {
		return status;
	}

	public void setStatus(Integer status) {
		this.status = status;
	}

	public String getMsg() {
		return msg;
	}

	public void setMsg(String msg) {
		this.msg = msg;
	}

	public Object getData() {
		return data;
	}

	public void setData(Object data) {
		this.data = data;
	}
}
