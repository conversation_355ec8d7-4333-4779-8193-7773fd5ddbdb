#æ¶æ¯æ ååå¤çServiceéç½®
#é²åºï¼æå¨æ¥è­¦ï¼èµæºåºç¡ä¿¡æ¯
hkhandEquInfo=MessageRecvFormatServiceImpl,cContainerEqu,HAND_EQU,EQU
#é²åºï¼æå¨æ¥è­¦ï¼èµæºç¶æåæ´ä¿¡æ¯
hkhandEquState=MessageRecvFormatServiceImpl,cC<PERSON>r<PERSON>qu,HAND_EQU_STATE,EQU_STATES
#æå¨æ¥è­¦æ¥è­¦ä¿¡æ¯
handAlarmMessage=MessageRecvFormatServiceImpl,cContainerAlarm,HAND_ALARM,ALARM
#å®è§æå¨æ¥è­¦æ¥è­¦ä¿¡æ¯
ysHandAlarmMessage=MessageRecvFormatServiceImpl,cContainerAlarm,YSHAND_ALARM,ALARM
#å®è§è§é¢åææ¥è­¦æ¥è­¦ä¿¡æ¯
ysVideoAnalysisAlarmMessage=MessageRecvFormatServiceImpl,cContainerAlarm,YSVIDEO_ANALYSIS_ALARM,ALARM
#å®è§èµæºåºç¡ä¿¡æ¯
ysHandEquInfo=MessageRecvFormatServiceImpl,cContainerEqu,YSHAND_EQU,EQU