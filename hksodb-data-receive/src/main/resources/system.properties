#\u7EBF\u7A0B\u6C60\u5927\u5C0F
threadpool.count=20
rest.dh.pageSize=200
#kafkaConsumer kafka\u6D88\u8D39\u8005\u914D\u7F6E\u6587\u4EF6\u8DEF\u5F84
kafka.consumer.config.path=D:\\server\\dts\\config\\kafkaConsumer.properties
#kafka.consumer.config.path=/server/dtsServer/hkSodbRecv/config/kafkaConsumer.properties
#kafkaProducer kafka\u6D88\u8D39\u8005\u914D\u7F6E\u6587\u4EF6\u8DEF\u5F84
kafka.producer.config.path=D:\\server\\dts\\config\\kafkaProducer.properties
#kafka.producer.config.path=/server/dtsServer/hkSodbRecv/config/kafkaProducer.properties
#\u8BB0\u5F55\u6570\u636E\u4F20\u8F93\u65E5\u5FD7\u7684topic
kafka.msg.transfer.log.topic=msgTranLog
qzsodb.stop.port=20032
#\u6D77\u5EB7\u670D\u52A1ip:port
rest.hk.host=************:443
#\u6D77\u5EB7\u670D\u52A1appkey
rest.hk.appKey=29274096
#\u6D77\u5EB7\u670D\u52A1appsecret
rest.hk.appSecret=jZwms1ZE5km3S4Hn502p
#\u9632\u533A\uFF08\u624B\u52A8\u62A5\u8B66\uFF09\u8D44\u6E90\u57FA\u7840\u4FE1\u606Fcode
hk.handEquInfo.service.code=hkhandEquInfo
#\u6D77\u5EB7\u9632\u533A\uFF08\u624B\u52A8\u62A5\u8B66\uFF09\u8D44\u6E90\u57FA\u7840\u4FE1\u606Furl
hk.handEquInfo.url=/api/irds/v2/deviceResource/resources
#\u9632\u533A\uFF08\u624B\u52A8\u62A5\u8B66\uFF09\u8D44\u6E90\u53D8\u66F4\u57FA\u7840\u4FE1\u606Fcode
hk.handEquState.service.code=hkhandEquState
#\u6D77\u5EB7\u9632\u533A\uFF08\u624B\u52A8\u62A5\u8B66\uFF09\u8D44\u6E90\u53D8\u66F4\u57FA\u7840\u4FE1\u606Furl
hk.handEquState.url=/api/scpms/v1/defence/status
#\u6D77\u5EB7\u62A5\u8B66\u8F93\u5165\uFF08\u624B\u52A8\u62A5\u8B66\uFF09\u8D44\u6E90\u53D8\u66F4\u4FE1\u606Furl
hk.handEquIoInState.url=/api/pems/v1/defense/ioIn/state
#\u6D77\u5EB7\u62A5\u8B66\u8BA2\u9605url
hk.eventSubscription.url=/api/eventService/v1/eventSubscriptionByEventTypes
#\u6D77\u5EB7\u624B\u52A8\u62A5\u8B66\u62A5\u8B66\u4FE1\u606Fcode
hk.handAlarmMessage.service.code=handAlarmMessage
#sodb\u624B\u52A8\u62A5\u8B66\u8D44\u6E90\u8BF7\u6C42
sodb.ec03.equ.status.req.url=http://************/api/bs/equ/info/open/getEquInfoByCode/EC03
########################  \u5B87\u89C6\u5E73\u53F0\u76F8\u5173   ########################
# \u5B87\u89C6\u5E73\u53F0\u5BF9\u63A5\u83B7\u53D6 AccessCode \u8BF7\u6C42\u5730\u5740
yushi.login.url=http://************:8088/VIID/login
yushi.login.username=loadmin
yushi.login.password=Ab*123456
# \u5B87\u89C6\u62A5\u8B66\u63A5\u5165\u5F00\u5173
yushi.alarm.switch=on
# \u5B87\u89C6\u5E73\u53F0\u8BA2\u9605\u544A\u8B66\u63A5\u53E3\u5730\u5740
yushi.alarm.subscribe=http://************:8088/VIID/alarm/open
#\u5B87\u89C6\u624B\u52A8\u62A5\u8B66\u62A5\u8B66\u4FE1\u606Fcode
yushi.handAlarmMessage.service.code=ysHandAlarmMessage
#\u5B87\u89C6\u89C6\u9891\u5206\u6790\u62A5\u8B66\u62A5\u8B66\u4FE1\u606Fcode
yushi.videoAnalysisAlarmMessage.service.code=ysVideoAnalysisAlarmMessage
#\u5B87\u89C6\uFF08\u624B\u52A8\u62A5\u8B66\uFF09\u8D44\u6E90\u57FA\u7840\u4FE1\u606Fcode
yushi.handEquInfo.service.code=ysHandEquInfo
#\u5B87\u89C6\uFF08\u624B\u52A8\u62A5\u8B66\uFF09\u8D44\u6E90\u57FA\u7840\u4FE1\u606F \u63A5\u53E3\u5730\u5740
yushi.handEquInfo.url=http://************:8088/VIID/query
#\u5B87\u89C6\uFF08\u89C6\u9891\u5206\u6790\uFF09\u62A5\u8B66\u7C7B\u578B
yushi.EventType=39100,39101,309,39105,39104
#\u5B87\u89C6\uFF08\u624B\u52A8\u62A5\u8B66\uFF09\u624B\u52A8\u62A5\u8B66\u7C7B\u578B
yushi.handEventType=20001
#\u7EC4\u7EC7\u7F16\u7801
yushi.urlOrg.url=org@34029#20200811172411
#gis\u5750\u6807\u540C\u6B65url
gis.request.url=http://10.30.3.5:8080/GISW1GisService/incrementRest/gisdata.do?systemCode=GIS&dataTheme=sodb&outsr=berghaus
#gis.layer.config.path=/server/dtsServer/hkSodbRecv/config/gisLayer.json
gis.layer.config.path=D:\\server\\hkSodbRecv\\config\\gisLayer.json
