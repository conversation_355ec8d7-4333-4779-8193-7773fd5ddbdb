package com.air.security.sodb.dts.receive.job;

import com.air.security.sodb.data.core.base.Meta;
import com.air.security.sodb.data.core.constant.GlobalCodeConstant;
import com.air.security.sodb.data.core.constant.MsgIdConstant;
import com.air.security.sodb.data.core.exception.SystemBaseException;
import com.air.security.sodb.data.core.quartz.AbstractJob;
import com.air.security.sodb.data.core.util.DateTimeUtil;
import com.air.security.sodb.data.core.util.HaLog;
import com.air.security.sodb.data.core.util.PropertyUtil;
import com.air.security.sodb.data.core.util.UuidUtil;
import com.air.security.sodb.dts.receive.server.MqMessageRecvServer;
import com.air.security.sodb.dts.receive.server.MqMessageRecvServerImpl;
import com.air.security.sodb.dts.receive.util.CertificationInfoUtil;
import com.air.security.sodb.dts.receive.util.HkEquStatus;
import com.air.security.sodb.dts.receive.util.HkEquStatusCache;
import com.alibaba.fastjson.JSONObject;
import org.apache.commons.lang3.StringUtils;
import org.quartz.JobDataMap;
import org.slf4j.Logger;
import org.slf4j.LoggerFactory;

import java.io.IOException;
import java.util.List;

/**
 * 防区（手动报警）资源变更信息获取任务
 *
 * <AUTHOR>
 */
public class HandEquStateJob extends AbstractJob {

    private static final Logger log = LoggerFactory.getLogger(HandEquStateJob.class);

    private static String handEquState = PropertyUtil.getProperty("hk.handEquState.service.code");

    private static String url = PropertyUtil.getProperty("hk.handEquState.url");

    /**
     *
     */
    private MqMessageRecvServer server = new MqMessageRecvServerImpl();

    @Override
    public void executeJob(JobDataMap paramMap) {
        HaLog.info(log, MsgIdConstant.MS_INF_0001, "获取防区（手动报警）资源变更信息定时任务");

        try {
            execPost();
        } catch (Exception e) {
            throw new SystemBaseException("获取防区（手动报警）资源变更信息失败", e);
        } finally {
            HaLog.info(log, MsgIdConstant.MS_INF_0002, "获取防区（手动报警）资源变更信息定时任务");
        }

    }

    /**
     * @throws IOException
     */
    private void execPost() {
        String[] arr = new String[1];
        JSONObject jsonObject = new JSONObject();
        //编辑消息头
        Meta meta = new Meta();
        meta.setEventType(handEquState);
        meta.setRecvSequence(UuidUtil.getUuid32());
        //查询数据库中的资源编码和状态
        List<HkEquStatus> cacheList = HkEquStatusCache.getCacheList();
        for (int i = 0; i < cacheList.size(); i++) {
            HkEquStatus hkEquStatus = cacheList.get(i);
            //资源编码
            String equCode = hkEquStatus.getEquCode();
            arr[0] = equCode;
            //资源状态
            String timeStateId = hkEquStatus.getTimeStateId();
            jsonObject.put("defenceIndexCodes", arr);

            //查询对应的状态
            String message = CertificationInfoUtil.send(url, jsonObject.toString());
            JSONObject result = JSONObject.parseObject(message);
            String total = result.getJSONObject("data").getString("total");
            if ("0".equals(total)) {
                continue;
            }
            JSONObject json = (JSONObject) result.getJSONObject("data").getJSONArray("list").get(0);
            String onlineStatus = json.getString("status");
            //资源编码转换
            onlineStatus = formatStatus(onlineStatus);
            if (!StringUtils.equals(timeStateId, onlineStatus)) {
                HkEquStatusCache.getCacheList().get(i).setTimeStateId(onlineStatus);
                json.put("operateTime", DateTimeUtil.getCurrentTimestampStr("yyyy-MM-dd HH:mm:ss"));
                json.put("timeStateId", onlineStatus);
                server.handle(meta, json.toString());
            }
        }
    }

    /**
     * 资源编码转换
     *
     * @param onlineStatus 状态
     * @return
     */
    private String formatStatus(String onlineStatus) {
        if (GlobalCodeConstant.MINUSONE.equalsIgnoreCase(onlineStatus)) {
            onlineStatus = "ES02";
        }
        if (GlobalCodeConstant.ZERO.equalsIgnoreCase(onlineStatus)) {
            onlineStatus = "ES02";
        }
        if (GlobalCodeConstant.ONE.equalsIgnoreCase(onlineStatus)) {
            onlineStatus = "ES01";
        }
        if (GlobalCodeConstant.TWO.equalsIgnoreCase(onlineStatus)) {
            onlineStatus = "ES03";
        }
        if (GlobalCodeConstant.THREE.equalsIgnoreCase(onlineStatus)) {
            onlineStatus = "ES01";
        }
        if (GlobalCodeConstant.FOUR.equalsIgnoreCase(onlineStatus)) {
            onlineStatus = "ES02";
        }
        return onlineStatus;
    }


}
