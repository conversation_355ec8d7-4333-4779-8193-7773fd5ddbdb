package com.air.security.sodb.dts.srvc.restsrvc.receive.alarm;

import com.air.security.sodb.data.core.base.Meta;
import com.air.security.sodb.data.core.constant.MsgIdConstant;
import com.air.security.sodb.data.core.util.HaLog;
import com.air.security.sodb.data.core.util.PropertyUtil;
import com.air.security.sodb.data.core.util.RequestUtil;
import com.air.security.sodb.data.core.util.UuidUtil;
import com.air.security.sodb.dts.receive.server.MqMessageRecvServer;
import com.air.security.sodb.dts.receive.server.MqMessageRecvServerImpl;
import com.alibaba.fastjson.JSONObject;
import org.slf4j.Logger;
import org.slf4j.LoggerFactory;
import org.springframework.web.bind.annotation.CrossOrigin;
import org.springframework.web.bind.annotation.RequestMapping;
import org.springframework.web.bind.annotation.RequestMethod;
import org.springframework.web.bind.annotation.RestController;

import javax.servlet.http.HttpServletRequest;
import java.util.Arrays;
import java.util.Collections;
import java.util.List;
import java.util.stream.Collectors;

/**
 * @Description: 宇视报警（手动报警报警）
 * @author: mk
 * @Date: 2020-08-13
 */
@CrossOrigin
@RestController
@RequestMapping("/api/release/ysHandAlarmMessage")
public class YsHandAlarmMessageSaveRestController {

    private static final Logger log = LoggerFactory.getLogger(YsHandAlarmMessageSaveRestController.class);

    private static String ysHandAlarmMessage = PropertyUtil.getProperty("yushi.handAlarmMessage.service.code");

    private static String ysVideoAnalysisAlarmMessage = PropertyUtil.getProperty("yushi.videoAnalysisAlarmMessage.service.code");

    private static String ysEventType = PropertyUtil.getProperty("yushi.EventType");
    private static String yshandEventType = PropertyUtil.getProperty("yushi.handEventType");

    /**
     *
     */
    private MqMessageRecvServer server = new MqMessageRecvServerImpl();

    /**
     * 接口
     */
    @RequestMapping(value = "/execute", method = RequestMethod.POST)
    public void execute(HttpServletRequest request) {

        JSONObject jsonObj = RequestUtil.requestGetJson(request);
        HaLog.info(log, MsgIdConstant.MS_INF_0001, "宇视报警接入接口");
        try {
            // 执行业务处理
            if (null != jsonObj) {
                // 执行业务处理
                this.execute(jsonObj);
            }
            HaLog.info(log, MsgIdConstant.MS_INF_0002, "宇视报警接入接口");
        } catch (Exception e) {
            HaLog.error(log, e, MsgIdConstant.MS_ERR_0001);
        }

    }

    /**
     * 执行业务处理
     *
     * @param jsonObj
     */
    private void execute(JSONObject jsonObj) {
        String eventType = jsonObj.getString("EventType");
        HaLog.info(log, MsgIdConstant.MS_INF_0001, jsonObj.toString());
        //判断是否为手动报警类型
        if (yshandEventType.equals(eventType)) {
            HaLog.info(log, MsgIdConstant.MS_INF_0001, "宇视报警（手动报警报警）接入接口");
            Meta meta = new Meta();
            meta.setEventType(ysHandAlarmMessage);
            meta.setRecvSequence(UuidUtil.getUuid32());
            server.handle(meta, jsonObj.toString());
            HaLog.info(log, MsgIdConstant.MS_INF_0002, "宇视报警（手动报警报警）接入接口");
        }
        //判断是否为视频分析报警类型
        List<String> listIds = Arrays.asList(ysEventType.split(",")).stream().map(s -> s.trim()).collect(Collectors.toList());
        int frequency = Collections.frequency(listIds, eventType);
        if (frequency > 0) {
            HaLog.info(log, MsgIdConstant.MS_INF_0001, "宇视报警（视频分析报警）接入接口");
            Meta meta = new Meta();
            meta.setEventType(ysVideoAnalysisAlarmMessage);
            meta.setRecvSequence(UuidUtil.getUuid32());
            server.handle(meta, jsonObj.toString());
            HaLog.info(log, MsgIdConstant.MS_INF_0002, "宇视报警（视频分析报警）接入接口");
        }
    }

}
