package com.air.security.sodb.dts.srvc.restsrvc.receive.alarm;

import com.air.security.sodb.data.core.base.ReceiveErrorType;
import com.air.security.sodb.data.core.constant.GlobalCodeConstant;
import com.air.security.sodb.data.core.constant.MsgIdConstant;
import com.air.security.sodb.data.core.util.*;
import com.air.security.sodb.dts.receive.util.ResponseDTO;
import com.air.security.sodb.dts.receive.util.YsHelper;
import com.alibaba.fastjson.JSONObject;
import io.swagger.annotations.ApiOperation;
import org.slf4j.Logger;
import org.slf4j.LoggerFactory;
import org.springframework.web.bind.annotation.RequestMapping;
import org.springframework.web.bind.annotation.RequestMethod;
import org.springframework.web.bind.annotation.RestController;

import javax.servlet.http.HttpServletRequest;
import java.util.Map;

/**
 * 宇视告警订阅
 *
 * <AUTHOR>
 */
@RestController
@RequestMapping("/api/release/subscribe")
public class YsAlarmResgisterReceiveRestController {

    private static final Logger log = LoggerFactory.getLogger(YsAlarmResgisterReceiveRestController.class);

    private static String alarmSubscribeUrl = PropertyUtil.getProperty("yushi.alarm.subscribe");

    /**
     * 宇视报警接入
     *
     * @param request
     * @return
     */
    @RequestMapping(value = "/execute", method = RequestMethod.POST)
    @ApiOperation(value = "宇视报警接口注册")
    public Object alarm(HttpServletRequest request) {

        JSONObject jsonObj = RequestUtil.requestGetJson(request);
        HaLog.info(log, MsgIdConstant.MS_INF_0003, "宇视报警接口注册", jsonObj.toJSONString());
        ResponseDTO<JSONObject> responseDto = new ResponseDTO<JSONObject>();

        try {

            String data = jsonObj.getString("Data");
            int type = jsonObj.getIntValue("Type");

            JSONObject subscribeParms = new JSONObject();

            // 宇视告警数据接收服务地址
            subscribeParms.put("Data", data);

            // 宇视告警数据类型
            subscribeParms.put("Type", type);

            Map<String, String> headers = MapUtil.getHashMap(1);
            headers.put("Authorization", YsHelper.getAccessToken());
            String subscribeResult = HttpRequestUtil.sendPost(alarmSubscribeUrl, headers, subscribeParms);
            HaLog.infoJson(log, "告警订阅结果：" + subscribeResult);

            JSONObject subscribeObj = JSONObject.parseObject(subscribeResult);

            HaLog.info(log, MsgIdConstant.MS_INF_0002, "宇视告警订阅接口注册");
            return subscribeObj;
        } catch (Exception e) {
            responseDto.failed(ReceiveErrorType.SYSTEM_ERROR.getCode(), ReceiveErrorType.SYSTEM_ERROR.getMessage(),
                    "", GlobalCodeConstant.SYS_CODE_SAEMS);
            HaLog.error(log, e, MsgIdConstant.MS_ERR_0001, "宇视告警订阅接口注册");
            return responseDto;
        }

    }

}
