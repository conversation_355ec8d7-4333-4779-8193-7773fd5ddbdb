package com.air.security.sodb.dts.receive.job;

import com.air.security.sodb.data.core.base.Meta;
import com.air.security.sodb.data.core.constant.MsgIdConstant;
import com.air.security.sodb.data.core.exception.SystemBaseException;
import com.air.security.sodb.data.core.quartz.AbstractJob;
import com.air.security.sodb.data.core.util.HaLog;
import com.air.security.sodb.data.core.util.HttpRequestUtil;
import com.air.security.sodb.data.core.util.PropertyUtil;
import com.air.security.sodb.data.core.util.UuidUtil;
import com.air.security.sodb.dts.receive.server.MqMessageRecvServer;
import com.air.security.sodb.dts.receive.server.MqMessageRecvServerImpl;
import com.air.security.sodb.dts.receive.util.SrvcHelper;
import com.air.security.sodb.dts.receive.util.YsHelper;
import com.alibaba.fastjson.JSONArray;
import com.alibaba.fastjson.JSONObject;
import org.quartz.JobDataMap;
import org.slf4j.Logger;
import org.slf4j.LoggerFactory;

import java.io.IOException;
import java.io.UnsupportedEncodingException;
import java.net.URLEncoder;

/**
 * 宇视手动报警资源信息
 *
 * <AUTHOR>
 */
public class YsHandEquInfoJob extends AbstractJob {

    private static final Logger log = LoggerFactory.getLogger(YsHandEquInfoJob.class);

    private static String yshandEquInfo = PropertyUtil.getProperty("yushi.handEquInfo.service.code");

    private static String url = PropertyUtil.getProperty("yushi.handEquInfo.url");

    private static String urlOrg = PropertyUtil.getProperty("yushi.urlOrg.url");

    private static int rowNum = 0;

    private static int totalRowNum = 0;

    private static int count = 0;

    /**
     *
     */
    private MqMessageRecvServer server = new MqMessageRecvServerImpl();


    @Override
    public void executeJob(JobDataMap paramMap) {
        HaLog.info(log, MsgIdConstant.MS_INF_0001, "获取宇视手动报警资源信息定时任务");

        try {
            int i = 0;
            execPost(url, SrvcHelper.PAGE_SIZE, 0, i);
        } catch (Exception e) {
            throw new SystemBaseException("获取防区（手动报警）资源信息失败", e);
        } finally {
            HaLog.info(log, MsgIdConstant.MS_INF_0002, "获取宇视手动报警资源信息定时任务");
        }

    }

    /**
     * @param url
     * @param pageSize
     * @param pageNo
     * @throws IOException
     */
    private void execPost(String url, int pageSize, int pageNo, int i) throws IOException {
        String msg = getMsg(pageSize, pageNo);
        String accessToken = YsHelper.getAccessToken();
        String result = HttpRequestUtil.sendGet(url, msg, accessToken);
        HaLog.infoJson(log, "宇视手动报警资源结果：" + result);
        JSONObject subscribeObj = JSONObject.parseObject(result);

        if (i == 0) {
            rowNum = Integer.parseInt(subscribeObj.getJSONObject("Result").getJSONObject("RspPageInfo").getString("RowNum"));
            totalRowNum = Integer.parseInt(subscribeObj.getJSONObject("Result").getJSONObject("RspPageInfo").getString("TotalRowNum"));
            count = totalRowNum % rowNum == 0 ? totalRowNum / rowNum - 1 : totalRowNum / rowNum;
        }
        if (totalRowNum > 0) {
            JSONObject jsonObject = JSONObject.parseObject(subscribeObj.toString());
            JSONArray jsonJsonArray = jsonObject.getJSONObject("Result").getJSONArray("InfoList");
            for (Object o : jsonJsonArray) {
                JSONObject json = (JSONObject) o;
                JSONObject jsonbject = json.getJSONObject("ResItemV1");
                Meta meta = new Meta();
                meta.setEventType(yshandEquInfo);
                meta.setRecvSequence(UuidUtil.getUuid32());
                server.handle(meta, jsonbject.toString());
            }
        }
        while (i < count) {
            i++;
            this.execPost(url, SrvcHelper.PAGE_SIZE, i * SrvcHelper.PAGE_SIZE, i);
        }

    }

    /**
     * @param pageSize
     * @param pageNo
     */
    private static String getMsg(int pageSize, int pageNo) throws UnsupportedEncodingException {
        JSONObject jsonObject = new JSONObject();
        jsonObject.put("ItemNum", 1);
        JSONArray array = new JSONArray();
        JSONObject json = new JSONObject();
        json.put("QueryType", 256);
        json.put("LogicFlag", 0);
        json.put("QueryData", "20000");
        array.add(json);
        jsonObject.put("Condition", array);
        jsonObject.put("QueryCount", 1);
        jsonObject.put("PageFirstRowNumber", pageNo);
        jsonObject.put("PageRowNum", pageSize);
        String msg = jsonObject.toString();
        System.out.println(msg);
        String encode = "condition=" + URLEncoder.encode(msg, "UTF-8");
        encode += "&org=" + URLEncoder.encode(urlOrg, "UTF-8");
        return encode;
    }

    public static void main(String[] args) throws UnsupportedEncodingException {


    }


}
