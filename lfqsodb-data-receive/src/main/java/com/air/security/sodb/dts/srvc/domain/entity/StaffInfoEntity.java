package com.air.security.sodb.dts.srvc.domain.entity;


import io.swagger.annotations.ApiModelProperty;
import org.springframework.format.annotation.DateTimeFormat;

import javax.persistence.Column;
import javax.persistence.Id;
import javax.persistence.Table;
import java.util.Date;

/**
 * <AUTHOR>
 * @Description:人员信息实体
 * @date 2022-06-18
 */
@Table(name = "SODB_STAFF_INFO")
public class StaffInfoEntity {

    /**
     * 主键
     */
    @Id
    @Column(name = "UUID")
    @ApiModelProperty(value = "主键ID")
    private String uuid;

    /**
     * 人员原始主键
     */
    @Column(name = "STAFF_UUID")
    @ApiModelProperty(value = "人员原始主键")
    private String staffUuid;

    /**
     * 人员编码
     */
    @Column(name = "STAFF_CODE")
    @ApiModelProperty(value = "人员编码")
    private String staffCode;

    /**
     * 人员名称
     */
    @Column(name = "STAFF_NAME")
    @ApiModelProperty(value = "人员名称")
    private String staffName;

    /**
     * 邮箱
     */
    @Column(name = "EMAIL")
    @ApiModelProperty(value = "邮箱")
    private String email;

    /**
     * 性别 1-男 2-女 0-未知
     */
    @Column(name = "SEX")
    @ApiModelProperty(value = "性别 1-男 2-女 0-未知")
    private String sex;

    /**
     * 拼音
     */
    @Column(name = "SPELL")
    @ApiModelProperty(value = "拼音")
    private String spell;

    /**
     * 证件类型 0-身份证 1-卡号
     */
    @Column(name = "CARD_TYPE")
    @ApiModelProperty(value = "证件类型 0-身份证 1-卡号")
    private String cardType;

    /**
     * 证件号
     */
    @Column(name = "ID_CARD")
    @ApiModelProperty(value = "证件号")
    private String idCard;

    /**
     * 手机号
     */
    @Column(name = "TELEPHONE")
    @ApiModelProperty(value = "手机号")
    private String telephone;

    /**
     * 地址
     */
    @Column(name = "ADDRESS")
    @ApiModelProperty(value = "地址")
    private String address;

    /**
     * 部门编码（数据字典配置）
     */
    @Column(name = "DEPART_CODE")
    @ApiModelProperty(value = "部门编码（数据字典配置）")
    private String departCode;

    /**
     * 部门名称（数据字典配置）
     */
    @Column(name = "DEPART_NAME")
    @ApiModelProperty(value = "部门名称（数据字典配置）")
    private String departName;

    /**
     * 是否删除 0-否 1-是
     */
    @Column(name = "DELETE_FLAG")
    @ApiModelProperty(value = "是否删除 0-否 1-是")
    private String deleteFlag;

    /**
     * 创建人
     */
    @Column(name = "CREATE_BY")
    @ApiModelProperty(value = "创建人")
    private String createBy;

    /**
     * 创建时间
     */
    @Column(name = "CREATE_DATE")
    @DateTimeFormat(pattern = "yyyy-MM-dd HH:mm:ss")
    @ApiModelProperty(value = "创建时间")
    private Date createDate;

    /**
     * 更新人
     */
    @Column(name = "UPDATE_BY")
    @ApiModelProperty(value = "更新人")
    private String updateBy;

    /**
     * 更新时间
     */
    @Column(name = "UPDATE_DATE")
    @DateTimeFormat(pattern = "yyyy-MM-dd HH:mm:ss")
    @ApiModelProperty(value = "更新时间")
    private Date updateDate;

    public String getUuid() {
        return uuid;
    }

    public void setUuid(String uuid) {
        this.uuid = uuid;
    }

    public String getStaffUuid() {
        return staffUuid;
    }

    public void setStaffUuid(String staffUuid) {
        this.staffUuid = staffUuid;
    }

    public String getStaffCode() {
        return staffCode;
    }

    public void setStaffCode(String staffCode) {
        this.staffCode = staffCode;
    }

    public String getStaffName() {
        return staffName;
    }

    public void setStaffName(String staffName) {
        this.staffName = staffName;
    }

    public String getEmail() {
        return email;
    }

    public void setEmail(String email) {
        this.email = email;
    }

    public String getSex() {
        return sex;
    }

    public void setSex(String sex) {
        this.sex = sex;
    }

    public String getSpell() {
        return spell;
    }

    public void setSpell(String spell) {
        this.spell = spell;
    }

    public String getCardType() {
        return cardType;
    }

    public void setCardType(String cardType) {
        this.cardType = cardType;
    }

    public String getIdCard() {
        return idCard;
    }

    public void setIdCard(String idCard) {
        this.idCard = idCard;
    }

    public String getTelephone() {
        return telephone;
    }

    public void setTelephone(String telephone) {
        this.telephone = telephone;
    }

    public String getAddress() {
        return address;
    }

    public void setAddress(String address) {
        this.address = address;
    }

    public String getDepartCode() {
        return departCode;
    }

    public void setDepartCode(String departCode) {
        this.departCode = departCode;
    }

    public String getDepartName() {
        return departName;
    }

    public void setDepartName(String departName) {
        this.departName = departName;
    }

    public String getDeleteFlag() {
        return deleteFlag;
    }

    public void setDeleteFlag(String deleteFlag) {
        this.deleteFlag = deleteFlag;
    }

    public String getCreateBy() {
        return createBy;
    }

    public void setCreateBy(String createBy) {
        this.createBy = createBy;
    }

    public Date getCreateDate() {
        return createDate;
    }

    public void setCreateDate(Date createDate) {
        this.createDate = createDate;
    }

    public String getUpdateBy() {
        return updateBy;
    }

    public void setUpdateBy(String updateBy) {
        this.updateBy = updateBy;
    }

    public Date getUpdateDate() {
        return updateDate;
    }

    public void setUpdateDate(Date updateDate) {
        this.updateDate = updateDate;
    }

    @Override
    public String toString() {
        return "StaffInfoEntity{" +
                "uuid='" + uuid + '\'' +
                ", staffUuid='" + staffUuid + '\'' +
                ", staffCode='" + staffCode + '\'' +
                ", staffName='" + staffName + '\'' +
                ", email='" + email + '\'' +
                ", sex='" + sex + '\'' +
                ", spell='" + spell + '\'' +
                ", cardType='" + cardType + '\'' +
                ", idCard='" + idCard + '\'' +
                ", telephone='" + telephone + '\'' +
                ", address='" + address + '\'' +
                ", departCode='" + departCode + '\'' +
                ", departName='" + departName + '\'' +
                ", deleteFlag='" + deleteFlag + '\'' +
                ", createBy='" + createBy + '\'' +
                ", createDate=" + createDate +
                ", updateBy='" + updateBy + '\'' +
                ", updateDate=" + updateDate +
                '}';
    }
}
