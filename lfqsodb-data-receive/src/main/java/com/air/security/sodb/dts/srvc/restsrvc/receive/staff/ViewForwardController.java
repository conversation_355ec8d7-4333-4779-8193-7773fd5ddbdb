package com.air.security.sodb.dts.srvc.restsrvc.receive.staff;


import com.air.security.sodb.data.core.base.Meta;
import com.air.security.sodb.data.core.constant.MsgIdConstant;
import com.air.security.sodb.data.core.mq.MyProducer;
import com.air.security.sodb.data.core.mq.impl.SyncRocketMQProducer;
import com.air.security.sodb.data.core.util.HaLog;
import com.air.security.sodb.data.core.util.UuidUtil;
import com.air.security.sodb.dts.receive.server.MqMessageRecvServer;
import com.air.security.sodb.dts.receive.server.MqMessageRecvServerImpl;
import com.air.security.sodb.dts.srvc.restsrvc.dto.GaDeviceDTO;
import com.air.security.sodb.dts.srvc.restsrvc.vo.ResponseStatusVO;
import com.alibaba.fastjson.JSON;
import com.alibaba.fastjson.JSONObject;
import org.slf4j.Logger;
import org.slf4j.LoggerFactory;
import org.springframework.web.bind.annotation.*;

import javax.servlet.http.HttpServletRequest;

@CrossOrigin
@RestController
@RequestMapping("/api/release")
public class ViewForwardController {

    /**
     * 日志
     */
    private static final Logger logger = LoggerFactory.getLogger(ViewForwardController.class);

    private MyProducer producer = new SyncRocketMQProducer();

    /**
     * 注册接口
     *
     * @param request  request
     * @param gaDevice gaDevice
     * @return
     */
    @PostMapping(value = "/VIID/System/Register")
    public ResponseStatusVO register(HttpServletRequest request, @RequestBody GaDeviceDTO gaDevice) {
        String host = request.getRemoteHost();
        String addr = request.getLocalAddr();
        logger.info(host + " register " + addr);
        logger.info(JSON.toJSONString(gaDevice));
        return new ResponseStatusVO("/VIID/System/Register");
    }

    /**
     * 保活接口
     *
     * @param request  request
     * @param gaDevice gaDevice
     * @return
     */
    @PostMapping(value = "/VIID/System/Keepalive")
    public ResponseStatusVO keepAlive(HttpServletRequest request
            , @RequestBody GaDeviceDTO gaDevice) {
        String host = request.getRemoteHost();
        String addr = request.getLocalAddr();
        logger.info(host + " keepAlive " + addr);
        logger.info(JSON.toJSONString(gaDevice));
        return new ResponseStatusVO("/VIID/System/Keepalive");
    }

    /**
     * 通知接受接口
     *
     * @param request                  request
     * @param strSubscribeNotification strSubscribeNotification
     * @return
     */

    @PostMapping(value = "/VIID/SubscribeNotifications")
    public ResponseStatusVO notifications(HttpServletRequest request,
                                          @RequestBody String strSubscribeNotification) {
        HaLog.info(logger, MsgIdConstant.MS_INF_0001, "员工刷卡数据信息接入接口");
        ResponseStatusVO responseStatus = new ResponseStatusVO();
        try {
            MqMessageRecvServer server = new MqMessageRecvServerImpl(producer);
            JSONObject jsonObject = JSONObject.parseObject(strSubscribeNotification);
            logger.debug("8>>>>>"+jsonObject.toString());
            Meta meta = new Meta();
            meta.setEventType("YS_RECORD");
            meta.setRecvSequence(UuidUtil.getUuid32());
            server.handle(meta, jsonObject.toString());
            responseStatus = new ResponseStatusVO("/VIID/SubscribeNotifications",
                    0, "正常");
            return responseStatus;
        } catch (Exception e) {
            responseStatus = new ResponseStatusVO("/VIID/SubscribeNotifications",
                    1, "其他未知错误");
        }
        return responseStatus;
    }
}
