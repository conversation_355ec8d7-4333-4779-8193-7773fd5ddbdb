package com.air.security.sodb.dts.srvc.restsrvc.dto;

import com.alibaba.fastjson.annotation.JSONField;

public class GaDeviceDTO {
    
    public GaDeviceDTO() {
    }

    public GaDeviceDTO(String deviceID) {
        this.deviceID = deviceID;
    }

    @J<PERSON><PERSON>ield(name = "DeviceID")
    private String deviceID;

    @Override
    public String toString() {
        return "GaDevice{" +
                "deviceID='" + deviceID + '\'' +
                '}';
    }

    public String getDeviceID() {
        return deviceID;
    }

    public void setDeviceID(String deviceID) {
        this.deviceID = deviceID;
    }
}
