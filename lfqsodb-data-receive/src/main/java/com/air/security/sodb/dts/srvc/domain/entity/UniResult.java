package com.air.security.sodb.dts.srvc.domain.entity;

public class UniResult {

    /**
     * 错误信息码
     */
    private Integer ErrCode;
    /**
     * 错误信息
     */
    private String ErrMsg;
    /**
     * 数据信息
     */
    private Object data;

    public UniResult() {
    }

    public UniResult(Integer ErrCode, String ErrMsg) {
        this.ErrCode = ErrCode;
        this.ErrMsg = ErrMsg;
    }

    public Object getData() {
        return data;
    }

    public void setData(Object data) {
        this.data = data;
    }

    public Integer getErrCode() {
        return ErrCode;
    }

    public void setErrCode(Integer errCode) {
        ErrCode = errCode;
    }

    public String getErrMsg() {
        return ErrMsg;
    }

    public void setErrMsg(String errMsg) {
        ErrMsg = errMsg;
    }

    @Override
    public String toString() {
        return "UniResult{" +
                "ErrCode=" + ErrCode +
                ", ErrMsg='" + ErrMsg + '\'' +
                ", data=" + data +
                '}';
    }


}