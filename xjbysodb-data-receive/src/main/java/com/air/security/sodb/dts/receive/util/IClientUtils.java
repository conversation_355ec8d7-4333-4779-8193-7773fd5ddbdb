package com.air.security.sodb.dts.receive.util;

import com.air.security.sodb.data.core.util.PropertyUtil;
import com.air.security.sodb.dts.srvc.restsrvc.platform.Response;
import com.air.security.sodb.dts.srvc.restsrvc.platform.car.dto.CarReservationVO;
import com.air.security.sodb.dts.srvc.restsrvc.platform.car.vo.RequestParamVO;
import com.air.security.sodb.dts.srvc.restsrvc.platform.equ.vo.ChannelQueryVO;
import com.air.security.sodb.dts.srvc.restsrvc.platform.equ.vo.DeviceQueryVO;
import com.alibaba.fastjson.JSON;
import com.alibaba.fastjson.JSONObject;
import com.dahuatech.hutool.http.Method;
import com.dahuatech.icc.exception.ClientException;
import com.dahuatech.icc.oauth.http.DefaultClient;
import com.dahuatech.icc.oauth.http.IClient;
import com.dahuatech.icc.oauth.model.v202010.GeneralRequest;
import com.dahuatech.icc.oauth.model.v202010.GeneralResponse;
import org.apache.commons.lang3.StringUtils;
import org.springframework.context.annotation.Bean;
import org.springframework.stereotype.Component;

import java.io.UnsupportedEncodingException;
import java.net.URLEncoder;
import java.util.List;
import java.util.Map;

@Component
public class IClientUtils {

    private static final String VERSION = "1.2.0";

    private static final String EUQ_CHANNEL_URL = "/evo-apigw/evo-brm/" + VERSION + "/device/channel/subsystem/page";

    private static final String EUQ_HAND_URL = "/evo-apigw/evo-brm/" + VERSION + "/device/subsystem/page";

    private static final String EVENT_SUBSCRIPTION_URL = "/evo-apigw/evo-event/1.0.0/subscribe/mqinfo";

    private static final String EVENT_UN_SUBSCRIPTION_URL = "/evo-apigw/evo-event/1.0.0/subscribe/mqinfo";

    private static final String SUBSCRIBE_LIST_URL = "/evo-apigw/evo-event/1.0.0/subscribe/subscribe-list";

    private static final String WHITE_CAR_URL = "/evo-apigw/ipms/carWhiteList/sendCars";

    private static final String BLACK_CAR_URL = "/evo-apigw/ipms/carBlackList/sendCars";

    private static String host = PropertyUtil.getProperty("icc.sdk.host");
    private static String clientId = PropertyUtil.getProperty("icc.sdk.clientId");
    private static String clientSecret = PropertyUtil.getProperty("icc.sdk.clientSecret");
    private static String username = PropertyUtil.getProperty("icc.sdk.username");
    private static String password = PropertyUtil.getProperty("icc.sdk.password");


    @Bean
    public IClient iccDefaultClient() throws ClientException {
        return new DefaultClient(host, username, password,
                clientId, clientSecret);
    }

    public static GeneralResponse getMsgs(String url, Method method, String json, Map<String, Object> forms, Map<String, String> herders) throws ClientException {
        IClient iClient = new DefaultClient();
        iClient.getAccessToken();
        GeneralRequest generalRequest = new GeneralRequest(url, method);
        // set http post body
        if (StringUtils.isNotBlank(json)) {
            generalRequest.body(json);
        }
        if (forms != null && forms.size() > 0) {
            for (Map.Entry<String, Object> stringStringEntry : forms.entrySet()) {
                generalRequest.form(stringStringEntry.getKey(), stringStringEntry.getValue());
            }
        }
        if (herders != null && herders.size() > 0) {
            for (Map.Entry<String, String> stringStringEntry : herders.entrySet()) {
                generalRequest.header(stringStringEntry.getKey(), stringStringEntry.getValue());
            }
        }

        // 发起请求处理应答或异常。
        GeneralResponse generalResponse = iClient.doAction(generalRequest, generalRequest.getResponseClass());

        return generalResponse;
    }

    /**
     * 获取门禁资源数据
     */
    public static Response getEquChannelList(ChannelQueryVO vo) throws ClientException {
        String jsonString = JSON.toJSONString(vo);
        GeneralResponse msgs = getMsgs(EUQ_CHANNEL_URL, Method.POST, jsonString, null, null);
        String result = msgs.getResult();
        Response response = JSON.parseObject(result, Response.class);
        return response;
    }

    /**
     * 获取大华隐蔽报警资源
     */
    public static Response getEquHandList(DeviceQueryVO vo) throws ClientException {
        String jsonString = JSON.toJSONString(vo);
        GeneralResponse msgs = getMsgs(EUQ_HAND_URL, Method.POST, jsonString, null, null);
        String result = msgs.getResult();
        Response response = JSON.parseObject(result, Response.class);
        return response;
    }

    /**
     * 获取车辆白名单数据
     */
    public static Response getWhiteCarList(RequestParamVO vo) throws ClientException {
        String jsonString = JSON.toJSONString(vo);
        Map<String, Object> map = (Map<String, Object>) JSON.parse(jsonString);
        GeneralResponse msgs = getMsgs(WHITE_CAR_URL, Method.GET, null, map, null);
        String result = msgs.getResult();
        Response response = JSON.parseObject(result, Response.class);
        return response;
    }

    /**
     * 获取车辆黑名单数据
     */
    public static Response getBlackCarList(RequestParamVO vo) throws ClientException {
        String jsonString = JSON.toJSONString(vo);
        Map<String, Object> map = (Map<String, Object>) JSON.parse(jsonString);
        GeneralResponse msgs = getMsgs(BLACK_CAR_URL, Method.GET, null, map, null);
        String result = msgs.getResult();
        Response response = JSON.parseObject(result, Response.class);
        return response;
    }

    /**
     * 订阅数据
     */
    public static Response eventSubscription(JSONObject jsonObj) throws ClientException {
        String jsonString = JSON.toJSONString(jsonObj);
        GeneralResponse msgs = getMsgs(EVENT_SUBSCRIPTION_URL, Method.POST, jsonString, null, null);
        String result = msgs.getResult();
        Response response = JSON.parseObject(result, Response.class);
        return response;
    }

    /**
     * 取消订阅数据
     */
    public static String eventUnSubscription(JSONObject jsonObj) throws ClientException, UnsupportedEncodingException {
        IClient iClient = new DefaultClient();
        // 事件订阅按`name`字段取消
        String dd = URLEncoder.encode(jsonObj.getString("name"), "UTF-8");
        GeneralRequest request = new GeneralRequest
                (EVENT_UN_SUBSCRIPTION_URL + "?name=" +
                        dd, Method.DELETE);
        GeneralResponse response = iClient.doAction(request, request.getResponseClass());
        String result = response.getResult();
        return result;
    }

    /**
     * 订阅列表
     */
    public static String subscribeList(JSONObject jsonObj) throws ClientException {
        Map<String, Object> map = jsonObj;
        GeneralResponse msgs = getMsgs(SUBSCRIBE_LIST_URL, Method.GET, null, map, null);
        String result = msgs.getResult();
        return result;
    }

    public static void main(String[] args) {
        String result = "";
        Response response = JSON.parseObject(result, Response.class);
        Integer currentPage = response.getData().getCurrentPage();
        System.out.println(currentPage);
        List<CarReservationVO> pageData = JSON.parseArray(JSON.toJSONString(response.getData().getPageData()), CarReservationVO.class);
        for (CarReservationVO pageDatum : pageData) {
            System.out.println(pageDatum.getId());
        }
    }


}
