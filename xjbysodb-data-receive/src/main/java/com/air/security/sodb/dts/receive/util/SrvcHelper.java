package com.air.security.sodb.dts.receive.util;

import com.air.security.sodb.data.core.util.PropertyUtil;
import com.alibaba.fastjson.JSONObject;

/**
 * <AUTHOR>
 */
public class SrvcHelper {

    /**
     *
     */
    public static final int PAGE_SIZE = getPageSize();

    private static final int DEFAULT_PAGE_SIZE = 20;

    /**
     * 大华服务ip:port
     */
    public static final String DH_SRVC_HOST = PropertyUtil.getProperty("rest.dh.host");

    /**
     * 获取分页长度
     *
     * @return
     */
    public static int getPageSize() {
        try {
            String pageSize = PropertyUtil.getProperty("rest.dh.pageSize");
            return Integer.parseInt(pageSize);
        } catch (Exception e) {
            return DEFAULT_PAGE_SIZE;
        }
    }

    /**
     * 获取分页参数
     *
     * @param pageNum
     * @param pageSize
     * @return
     */
    public static JSONObject getPageParam(int pageNum, int pageSize) {
        JSONObject jsonObject = new JSONObject();
        jsonObject.put("pageNum", pageNum);
        jsonObject.put("pageSize", pageSize);
        return jsonObject;
    }

    /**
     * 获取分页信息
     *
     * @param messageBody
     * @return
     */
    public static PageInfo getPageInfo(String messageBody) {
        JSONObject obj = JSONObject.parseObject(messageBody);
        JSONObject dataObj = obj.getJSONObject("data");
        PageInfo page = new PageInfo();
        int currentPage = IntegerUtil.parseInt(dataObj.getString("currentPage"), -1);
        int pageSize = IntegerUtil.parseInt(dataObj.getString("pageSize"), -1);
        int totalPage = IntegerUtil.parseInt(dataObj.getString("totalPage"), -1);
        int totalRows = IntegerUtil.parseInt(dataObj.getString("totalRows"), -1);
        page.setCurrentPage(currentPage);
        page.setPageSize(pageSize);
        page.setTotalPage(totalPage);
        page.setTotalRows(totalRows);
        return page;
    }

}
