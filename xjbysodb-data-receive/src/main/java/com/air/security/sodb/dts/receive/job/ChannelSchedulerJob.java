package com.air.security.sodb.dts.receive.job;

import com.air.security.sodb.data.core.base.Meta;
import com.air.security.sodb.data.core.constant.MsgIdConstant;
import com.air.security.sodb.data.core.exception.SystemBaseException;
import com.air.security.sodb.data.core.quartz.AbstractJob;
import com.air.security.sodb.data.core.util.HaLog;
import com.air.security.sodb.data.core.util.UuidUtil;
import com.air.security.sodb.dts.receive.server.MqMessageRecvServer;
import com.air.security.sodb.dts.receive.server.MqMessageRecvServerImpl;
import com.air.security.sodb.dts.receive.util.IClientUtils;
import com.air.security.sodb.dts.srvc.restsrvc.platform.Response;
import com.air.security.sodb.dts.srvc.restsrvc.platform.equ.dto.ChannelInfoVO;
import com.air.security.sodb.dts.srvc.restsrvc.platform.equ.vo.ChannelQueryVO;
import com.alibaba.fastjson.JSON;
import com.dahuatech.icc.exception.ClientException;
import org.quartz.JobDataMap;
import org.slf4j.Logger;
import org.slf4j.LoggerFactory;

import java.util.Arrays;
import java.util.List;

/**
 * 大华门禁通道信息获取任务
 *
 * <AUTHOR>
 */
public class ChannelSchedulerJob extends AbstractJob {

    private static final Logger log = LoggerFactory.getLogger(ChannelSchedulerJob.class);

    /**
     *
     */
    private MqMessageRecvServer server = new MqMessageRecvServerImpl();

    @Override
    public void executeJob(JobDataMap paramMap) {
        HaLog.info(log, MsgIdConstant.MS_INF_0001, "获取大华门禁通道信息定时任务");

        try {
            ChannelQueryVO vo = new ChannelQueryVO();
            vo.setPageSize(600);
            vo.setUnitTypeList(Arrays.asList(new Integer[]{7}));
            getAllEquList(vo);
        } catch (Exception e) {
            throw new SystemBaseException("获取大华门禁设备信息失败", e);
        } finally {
            HaLog.info(log, MsgIdConstant.MS_INF_0002, "获取大华门禁通道信息定时任务");
        }

    }

    public void getAllEquList(ChannelQueryVO vo) throws ClientException {
        int currentPage = 1;
        while (true) {
            vo.setPageNum(currentPage);
            Response response = IClientUtils.getEquChannelList(vo);
            if (response != null && response.getData() != null) {
                // 处理返回的数据
                handleData(response.getData().getPageData());
                if (response.getData().getCurrentPage() < response.getData().getTotalPage()) {
                    currentPage++;
                } else {
                    break;
                }
            } else {
                break;
            }
        }
    }

    public void handleData(List<ChannelInfoVO> pageData) {
        for (ChannelInfoVO pageDatum : pageData) {
            Meta meta = new Meta();
            meta.setEventType("dhEquChannelInfo");
            meta.setRecvSequence(UuidUtil.getUuid32());
            server.handle(meta, JSON.toJSONString(pageDatum));
        }
    }

}
