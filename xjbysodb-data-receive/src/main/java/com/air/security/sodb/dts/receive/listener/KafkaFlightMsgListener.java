package com.air.security.sodb.dts.receive.listener;

import com.air.security.sodb.data.core.base.Meta;
import com.air.security.sodb.data.core.constant.MsgIdConstant;
import com.air.security.sodb.data.core.util.HaLog;
import com.air.security.sodb.data.core.util.KafkaConscumerUtil;
import com.air.security.sodb.data.core.util.PropertyUtil;
import com.air.security.sodb.data.core.util.ThreadUtil;
import com.air.security.sodb.dts.receive.server.MqMessageRecvServer;
import com.air.security.sodb.dts.receive.server.MqMessageRecvServerImpl;
import com.alibaba.fastjson.JSONArray;
import com.alibaba.fastjson.JSONObject;
import org.apache.commons.lang.StringUtils;
import org.apache.kafka.clients.consumer.ConsumerRecord;
import org.apache.kafka.clients.consumer.ConsumerRecords;
import org.apache.kafka.clients.consumer.KafkaConsumer;
import org.slf4j.Logger;
import org.slf4j.LoggerFactory;

import java.time.Duration;

/**
 * @Description: kafka事件消息监听
 * @author: zhangc
 * @Date: 2018年12月18日
 */
public class KafkaFlightMsgListener implements Runnable {

	private static final Logger log = LoggerFactory.getLogger(KafkaFlightMsgListener.class);

	/**
	 * 消息分发处理器
	 */
	private MqMessageRecvServer server = new MqMessageRecvServerImpl();

	/**
	 * 报警消息发布服务主题
	 */
	private static String topic = PropertyUtil.getProperty("kafka.flight.msg.input.topics");

	@Override
	public void run() {

		HaLog.info(log, MsgIdConstant.MS_INF_0001, "kafka事件消息监听");

		KafkaConsumer<String, String> consumer = KafkaConscumerUtil.getConsumer(new String[] { topic });
		while (true) {
			ThreadUtil.sleepThreadUnit();

			// 消费消息
			ConsumerRecords<String, String> records = consumer.poll(Duration.ZERO);
			for (ConsumerRecord<String, String> record : records) {
				String message = record.value();

				if (StringUtils.isBlank(message)) {
					continue;
				}
				// 接收消息日志
				HaLog.infoJson(log, message);

				try {
					JSONObject jsonObject = JSONObject.parseObject(message);
					if (null!=jsonObject){
						String eventType = jsonObject.getString("eventType");
						if ("FLIGHT_DYN".equals(eventType)){
							JSONArray data = jsonObject.getJSONArray("data");
							for (Object object : data) {
								Meta meta = new Meta();
								meta.setEventType("flight");
								server.handle(meta, JSONObject.toJSONString(object));
							}
						}else if("WEATHER_DYN".equals(eventType)){
							JSONObject data = jsonObject.getJSONObject("data");
							Meta meta = new Meta();
							meta.setEventType("weather");
							server.handle(meta, JSONObject.toJSONString(data));
						}
					}

				} catch (Exception e) {
					HaLog.error(log, e, MsgIdConstant.MS_ERR_0001);
				}
			}
		}
	}

}
