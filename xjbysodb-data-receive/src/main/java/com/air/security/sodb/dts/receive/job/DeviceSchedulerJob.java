package com.air.security.sodb.dts.receive.job;

import com.air.security.sodb.data.core.base.Meta;
import com.air.security.sodb.data.core.constant.MsgIdConstant;
import com.air.security.sodb.data.core.exception.SystemBaseException;
import com.air.security.sodb.data.core.quartz.AbstractJob;
import com.air.security.sodb.data.core.util.HaLog;
import com.air.security.sodb.data.core.util.UuidUtil;
import com.air.security.sodb.dts.receive.server.MqMessageRecvServer;
import com.air.security.sodb.dts.receive.server.MqMessageRecvServerImpl;
import com.air.security.sodb.dts.receive.util.IClientUtils;
import com.air.security.sodb.dts.srvc.restsrvc.platform.Response;
import com.air.security.sodb.dts.srvc.restsrvc.platform.equ.dto.ChannelVO;
import com.air.security.sodb.dts.srvc.restsrvc.platform.equ.dto.DeviceInfoVO;
import com.air.security.sodb.dts.srvc.restsrvc.platform.equ.dto.UnitVO;
import com.air.security.sodb.dts.srvc.restsrvc.platform.equ.vo.DeviceQueryVO;
import com.alibaba.fastjson.JSON;
import com.dahuatech.icc.exception.ClientException;
import org.quartz.JobDataMap;
import org.slf4j.Logger;
import org.slf4j.LoggerFactory;

import java.util.ArrayList;
import java.util.Arrays;
import java.util.List;

/**
 * 获取大华隐蔽报警资源信息定时任务
 *
 * <AUTHOR>
 * @version 1.0
 * @date 2018-04-26
 */
public class DeviceSchedulerJob extends AbstractJob {

    private static final Logger log = LoggerFactory.getLogger(DeviceSchedulerJob.class);

    /**
     *
     */
    private MqMessageRecvServer server = new MqMessageRecvServerImpl();

    @Override
    public void executeJob(JobDataMap paramMap) {
        HaLog.info(log, MsgIdConstant.MS_INF_0001, "获取大华隐蔽报警资源信息定时任务");

        try {
            DeviceQueryVO vo = new DeviceQueryVO();
            vo.setPageSize(600);
            vo.setCategorys(new ArrayList<>(Arrays.asList("3")));
            getAllEquList(vo);
        } catch (Exception e) {
            throw new SystemBaseException("获取大华隐蔽报警资源设备信息失败", e);
        } finally {
            HaLog.info(log, MsgIdConstant.MS_INF_0002, "获取大华隐蔽报警资源信息定时任务");
        }
    }

    public void getAllEquList(DeviceQueryVO vo) throws ClientException {
        int currentPage = 1;
        while (true) {
            vo.setPageNum(currentPage);
            Response response = IClientUtils.getEquHandList(vo);
            if (response != null && response.getData() != null) {
                List<DeviceInfoVO> pageData = JSON.parseArray(JSON.toJSONString(response.getData().getPageData()), DeviceInfoVO.class);
                // 处理返回的数据
                handleData(pageData);
                if (response.getData().getCurrentPage() < response.getData().getTotalPage()) {
                    currentPage++;
                } else {
                    break;
                }
            } else {
                break;
            }
        }
    }

    public void handleData(List<DeviceInfoVO> pageData) {
        for (DeviceInfoVO pageDatum : pageData) {
            List<UnitVO> units = pageDatum.getUnits();
            for (UnitVO unit : units) {
                int unitType = unit.getUnitType();
                if (unitType!=3){
                    return;
                }
                List<ChannelVO> channels = unit.getChannels();
                System.out.println(channels.size());
                for (ChannelVO channel : channels) {
                    Meta meta = new Meta();
                    meta.setEventType("dhEquHandInfo");
                    meta.setRecvSequence(UuidUtil.getUuid32());
                    server.handle(meta, JSON.toJSONString(channel));
                }
            }
        }

    }

    public static void main(String[] args) {
        String result = "{\n" +
                "\t\"success\": true,\n" +
                "\t\"code\": \"0\",\n" +
                "\t\"errMsg\": \"成功\",\n" +
                "\t\"data\": {\n" +
                "\t\t\"pageData\": [{\n" +
                "\t\t\t\"deviceIp\": \"**********\",\n" +
                "\t\t\t\"offlineReason\": \"0\",\n" +
                "\t\t\t\"onlineUpdateVersion\": 0,\n" +
                "\t\t\t\"subSystem\": \"evo-alarm\",\n" +
                "\t\t\t\"ownerCode\": \"001003\",\n" +
                "\t\t\t\"loginType\": \"0\",\n" +
                "\t\t\t\"isPwdChange\": false,\n" +
                "\t\t\t\"isOnline\": 1,\n" +
                "\t\t\t\"units\": [{\n" +
                "\t\t\t\t\"unitType\": 3,\n" +
                "\t\t\t\t\"unitSeq\": 0,\n" +
                "\t\t\t\t\"channels\": [{\n" +
                "\t\t\t\t\t\t\"stat\": 1,\n" +
                "\t\t\t\t\t\t\"treeSort\": 31003519131010,\n" +
                "\t\t\t\t\t\t\"onlineUpdateVersion\": 0,\n" +
                "\t\t\t\t\t\t\"ownerCode\": \"001003\",\n" +
                "\t\t\t\t\t\t\"isOnline\": 1,\n" +
                "\t\t\t\t\t\t\"updateTime\": 1700036768000,\n" +
                "\t\t\t\t\t\t\"createTime\": 1697883866000,\n" +
                "\t\t\t\t\t\t\"channelSeq\": 0,\n" +
                "\t\t\t\t\t\t\"channelName\": \"BJ3\",\n" +
                "\t\t\t\t\t\t\"onlineUpdateTime\": \"2023-11-15 16:26:08.197\",\n" +
                "\t\t\t\t\t\t\"isVirtual\": 0,\n" +
                "\t\t\t\t\t\t\"channelCode\": \"1003519$3$0$0\",\n" +
                "\t\t\t\t\t\t\"chExt\": {\n" +
                "\t\t\t\t\t\t\t\"defenceAreaType\": 2,\n" +
                "\t\t\t\t\t\t\t\"detectorType\": 31,\n" +
                "\t\t\t\t\t\t\t\"channelSnExt\": \"0\",\n" +
                "\t\t\t\t\t\t\t\"subSystemSeq\": \"0\"\n" +
                "\t\t\t\t\t\t}\n" +
                "\t\t\t\t\t},\n" +
                "\t\t\t\t\t{\n" +
                "\t\t\t\t\t\t\"stat\": 1,\n" +
                "\t\t\t\t\t\t\"treeSort\": 31003519131011,\n" +
                "\t\t\t\t\t\t\"onlineUpdateVersion\": 0,\n" +
                "\t\t\t\t\t\t\"ownerCode\": \"001003\",\n" +
                "\t\t\t\t\t\t\"isOnline\": 1,\n" +
                "\t\t\t\t\t\t\"updateTime\": 1700036768000,\n" +
                "\t\t\t\t\t\t\"createTime\": 1697883866000,\n" +
                "\t\t\t\t\t\t\"channelSeq\": 1,\n" +
                "\t\t\t\t\t\t\"channelName\": \"BJ1\",\n" +
                "\t\t\t\t\t\t\"onlineUpdateTime\": \"2023-11-15 16:26:08.197\",\n" +
                "\t\t\t\t\t\t\"isVirtual\": 0,\n" +
                "\t\t\t\t\t\t\"channelCode\": \"1003519$3$0$1\",\n" +
                "\t\t\t\t\t\t\"chExt\": {\n" +
                "\t\t\t\t\t\t\t\"defenceAreaType\": 2,\n" +
                "\t\t\t\t\t\t\t\"detectorType\": 31,\n" +
                "\t\t\t\t\t\t\t\"channelSnExt\": \"1\",\n" +
                "\t\t\t\t\t\t\t\"subSystemSeq\": \"0\"\n" +
                "\t\t\t\t\t\t}\n" +
                "\t\t\t\t\t},\n" +
                "\t\t\t\t\t{\n" +
                "\t\t\t\t\t\t\"stat\": 1,\n" +
                "\t\t\t\t\t\t\"treeSort\": 31003519131012,\n" +
                "\t\t\t\t\t\t\"onlineUpdateVersion\": 0,\n" +
                "\t\t\t\t\t\t\"ownerCode\": \"001003\",\n" +
                "\t\t\t\t\t\t\"isOnline\": 1,\n" +
                "\t\t\t\t\t\t\"updateTime\": 1700036768000,\n" +
                "\t\t\t\t\t\t\"createTime\": 1697883866000,\n" +
                "\t\t\t\t\t\t\"channelSeq\": 2,\n" +
                "\t\t\t\t\t\t\"channelName\": \"BJ2\",\n" +
                "\t\t\t\t\t\t\"onlineUpdateTime\": \"2023-11-15 16:26:08.197\",\n" +
                "\t\t\t\t\t\t\"isVirtual\": 0,\n" +
                "\t\t\t\t\t\t\"channelCode\": \"1003519$3$0$2\",\n" +
                "\t\t\t\t\t\t\"chExt\": {\n" +
                "\t\t\t\t\t\t\t\"defenceAreaType\": 2,\n" +
                "\t\t\t\t\t\t\t\"detectorType\": 31,\n" +
                "\t\t\t\t\t\t\t\"channelSnExt\": \"2\",\n" +
                "\t\t\t\t\t\t\t\"subSystemSeq\": \"0\"\n" +
                "\t\t\t\t\t\t}\n" +
                "\t\t\t\t\t},\n" +
                "\t\t\t\t\t{\n" +
                "\t\t\t\t\t\t\"stat\": 1,\n" +
                "\t\t\t\t\t\t\"treeSort\": 31003519131013,\n" +
                "\t\t\t\t\t\t\"onlineUpdateVersion\": 0,\n" +
                "\t\t\t\t\t\t\"ownerCode\": \"001003\",\n" +
                "\t\t\t\t\t\t\"isOnline\": 1,\n" +
                "\t\t\t\t\t\t\"updateTime\": 1700036768000,\n" +
                "\t\t\t\t\t\t\"createTime\": 1697883866000,\n" +
                "\t\t\t\t\t\t\"channelSeq\": 3,\n" +
                "\t\t\t\t\t\t\"channelName\": \"BJ4\",\n" +
                "\t\t\t\t\t\t\"onlineUpdateTime\": \"2023-11-15 16:26:08.197\",\n" +
                "\t\t\t\t\t\t\"isVirtual\": 0,\n" +
                "\t\t\t\t\t\t\"channelCode\": \"1003519$3$0$3\",\n" +
                "\t\t\t\t\t\t\"chExt\": {\n" +
                "\t\t\t\t\t\t\t\"defenceAreaType\": 2,\n" +
                "\t\t\t\t\t\t\t\"detectorType\": 31,\n" +
                "\t\t\t\t\t\t\t\"channelSnExt\": \"3\",\n" +
                "\t\t\t\t\t\t\t\"subSystemSeq\": \"0\"\n" +
                "\t\t\t\t\t\t}\n" +
                "\t\t\t\t\t},\n" +
                "\t\t\t\t\t{\n" +
                "\t\t\t\t\t\t\"stat\": 1,\n" +
                "\t\t\t\t\t\t\"treeSort\": 31003519131014,\n" +
                "\t\t\t\t\t\t\"onlineUpdateVersion\": 0,\n" +
                "\t\t\t\t\t\t\"ownerCode\": \"001003\",\n" +
                "\t\t\t\t\t\t\"isOnline\": 1,\n" +
                "\t\t\t\t\t\t\"updateTime\": 1700036768000,\n" +
                "\t\t\t\t\t\t\"createTime\": 1697883866000,\n" +
                "\t\t\t\t\t\t\"channelSeq\": 4,\n" +
                "\t\t\t\t\t\t\"channelName\": \"BJ5\",\n" +
                "\t\t\t\t\t\t\"onlineUpdateTime\": \"2023-11-15 16:26:08.197\",\n" +
                "\t\t\t\t\t\t\"isVirtual\": 0,\n" +
                "\t\t\t\t\t\t\"channelCode\": \"1003519$3$0$4\",\n" +
                "\t\t\t\t\t\t\"chExt\": {\n" +
                "\t\t\t\t\t\t\t\"defenceAreaType\": 2,\n" +
                "\t\t\t\t\t\t\t\"detectorType\": 31,\n" +
                "\t\t\t\t\t\t\t\"channelSnExt\": \"4\",\n" +
                "\t\t\t\t\t\t\t\"subSystemSeq\": \"0\"\n" +
                "\t\t\t\t\t\t}\n" +
                "\t\t\t\t\t},\n" +
                "\t\t\t\t\t{\n" +
                "\t\t\t\t\t\t\"stat\": 1,\n" +
                "\t\t\t\t\t\t\"treeSort\": 31003519131015,\n" +
                "\t\t\t\t\t\t\"onlineUpdateVersion\": 0,\n" +
                "\t\t\t\t\t\t\"ownerCode\": \"001003\",\n" +
                "\t\t\t\t\t\t\"isOnline\": 1,\n" +
                "\t\t\t\t\t\t\"updateTime\": 1700036768000,\n" +
                "\t\t\t\t\t\t\"createTime\": 1697883866000,\n" +
                "\t\t\t\t\t\t\"channelSeq\": 5,\n" +
                "\t\t\t\t\t\t\"channelName\": \"BJ6\",\n" +
                "\t\t\t\t\t\t\"onlineUpdateTime\": \"2023-11-15 16:26:08.197\",\n" +
                "\t\t\t\t\t\t\"isVirtual\": 0,\n" +
                "\t\t\t\t\t\t\"channelCode\": \"1003519$3$0$5\",\n" +
                "\t\t\t\t\t\t\"chExt\": {\n" +
                "\t\t\t\t\t\t\t\"defenceAreaType\": 2,\n" +
                "\t\t\t\t\t\t\t\"detectorType\": 31,\n" +
                "\t\t\t\t\t\t\t\"channelSnExt\": \"5\",\n" +
                "\t\t\t\t\t\t\t\"subSystemSeq\": \"0\"\n" +
                "\t\t\t\t\t\t}\n" +
                "\t\t\t\t\t},\n" +
                "\t\t\t\t\t{\n" +
                "\t\t\t\t\t\t\"stat\": 1,\n" +
                "\t\t\t\t\t\t\"treeSort\": 31003519131016,\n" +
                "\t\t\t\t\t\t\"onlineUpdateVersion\": 0,\n" +
                "\t\t\t\t\t\t\"ownerCode\": \"001003\",\n" +
                "\t\t\t\t\t\t\"isOnline\": 1,\n" +
                "\t\t\t\t\t\t\"updateTime\": 1700036768000,\n" +
                "\t\t\t\t\t\t\"createTime\": 1697883866000,\n" +
                "\t\t\t\t\t\t\"channelSeq\": 6,\n" +
                "\t\t\t\t\t\t\"channelName\": \"BJ7\",\n" +
                "\t\t\t\t\t\t\"onlineUpdateTime\": \"2023-11-15 16:26:08.197\",\n" +
                "\t\t\t\t\t\t\"isVirtual\": 0,\n" +
                "\t\t\t\t\t\t\"channelCode\": \"1003519$3$0$6\",\n" +
                "\t\t\t\t\t\t\"chExt\": {\n" +
                "\t\t\t\t\t\t\t\"defenceAreaType\": 2,\n" +
                "\t\t\t\t\t\t\t\"detectorType\": 31,\n" +
                "\t\t\t\t\t\t\t\"channelSnExt\": \"6\",\n" +
                "\t\t\t\t\t\t\t\"subSystemSeq\": \"0\"\n" +
                "\t\t\t\t\t\t}\n" +
                "\t\t\t\t\t},\n" +
                "\t\t\t\t\t{\n" +
                "\t\t\t\t\t\t\"stat\": 0,\n" +
                "\t\t\t\t\t\t\"treeSort\": 31003519131017,\n" +
                "\t\t\t\t\t\t\"onlineUpdateVersion\": 0,\n" +
                "\t\t\t\t\t\t\"ownerCode\": \"001003\",\n" +
                "\t\t\t\t\t\t\"isOnline\": 1,\n" +
                "\t\t\t\t\t\t\"updateTime\": 1700036768000,\n" +
                "\t\t\t\t\t\t\"createTime\": 1697883866000,\n" +
                "\t\t\t\t\t\t\"channelSeq\": 7,\n" +
                "\t\t\t\t\t\t\"channelName\": \"防区8\",\n" +
                "\t\t\t\t\t\t\"onlineUpdateTime\": \"2023-11-15 16:26:08.197\",\n" +
                "\t\t\t\t\t\t\"isVirtual\": 0,\n" +
                "\t\t\t\t\t\t\"channelCode\": \"1003519$3$0$7\",\n" +
                "\t\t\t\t\t\t\"chExt\": {\n" +
                "\t\t\t\t\t\t\t\"defenceAreaType\": 2,\n" +
                "\t\t\t\t\t\t\t\"detectorType\": 0,\n" +
                "\t\t\t\t\t\t\t\"channelSnExt\": \"7\"\n" +
                "\t\t\t\t\t\t}\n" +
                "\t\t\t\t\t},\n" +
                "\t\t\t\t\t{\n" +
                "\t\t\t\t\t\t\"stat\": 0,\n" +
                "\t\t\t\t\t\t\"treeSort\": 31003519131018,\n" +
                "\t\t\t\t\t\t\"onlineUpdateVersion\": 0,\n" +
                "\t\t\t\t\t\t\"ownerCode\": \"001003\",\n" +
                "\t\t\t\t\t\t\"isOnline\": 1,\n" +
                "\t\t\t\t\t\t\"updateTime\": 1700036768000,\n" +
                "\t\t\t\t\t\t\"createTime\": 1697883866000,\n" +
                "\t\t\t\t\t\t\"channelSeq\": 8,\n" +
                "\t\t\t\t\t\t\"channelName\": \"防区9\",\n" +
                "\t\t\t\t\t\t\"onlineUpdateTime\": \"2023-11-15 16:26:08.197\",\n" +
                "\t\t\t\t\t\t\"isVirtual\": 0,\n" +
                "\t\t\t\t\t\t\"channelCode\": \"1003519$3$0$8\",\n" +
                "\t\t\t\t\t\t\"chExt\": {\n" +
                "\t\t\t\t\t\t\t\"defenceAreaType\": 2,\n" +
                "\t\t\t\t\t\t\t\"detectorType\": 0,\n" +
                "\t\t\t\t\t\t\t\"channelSnExt\": \"8\"\n" +
                "\t\t\t\t\t\t}\n" +
                "\t\t\t\t\t},\n" +
                "\t\t\t\t\t{\n" +
                "\t\t\t\t\t\t\"stat\": 0,\n" +
                "\t\t\t\t\t\t\"treeSort\": 31003519131019,\n" +
                "\t\t\t\t\t\t\"onlineUpdateVersion\": 0,\n" +
                "\t\t\t\t\t\t\"ownerCode\": \"001003\",\n" +
                "\t\t\t\t\t\t\"isOnline\": 1,\n" +
                "\t\t\t\t\t\t\"updateTime\": 1700036768000,\n" +
                "\t\t\t\t\t\t\"createTime\": 1697883866000,\n" +
                "\t\t\t\t\t\t\"channelSeq\": 9,\n" +
                "\t\t\t\t\t\t\"channelName\": \"防区10\",\n" +
                "\t\t\t\t\t\t\"onlineUpdateTime\": \"2023-11-15 16:26:08.197\",\n" +
                "\t\t\t\t\t\t\"isVirtual\": 0,\n" +
                "\t\t\t\t\t\t\"channelCode\": \"1003519$3$0$9\",\n" +
                "\t\t\t\t\t\t\"chExt\": {\n" +
                "\t\t\t\t\t\t\t\"defenceAreaType\": 2,\n" +
                "\t\t\t\t\t\t\t\"detectorType\": 0,\n" +
                "\t\t\t\t\t\t\t\"channelSnExt\": \"9\"\n" +
                "\t\t\t\t\t\t}\n" +
                "\t\t\t\t\t},\n" +
                "\t\t\t\t\t{\n" +
                "\t\t\t\t\t\t\"stat\": 0,\n" +
                "\t\t\t\t\t\t\"treeSort\": 310035191310110,\n" +
                "\t\t\t\t\t\t\"onlineUpdateVersion\": 0,\n" +
                "\t\t\t\t\t\t\"ownerCode\": \"001003\",\n" +
                "\t\t\t\t\t\t\"isOnline\": 1,\n" +
                "\t\t\t\t\t\t\"updateTime\": 1700036768000,\n" +
                "\t\t\t\t\t\t\"createTime\": 1697883866000,\n" +
                "\t\t\t\t\t\t\"channelSeq\": 10,\n" +
                "\t\t\t\t\t\t\"channelName\": \"防区11\",\n" +
                "\t\t\t\t\t\t\"onlineUpdateTime\": \"2023-11-15 16:26:08.197\",\n" +
                "\t\t\t\t\t\t\"isVirtual\": 0,\n" +
                "\t\t\t\t\t\t\"channelCode\": \"1003519$3$0$10\",\n" +
                "\t\t\t\t\t\t\"chExt\": {\n" +
                "\t\t\t\t\t\t\t\"defenceAreaType\": 2,\n" +
                "\t\t\t\t\t\t\t\"detectorType\": 0,\n" +
                "\t\t\t\t\t\t\t\"channelSnExt\": \"10\"\n" +
                "\t\t\t\t\t\t}\n" +
                "\t\t\t\t\t},\n" +
                "\t\t\t\t\t{\n" +
                "\t\t\t\t\t\t\"stat\": 0,\n" +
                "\t\t\t\t\t\t\"treeSort\": 310035191310111,\n" +
                "\t\t\t\t\t\t\"onlineUpdateVersion\": 0,\n" +
                "\t\t\t\t\t\t\"ownerCode\": \"001003\",\n" +
                "\t\t\t\t\t\t\"isOnline\": 1,\n" +
                "\t\t\t\t\t\t\"updateTime\": 1700036768000,\n" +
                "\t\t\t\t\t\t\"createTime\": 1697883866000,\n" +
                "\t\t\t\t\t\t\"channelSeq\": 11,\n" +
                "\t\t\t\t\t\t\"channelName\": \"防区12\",\n" +
                "\t\t\t\t\t\t\"onlineUpdateTime\": \"2023-11-15 16:26:08.197\",\n" +
                "\t\t\t\t\t\t\"isVirtual\": 0,\n" +
                "\t\t\t\t\t\t\"channelCode\": \"1003519$3$0$11\",\n" +
                "\t\t\t\t\t\t\"chExt\": {\n" +
                "\t\t\t\t\t\t\t\"defenceAreaType\": 2,\n" +
                "\t\t\t\t\t\t\t\"detectorType\": 0,\n" +
                "\t\t\t\t\t\t\t\"channelSnExt\": \"11\"\n" +
                "\t\t\t\t\t\t}\n" +
                "\t\t\t\t\t},\n" +
                "\t\t\t\t\t{\n" +
                "\t\t\t\t\t\t\"stat\": 0,\n" +
                "\t\t\t\t\t\t\"treeSort\": 310035191310112,\n" +
                "\t\t\t\t\t\t\"onlineUpdateVersion\": 0,\n" +
                "\t\t\t\t\t\t\"ownerCode\": \"001003\",\n" +
                "\t\t\t\t\t\t\"isOnline\": 1,\n" +
                "\t\t\t\t\t\t\"updateTime\": 1700036768000,\n" +
                "\t\t\t\t\t\t\"createTime\": 1697883866000,\n" +
                "\t\t\t\t\t\t\"channelSeq\": 12,\n" +
                "\t\t\t\t\t\t\"channelName\": \"防区13\",\n" +
                "\t\t\t\t\t\t\"onlineUpdateTime\": \"2023-11-15 16:26:08.197\",\n" +
                "\t\t\t\t\t\t\"isVirtual\": 0,\n" +
                "\t\t\t\t\t\t\"channelCode\": \"1003519$3$0$12\",\n" +
                "\t\t\t\t\t\t\"chExt\": {\n" +
                "\t\t\t\t\t\t\t\"defenceAreaType\": 2,\n" +
                "\t\t\t\t\t\t\t\"detectorType\": 0,\n" +
                "\t\t\t\t\t\t\t\"channelSnExt\": \"12\"\n" +
                "\t\t\t\t\t\t}\n" +
                "\t\t\t\t\t},\n" +
                "\t\t\t\t\t{\n" +
                "\t\t\t\t\t\t\"stat\": 0,\n" +
                "\t\t\t\t\t\t\"treeSort\": 310035191310113,\n" +
                "\t\t\t\t\t\t\"onlineUpdateVersion\": 0,\n" +
                "\t\t\t\t\t\t\"ownerCode\": \"001003\",\n" +
                "\t\t\t\t\t\t\"isOnline\": 1,\n" +
                "\t\t\t\t\t\t\"updateTime\": 1700036768000,\n" +
                "\t\t\t\t\t\t\"createTime\": 1697883866000,\n" +
                "\t\t\t\t\t\t\"channelSeq\": 13,\n" +
                "\t\t\t\t\t\t\"channelName\": \"防区14\",\n" +
                "\t\t\t\t\t\t\"onlineUpdateTime\": \"2023-11-15 16:26:08.197\",\n" +
                "\t\t\t\t\t\t\"isVirtual\": 0,\n" +
                "\t\t\t\t\t\t\"channelCode\": \"1003519$3$0$13\",\n" +
                "\t\t\t\t\t\t\"chExt\": {\n" +
                "\t\t\t\t\t\t\t\"defenceAreaType\": 2,\n" +
                "\t\t\t\t\t\t\t\"detectorType\": 0,\n" +
                "\t\t\t\t\t\t\t\"channelSnExt\": \"13\"\n" +
                "\t\t\t\t\t\t}\n" +
                "\t\t\t\t\t},\n" +
                "\t\t\t\t\t{\n" +
                "\t\t\t\t\t\t\"stat\": 0,\n" +
                "\t\t\t\t\t\t\"treeSort\": 310035191310114,\n" +
                "\t\t\t\t\t\t\"onlineUpdateVersion\": 0,\n" +
                "\t\t\t\t\t\t\"ownerCode\": \"001003\",\n" +
                "\t\t\t\t\t\t\"isOnline\": 1,\n" +
                "\t\t\t\t\t\t\"updateTime\": 1700036768000,\n" +
                "\t\t\t\t\t\t\"createTime\": 1697883866000,\n" +
                "\t\t\t\t\t\t\"channelSeq\": 14,\n" +
                "\t\t\t\t\t\t\"channelName\": \"防区15\",\n" +
                "\t\t\t\t\t\t\"onlineUpdateTime\": \"2023-11-15 16:26:08.197\",\n" +
                "\t\t\t\t\t\t\"isVirtual\": 0,\n" +
                "\t\t\t\t\t\t\"channelCode\": \"1003519$3$0$14\",\n" +
                "\t\t\t\t\t\t\"chExt\": {\n" +
                "\t\t\t\t\t\t\t\"defenceAreaType\": 2,\n" +
                "\t\t\t\t\t\t\t\"detectorType\": 0,\n" +
                "\t\t\t\t\t\t\t\"channelSnExt\": \"14\"\n" +
                "\t\t\t\t\t\t}\n" +
                "\t\t\t\t\t},\n" +
                "\t\t\t\t\t{\n" +
                "\t\t\t\t\t\t\"stat\": 0,\n" +
                "\t\t\t\t\t\t\"treeSort\": 310035191310115,\n" +
                "\t\t\t\t\t\t\"onlineUpdateVersion\": 0,\n" +
                "\t\t\t\t\t\t\"ownerCode\": \"001003\",\n" +
                "\t\t\t\t\t\t\"isOnline\": 1,\n" +
                "\t\t\t\t\t\t\"updateTime\": 1700036768000,\n" +
                "\t\t\t\t\t\t\"createTime\": 1697883866000,\n" +
                "\t\t\t\t\t\t\"channelSeq\": 15,\n" +
                "\t\t\t\t\t\t\"channelName\": \"防区16\",\n" +
                "\t\t\t\t\t\t\"onlineUpdateTime\": \"2023-11-15 16:26:08.197\",\n" +
                "\t\t\t\t\t\t\"isVirtual\": 0,\n" +
                "\t\t\t\t\t\t\"channelCode\": \"1003519$3$0$15\",\n" +
                "\t\t\t\t\t\t\"chExt\": {\n" +
                "\t\t\t\t\t\t\t\"defenceAreaType\": 2,\n" +
                "\t\t\t\t\t\t\t\"detectorType\": 0,\n" +
                "\t\t\t\t\t\t\t\"channelSnExt\": \"15\"\n" +
                "\t\t\t\t\t\t}\n" +
                "\t\t\t\t\t}\n" +
                "\t\t\t\t]\n" +
                "\t\t\t}],\n" +
                "\t\t\t\"deviceName\": \"10.4.25.53\",\n" +
                "\t\t\t\"deviceSn\": \"\",\n" +
                "\t\t\t\"protocol\": \"1\",\n" +
                "\t\t\t\"loginName\": \"admin\",\n" +
                "\t\t\t\"loginPassword\": \"492517E19D87C20BC6357BA0FD77E7A60755E4EBAC852C4D796455C3765134F2\",\n" +
                "\t\t\t\"deviceManufacturer\": \"1\",\n" +
                "\t\t\t\"recordUpdateTime\": 1700036768224,\n" +
                "\t\t\t\"deviceType\": 1,\n" +
                "\t\t\t\"devExt\": {\n" +
                "\t\t\t\t\"arsServerCode\": \"\",\n" +
                "\t\t\t\t\"videoServerCodeKey\": \"MCD_ALARM_ICC_1@127.0.0.1(7003)\",\n" +
                "\t\t\t\t\"netModel\": \"5\",\n" +
                "\t\t\t\t\"videoServerCode\": \"video#local(7003)\",\n" +
                "\t\t\t\t\"netManufacturer\": \"1\"\n" +
                "\t\t\t},\n" +
                "\t\t\t\"updateTime\": 1700036768000,\n" +
                "\t\t\t\"deviceCode\": \"1004201\",\n" +
                "\t\t\t\"deviceCategory\": 3,\n" +
                "\t\t\t\"createTime\": 1699415824000,\n" +
                "\t\t\t\"devicePort\": 37777,\n" +
                "\t\t\t\"deviceModel\": \"\",\n" +
                "\t\t\t\"onlineUpdateTime\": \"2023-11-15 16:26:08.207\",\n" +
                "\t\t\t\"isVirtual\": 0\n" +
                "\t\t}],\n" +
                "\t\t\"currentPage\": 1,\n" +
                "\t\t\"totalPage\": 1,\n" +
                "\t\t\"pageSize\": 5,\n" +
                "\t\t\"totalRows\": 1\n" +
                "\t}\n" +
                "}";
        Response response = JSON.parseObject(result, Response.class);
        List<DeviceInfoVO> pageData = JSON.parseArray(JSON.toJSONString(response.getData().getPageData()), DeviceInfoVO.class);
        for (DeviceInfoVO pageDatum : pageData) {
            List<UnitVO> units = pageDatum.getUnits();
            for (UnitVO unit : units) {
                int unitType = unit.getUnitType();
                if (unitType!=3){
                    return;
                }
                List<ChannelVO> channels = unit.getChannels();
                System.out.println(channels.size());
                for (ChannelVO channel : channels) {
                    System.out.println(channel.getChannelCode());
                }
            }
        }
    }

}
