package com.air.security.sodb.dts.receive.util;

public class EquStatus {
    private String equCode;
    private String timeStateId;

    public String getEquCode() {
        return equCode;
    }

    public void setEquCode(String equCode) {
        this.equCode = equCode;
    }

    public String getTimeStateId() {
        return timeStateId;
    }

    public void setTimeStateId(String timeStateId) {
        this.timeStateId = timeStateId;
    }

    @Override
    public int hashCode() {
        final int prime = 31;
        int result = 1;
        result = prime * result + ((equCode == null) ? 0 : equCode.hashCode());
        return result;
    }

    @Override
    public boolean equals(Object obj) {
        if (this == obj) {
            return true;
        }
        if (obj == null) {
            return false;
        }
        if (getClass() != obj.getClass()) {
            return false;
        }
        EquStatus other = (EquStatus) obj;
        if (equCode == null) {
            if (other.equCode != null) {
                return false;
            }
        } else if (!equCode.equals(other.equCode)) {
            return false;
        }
        return true;
    }

}
