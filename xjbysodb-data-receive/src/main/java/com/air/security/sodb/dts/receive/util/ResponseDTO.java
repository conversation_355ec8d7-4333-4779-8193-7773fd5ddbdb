package com.air.security.sodb.dts.receive.util;

import com.air.security.sodb.data.core.service.ResponseBaseDTO;
import com.air.security.sodb.data.core.util.DateTimeUtil;

import java.util.List;

/**
 * esb服务返回结果父类
 * <AUTHOR>
public class ResponseDTO<T> extends ResponseBaseDTO<T> {

    /**
     * 消息发送者
     */
    private String sender;

    /**
     * 发送时间
     */
    private String sendTime;

    /**
     * 消息接收者
     */
    private String receiver;

    /**
     * 列表对象
     */
    private List<T> dataList;

    /**
     * 默认构造方法
     */
    public ResponseDTO() {
        super();
    }

    /**
     * @param 消息发送者
     */
    public void setSender(String sender) {
        this.sender = sender;
    }

    /**
     * @return 消息发送者
     */
    public String getSender() {
        return sender;
    }

    /**
     * @param 消息接收者
     */
    public void setReceiver(String receiver) {
        this.receiver = receiver;
    }

    /**
     * @return 消息接收者
     */
    public String getReceiver() {
        return receiver;
    }

    /**
     * @param 发送时间
     */
    public void setSendTime(String sendTime) {
        this.sendTime = sendTime;
    }

    /**
     * @return 发送时间
     */
    public String getSendTime() {
        return sendTime;
    }

    /**
     * @return 列表对象
     */
    public List<T> getDataList() {
        return dataList;
    }

    /**
     * @param 列表对象
     */
    public void setDataList(List<T> dataList) {
        this.dataList = dataList;
    }

    /**
     * 成功
     *
     * @param returnCode 返回码
     * @param returnMsg  返回信息
     * @param receiver   消息接收者
     */
    public void success(String returnCode, String returnMsg, String receiver, String sender) {
        this.setReturnCode(returnCode);
        this.setReturnMsg(returnMsg);
        this.sender = sender;
        this.sendTime = DateTimeUtil.getCurrentTimestampStr("yyyy-MM-dd HH:mm:ss");
        this.receiver = receiver;
    }

    /**
     * 失败
     *
     * @param returnCode 返回码
     * @param returnMsg  返回信息
     * @param receiver   消息接收者
     */
    public void failed(String returnCode, String returnMsg, String receiver, String sender) {
        this.setReturnCode(returnCode);
        this.setReturnMsg(returnMsg);
        this.sender = sender;
        this.sendTime = DateTimeUtil.getCurrentTimestampStr("yyyy-MM-dd HH:mm:ss");
        this.receiver = receiver;
    }

    @Override
    public String toString() {
        return "ResponseDTO{" +
                "sender='" + sender + '\'' +
                ", sendTime='" + sendTime + '\'' +
                ", receiver='" + receiver + '\'' +
                ", dataList=" + dataList +
                '}';
    }
}
