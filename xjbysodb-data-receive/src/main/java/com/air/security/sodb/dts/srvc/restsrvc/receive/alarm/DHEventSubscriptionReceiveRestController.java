package com.air.security.sodb.dts.srvc.restsrvc.receive.alarm;

import com.air.security.sodb.data.core.constant.MsgIdConstant;
import com.air.security.sodb.data.core.util.HaLog;
import com.air.security.sodb.data.core.util.RequestUtil;
import com.air.security.sodb.dts.receive.util.IClientUtils;
import com.air.security.sodb.dts.srvc.restsrvc.platform.Response;
import com.alibaba.fastjson.JSONObject;
import org.slf4j.Logger;
import org.slf4j.LoggerFactory;
import org.springframework.web.bind.annotation.CrossOrigin;
import org.springframework.web.bind.annotation.RequestMapping;
import org.springframework.web.bind.annotation.RequestMethod;
import org.springframework.web.bind.annotation.RestController;

import javax.servlet.http.HttpServletRequest;

/**
 * @Description: 大华订阅事件
 * @author: mk
 * @Date: 2020-08-13
 */
@CrossOrigin
@RestController
@RequestMapping("/api/release/subscribe")
public class DHEventSubscriptionReceiveRestController {

    private static final Logger log = LoggerFactory.getLogger(DHEventSubscriptionReceiveRestController.class);

    /**
     * 大华订阅信息接口
     */
    @RequestMapping(value = "/eventSubscription", method = RequestMethod.POST)
    public Object execute(HttpServletRequest request) {
        JSONObject jsonObj = RequestUtil.requestGetJson(request);
        HaLog.info(log, MsgIdConstant.MS_INF_0001, "大华订阅信息接入接口");
        Response str = null;
        try {
            // 执行业务处理
            if (null != jsonObj) {
                // 执行业务处理
                str = IClientUtils.eventSubscription(jsonObj);
            }
            return str;
        } catch (Exception e) {
            HaLog.error(log, e, MsgIdConstant.MS_ERR_0001);
            return str;
        } finally {
            HaLog.info(log, MsgIdConstant.MS_INF_0002, "大华订阅信息接入接口");
        }
    }

    /**
     * 大华取消订阅信息接口
     */
    @RequestMapping(value = "/unEventSubscription", method = RequestMethod.POST)
    public Object unEventSubscription(HttpServletRequest request) {
        JSONObject jsonObj = RequestUtil.requestGetJson(request);
        HaLog.info(log, MsgIdConstant.MS_INF_0001, "大华取消订阅信息接入接口");
        String str = null;
        try {
            // 执行业务处理
            if (null != jsonObj) {
                // 执行业务处理
                str = IClientUtils.eventUnSubscription(jsonObj);
            }
            return str;
        } catch (Exception e) {
            HaLog.error(log, e, MsgIdConstant.MS_ERR_0001);
            return str;
        } finally {
            HaLog.info(log, MsgIdConstant.MS_INF_0002, "大华取消订阅信息接入接口");
        }
    }

    /**
     * 获取大华所有订阅信息接口
     */
    @RequestMapping(value = "/subscribeList", method = RequestMethod.POST)
    public Object subscribeList(HttpServletRequest request) {
        JSONObject jsonObj = RequestUtil.requestGetJson(request);

        HaLog.info(log, MsgIdConstant.MS_INF_0001, "获取大华所有订阅信息接入接口");
        String str = null;
        try {
            // 执行业务处理
            if (null != jsonObj) {
                // 执行业务处理
                str = IClientUtils.subscribeList(jsonObj);
            }
            return str;
        } catch (Exception e) {
            HaLog.error(log, e, MsgIdConstant.MS_ERR_0001);
            return str;
        } finally {
            HaLog.info(log, MsgIdConstant.MS_INF_0002, "获取大华所有订阅信息接入接口");
        }
    }

}
