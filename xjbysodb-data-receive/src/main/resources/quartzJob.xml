<?xml version="1.0" encoding="UTF-8"?>
<!--定时任务配置文件： -->
<quartz>
    <job>
        <job-detail>
            <name>dhBlackCars-Job</name>
            <group>dhBlackCarsGroup</group>
            <description>大华停车场黑名单Job</description>
            <job-class>com.air.security.sodb.dts.receive.job.BlackCarsSchedulerJob</job-class>
        </job-detail>
        <trigger>
            <switch>off</switch>
            <name>dhBlackCars-Job-trigger</name>
            <group>dhBlackCarsGroup</group>
            <!-- 每分钟执行一次 -->
            <cron-expression>0 */1 * * * ?</cron-expression>
        </trigger>
    </job>

    <job>
        <job-detail>
            <name>dhChannel-Job</name>
            <group>dhChannelGroup</group>
            <description>大华门禁通道基础信息Job</description>
            <job-class>com.air.security.sodb.dts.receive.job.ChannelSchedulerJob</job-class>
        </job-detail>
        <trigger>
            <switch>on</switch>
            <name>dhChannel-Job-trigger</name>
            <group>dhChannelGroup</group>
            <!-- 每分钟执行一次 -->
            <cron-expression>0 0 1 ? * ? </cron-expression>
        </trigger>
    </job>

    <job>
        <job-detail>
            <name>dhDevice-Job</name>
            <group>dhDeviceGroup</group>
            <description>大华手报基础信息Job</description>
            <job-class>com.air.security.sodb.dts.receive.job.DeviceSchedulerJob</job-class>
        </job-detail>
        <trigger>
            <switch>on</switch>
            <name>dhDevice-Job-trigger</name>
            <group>dhDeviceGroup</group>
            <!-- 每分钟执行一次 -->
            <cron-expression>0 0 2 ? * ? </cron-expression>
        </trigger>
    </job>

    <job>
        <job-detail>
            <name>dhWhiteCars-Job</name>
            <group>dhWhiteCarsGroup</group>
            <description>大华停车场白名单Job</description>
            <job-class>com.air.security.sodb.dts.receive.job.WhiteCarsSchedulerJob</job-class>
        </job-detail>
        <trigger>
            <switch>off</switch>
            <name>dhWhiteCars-Job-trigger</name>
            <group>dhWhiteCarsGroup</group>
            <!-- 每分钟执行一次 -->
            <cron-expression>0 */1 * * * ?</cron-expression>
        </trigger>
    </job>

</quartz>