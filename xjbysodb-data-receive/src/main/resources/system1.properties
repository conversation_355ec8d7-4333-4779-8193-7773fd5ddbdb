#\u7EBF\u7A0B\u6C60\u5927\u5C0F
threadpool.count=20
rest.dh.pageSize=200
#kafkaConsumer kafka\u6D88\u8D39\u8005\u914D\u7F6E\u6587\u4EF6\u8DEF\u5F84
#kafka.consumer.config.path=D:\\config\\kafkaConsumer.properties
kafka.consumer.config.path=/server/dtsServer/xjjcDataRecv/config/kafkaConsumer.properties
#kafkaProducer kafka\u6D88\u8D39\u8005\u914D\u7F6E\u6587\u4EF6\u8DEF\u5F84
#kafka.producer.config.path=D:\\config\\kafkaProducer.properties
kafka.producer.config.path=/server/dtsServer/xjjcDataRecv/config/kafkaProducer.properties
#\u8BB0\u5F55\u6570\u636E\u4F20\u8F93\u65E5\u5FD7\u7684topic
kafka.msg.transfer.log.topic=msgTranLog
zssodb.stop.port=20032

#\u65C5\u68C0\u6570\u636E\u63A5\u5165\u4E3B\u9898
psgr.service=false
kafka.psgr.msg.input.topics=relsTopic1

#\u8D27\u68C0\u53D1\u9001\u63A5\u53E3
cargo.service=false
kafka.cargo.msg.output.topic = relsTopic2

#\u822A\u73ED\u6570\u636E\u63A5\u5165\u4E3B\u9898
flight.service=false
kafka.flight.msg.input.topics=relsTopic3

########################  \u6587\u4EF6\u670D\u52A1\u5668minio\u53C2\u6570\u76F8\u5173\u914D\u7F6E   ########################
#minio\u53C2\u6570
endpoint = http://10.6.26.152:9000
access_key = miniominio
secret_key = hayc@123
bucket = datarecv

#(\u95E8\u7981\uFF09\u95E8\u7981\u62A5\u8B66\u7C7B\u578B
doorAlarmEventType=41,43,44,46,52,54,55,62,1420,1449,1450,1433,1437,1438
#(\u5237\u5361\uFF09\u5237\u5361\u4E8B\u4EF6\u7C7B\u578B
recordEventType=42,43,44,45,46,48,49,51,52,54,55,56,57,61,62,1420,1430,1434,1435,1436,1437,1438,1439,1441,1442,1455,1456,1461,1462,1463,1464,1467,1468,1469,1470,1471,1472,1473,1474,1475,1476,1487,1488,1489,1490,1491,1492,1493,1494,4603,4604,4632,4633,701026
#\uFF08\u624B\u52A8\u62A5\u8B66\uFF09\u624B\u52A8\u62A5\u8B66\u7C7B\u578B
handAlarmEventTypes=81

handAnalyzeAlarmEventTypes=303

#\uFF08\u624B\u52A8\u62A5\u8B66\uFF09\u9ED1\u540D\u5355\u62A5\u8B66\u7C7B\u578B
blackAlarmEventType=4512,4513

# host
icc.sdk.host=*********
# \u5BA2\u6237\u7AEF\u6A21\u5F0F
icc.sdk.clientId=system
icc.sdk.clientSecret=32edc0eb-003a-48b0-a31e-2636a15afba7
# \u5BC6\u7801\u6821\u9A8C\u6A21\u5F0F
icc.sdk.pwdClientId=xxx
icc.sdk.pwdClientSecret=xxx
icc.sdk.username=system
icc.sdk.password=system123
# \u4F7F\u7528\u6388\u6743\u7C7B\u578Bpassword
icc.sdk.grantType=password
