#\u6D88\u606F\u6807\u51C6\u5316\u5904\u7406Service\u914D\u7F6E
# \u5927\u534E\u95E8\u7981\u8BBE\u5907\u4FE1\u606F
dhEquChannelInfo=MessageRecvFormatServiceImpl,cContainerEqu,DH_EQU_CHANNEL_INFO,EQU
# \u5927\u534E\u95E8\u7981\u8BBE\u5907\u72B6\u6001
dhEquChannelStatus=MessageRecvFormatServiceImpl,cContainerEqu,DH_EQU_CHANNEL_STATUS,EQU_STATUS
# \u5927\u534E\u5237\u5361\u8BB0\u5F55
dhRecordCard=MessageRecvFormatServiceImpl,cContainerRecord,DH_RECORD_CARD,RECORD_CARD
# \u5927\u534E\u95E8\u7981\u62A5\u8B66
dhChannelAlarm=MessageRecvFormatServiceImpl,cContainerAlarm,DH_CHANNEL_ALARM,ALARM

# \u5927\u534E\u624B\u62A5\u8BBE\u5907\u4FE1\u606F
dhEquHandInfo=MessageRecvFormatServiceImpl,cContainerEqu,DH_EQU_HAND_INFO,EQU
# \u5927\u534E\u624B\u62A5\u8BBE\u5907\u72B6\u6001
dhEquHandStatus=MessageRecvFormatServiceImpl,cContainerEqu,DH_EQU_HAND_STATUS,EQU_STATUS
# \u5927\u534E\u624B\u62A5\u62A5\u8B66\u4FE1\u606F
dhHandAlarm=MessageRecvFormatServiceImpl,cContainerAlarm,DH_HAND_ALARM,ALARM

dhHandAnalyzeAlarm=MessageRecvFormatServiceImpl,cContainerAlarm,DH_ANALYZE_ALARM,ALARM

#\u505C\u8F66\u573A\u9ED1\u540D\u5355\u63A5\u5165
dhCarBlackInfo = MessageRecvFormatServiceImpl,cContainerCar,DH_CAR_BLACK_INFO,CAR_DATA
#\u505C\u8F66\u573A\u767D\u540D\u5355\u63A5\u5165
dhCarWhiteInfo = MessageRecvFormatServiceImpl,cContainerCar,DH_CAR_WHITE_INFO,CAR_DATA
#\u505C\u8F66\u573A\u62A5\u8B66\u63A5\u5165
dhCarAlarm = MessageRecvFormatServiceImpl,cContainerAlarm,DH_CAR_ALARM,ALARM
#\u505C\u8F66\u573A\u8FDB\u51FA\u8F66\u8F86\u63A5\u5165
dhCarParkInfo = HhCarParkInfoMessageRecvFormatServiceImpl,cContainerCar,DH_CAR_PARK_INFO,CAR_DATA

#\u8D27\u8FD0\u5B89\u68C0\u7CFB\u7EDF-\u8FD0\u5355\u57FA\u672C\u4FE1\u606F
WAY_BILL_LOG=MessageRecvFormatServiceImpl,cContainerCargo,WAY_BILL_LOG,CARGO
#\u8D27\u8FD0\u5B89\u68C0\u7CFB\u7EDF-\u8FD0\u5355\u5F00\u5305\u4FE1\u606F
OPEN_LOG=CargoOpenMessageRecvFormatServiceImpl,cContainerCargo,OPEN_LOG,CARGO
#\u8D27\u8FD0\u5B89\u68C0\u7CFB\u7EDF-\u8FD0\u5355\u5B89\u68C0\u4FE1\u606F
SECURITY_LOG=MessageRecvFormatServiceImpl,cContainerCargo,SECURITY_LOG,CARGO
#\u8D27\u8FD0\u5B89\u68C0\u7CFB\u7EDF-\u5B89\u68C0\u901A\u9053\u7684\u5F00\u653E\u4FE1\u606F
CHECKSTATUS_EQU=MessageRecvFormatServiceImpl,cContainerCargo,CHECKSTATUS_EQU,CARGO

#\u65C5\u68C0\u6570\u636E
PRSC=ScimsImgMessageRecvFormatServiceImpl,cContainerPassenger,PSGR_SECURITY,PSGR_DATA
check=ScimsImgMessageRecvFormatServiceImpl,cContainerPassenger,PRSCJSON_SECURITY,PSGR_DATA
bag=ScimsImgMessageRecvFormatServiceImpl,cContainerPassenger,PSGR_OPEN_INFO,PSGR_DATA

flight=MessageRecvFormatServiceImpl,cContainerFlight,FLIGHT_DYN,FLIGHT_DATA
