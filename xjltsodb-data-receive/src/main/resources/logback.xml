<?xml version="1.0" encoding="UTF-8"?>
<configuration scan="true" scanPeriod="60 seconds" debug="false">
    <property name="PRO_NAME" value="hk"></property>
    <property name="RECEIVE_NAME" value="hkReceive"></property>
    <property name="SAVE_NAME" value="hkSave"></property>
    <property name="SRVC_NAME" value="hkSrvc"></property>
    <property name="DTS_HOME" value="/opt/log/xjltRecv"></property>
    <!-- scan:属性设置为true，配置文件如果发生改变，将会被重新加载，默认为true -->
    <!-- scanPeriod:设置监测配置文件是否有修改的时间间隔，如果没有给出时间单位，默认单位是毫秒。当scan为true时，此属性生效。默认的时间间隔为1分钟 -->
    <!-- debug:当此属性设置为true时，将打印出logback内部日志信息，实时查看logback运行状态。默认值为false -->
    <appender name="CONSOLE" class="ch.qos.logback.core.ConsoleAppender">
        <layout class="ch.qos.logback.classic.PatternLayout">
            <!-- <pattern>%d{HH:mm:ss.SSS} [%thread] [%c] %-5p - %m%n</pattern> -->
            <!--device模块 <pattern>%d{yyyy-MM-dd HH:mm:ss.SSS} [%thread] [%c] %-5p - %m%n</pattern>-->
            <!--格式化输出：%d表示日期，%thread表示线程名，%-5level：级别从左显示5个字符宽度%msg：日志消息，%n是换行符-->
            <!--%logger{50}: 50 表示长度，代表输出包是完整输出还是简短输出，至少包括包的首字母-->
            <pattern>[${PRO_NAME}] %d{HH:mm:ss.SSS} [%thread] %-5level %logger{50} - %msg%n</pattern>
        </layout>
    </appender>
    <appender name="FILE" class="ch.qos.logback.core.rolling.RollingFileAppender">
        <!--被写入的文件名，可以是相对目录，也可以是绝对目录，如果上级目录不存在，自动创建，无默认值 ,当EMM_LOG_HOME 无值时取：C:/emmlog -->
        <file>${DTS_HOME}/${PRO_NAME}.log</file>
        <!-- 日志文件固定窗口滚动策略，固定归档文件序号 minIndex ~ maxIndex -->
        <!-- <rollingPolicy class="ch.qos.logback.core.rolling.FixedWindowRollingPolicy">
            <fileNamePattern>${DTS_HOME}/log/glsodb.%i.log.zip</fileNamePattern>
            <minIndex>1</minIndex>
            <maxIndex>12</maxIndex>
        </rollingPolicy> -->
        <!-- 基于文件尺寸大小触发归档策略 (配置按每天归档时 不需要此配置 )-->
        <!-- <triggeringPolicy class="ch.qos.logback.core.rolling.SizeBasedTriggeringPolicy">
            <maxFileSize>30MB</maxFileSize>
        </triggeringPolicy> -->
        <!-- 日志文件固定窗口滚动策略，每日生成一个日志文件 最大保存90天（3个月） -->
        <rollingPolicy class="ch.qos.logback.core.rolling.TimeBasedRollingPolicy">
            <fileNamePattern>${DTS_HOME}/${PRO_NAME}.%d{yyyy-MM-dd-HH}.log.zip</fileNamePattern>
            <maxHistory>90</maxHistory>
        </rollingPolicy>
        <!-- 日志输出格式 -->
        <!-- %d表示日期，%thread表示线程名，%-5level：级别从左显示5个字符宽度     -->
        <!--%logger{50}: 50 表示长度，代表输出包是完整输出还是简短输出，至少包括包的首字母-->
        <encoder>
            <pattern>[${PRO_NAME}] %d{yyyy-MM-dd HH:mm:ss.SSS}[%thread] %-5level %logger{50} - %msg%n</pattern>
        </encoder>
    </appender>

    <appender name="FILE_ERROR"
              class="ch.qos.logback.core.rolling.RollingFileAppender">
        <file>${DTS_HOME}/${PRO_NAME}_error.log</file> <!-- 滚动方式 300M -->
        <filter class="ch.qos.logback.classic.filter.LevelFilter">
            <level>ERROR</level>
            <onMatch>ACCEPT</onMatch>
            <onMismatch>DENY</onMismatch>
        </filter>
        <rollingPolicy class="ch.qos.logback.core.rolling.TimeBasedRollingPolicy">
            <fileNamePattern>${DTS_HOME}/${PRO_NAME}_error.%d{yyyy-MM-dd-HH}.log.zip</fileNamePattern>
            <maxHistory>90</maxHistory>
        </rollingPolicy>
        <encoder>
            <pattern>[${PRO_NAME}] %date{YYYY-MM-dd HH:mm:ss} %level [%thread] %logger{10}
                [%file:%line] %msg%n
            </pattern>
        </encoder>
    </appender>

    <appender name="DTS_RECEIVE_JOB" class="ch.qos.logback.core.rolling.RollingFileAppender">
        <File>${DTS_HOME}/${RECEIVE_NAME}.log</File>
        <append>true</append>
        <filter class="ch.qos.logback.classic.filter.LevelFilter">
            <level>INFO</level>
            <onMatch>ACCEPT</onMatch>
            <onMismatch>DENY</onMismatch>
        </filter>
        <rollingPolicy class="ch.qos.logback.core.rolling.TimeBasedRollingPolicy">
            <fileNamePattern>${DTS_HOME}/${RECEIVE_NAME}.%d{yyyy-MM-dd}.log.zip</fileNamePattern>
            <maxHistory>90</maxHistory>
        </rollingPolicy>
        <encoder charset="UTF-8">
            <pattern>[%date{YYYY-MM-dd HH:mm:ss}] %msg%n</pattern>
            <charset>UTF-8</charset>
        </encoder>
    </appender>
    <appender name="DTS_RECEIVE_JOB_ERROR"
              class="ch.qos.logback.core.rolling.RollingFileAppender">
        <file>${DTS_HOME}/${RECEIVE_NAME}_error.log</file> <!-- 滚动方式 300M -->
        <filter class="ch.qos.logback.classic.filter.LevelFilter">
            <level>ERROR</level>
            <onMatch>ACCEPT</onMatch>
            <onMismatch>DENY</onMismatch>
        </filter>
        <rollingPolicy class="ch.qos.logback.core.rolling.TimeBasedRollingPolicy">
            <fileNamePattern>${DTS_HOME}/${RECEIVE_NAME}_error.%d{yyyy-MM-dd}.log.zip</fileNamePattern>
            <maxHistory>90</maxHistory>
        </rollingPolicy>
        <encoder>
            <pattern>[${RECEIVE_NAME}] %date{YYYY-MM-dd HH:mm:ss} %level [%thread] %logger{10}
                [%file:%line] %msg%n
            </pattern>
        </encoder>
    </appender>

    <appender name="DTS_SAVE_JOB" class="ch.qos.logback.core.rolling.RollingFileAppender">
        <File>${DTS_HOME}/${SAVE_NAME}.log</File>
        <append>true</append>
        <filter class="ch.qos.logback.classic.filter.LevelFilter">
            <level>INFO</level>
            <onMatch>ACCEPT</onMatch>
            <onMismatch>DENY</onMismatch>
        </filter>
        <rollingPolicy class="ch.qos.logback.core.rolling.TimeBasedRollingPolicy">
            <fileNamePattern>${DTS_HOME}/${SAVE_NAME}.%d{yyyy-MM-dd}.log.zip</fileNamePattern>
            <maxHistory>90</maxHistory>
        </rollingPolicy>
        <encoder charset="UTF-8">
            <pattern>[%date{YYYY-MM-dd HH:mm:ss}] %msg%n</pattern>
            <charset>UTF-8</charset>
        </encoder>
    </appender>
    <appender name="DTS_SAVE_JOB_ERROR"
              class="ch.qos.logback.core.rolling.RollingFileAppender">
        <file>${DTS_HOME}/${SAVE_NAME}_error.log</file> <!-- 滚动方式 300M -->
        <filter class="ch.qos.logback.classic.filter.LevelFilter">
            <level>ERROR</level>
            <onMatch>ACCEPT</onMatch>
            <onMismatch>DENY</onMismatch>
        </filter>
        <rollingPolicy class="ch.qos.logback.core.rolling.TimeBasedRollingPolicy">
            <fileNamePattern>${DTS_HOME}/${SAVE_NAME}_error.%d{yyyy-MM-dd}.log.zip</fileNamePattern>
            <maxHistory>90</maxHistory>
        </rollingPolicy>
        <encoder>
            <pattern>[${SAVE_NAME}] %date{YYYY-MM-dd HH:mm:ss} %level [%thread] %logger{10}
                [%file:%line] %msg%n
            </pattern>
        </encoder>
    </appender>

    <appender name="DTS_SRVC_JOB" class="ch.qos.logback.core.rolling.RollingFileAppender">
        <File>${DTS_HOME}/${SRVC_NAME}.log</File>
        <append>true</append>
        <filter class="ch.qos.logback.classic.filter.LevelFilter">
            <level>INFO</level>
            <onMatch>ACCEPT</onMatch>
            <onMismatch>DENY</onMismatch>
        </filter>
        <rollingPolicy class="ch.qos.logback.core.rolling.TimeBasedRollingPolicy">
            <fileNamePattern>${DTS_HOME}/${SRVC_NAME}.%d{yyyy-MM-dd}.log.zip</fileNamePattern>
            <maxHistory>90</maxHistory>
        </rollingPolicy>
        <encoder charset="UTF-8">
            <pattern>[%date{YYYY-MM-dd HH:mm:ss}] %msg%n</pattern>
            <charset>UTF-8</charset>
        </encoder>
    </appender>
    <appender name="DTS_SRVC_JOB_ERROR"
              class="ch.qos.logback.core.rolling.RollingFileAppender">
        <file>${DTS_HOME}/${SRVC_NAME}_error.log</file> <!-- 滚动方式 300M -->
        <filter class="ch.qos.logback.classic.filter.LevelFilter">
            <level>ERROR</level>
            <onMatch>ACCEPT</onMatch>
            <onMismatch>DENY</onMismatch>
        </filter>
        <rollingPolicy class="ch.qos.logback.core.rolling.TimeBasedRollingPolicy">
            <fileNamePattern>${DTS_HOME}/${SRVC_NAME}_error.%d{yyyy-MM-dd}.log.zip</fileNamePattern>
            <maxHistory>90</maxHistory>
        </rollingPolicy>
        <encoder>
            <pattern>[${SRVC_NAME}] %date{YYYY-MM-dd HH:mm:ss} %level [%thread] %logger{10}
                [%file:%line] %msg%n
            </pattern>
        </encoder>
    </appender>

    <appender name="MSG_TRAN_LOG" class="ch.qos.logback.core.rolling.RollingFileAppender">
        <!--被写入的文件名，可以是相对目录，也可以是绝对目录，如果上级目录不存在，自动创建，无默认值 ,当EMM_LOG_HOME 无值时取：C:/emmlog -->
        <file>${DTS_HOME}/${TRANSLOG_NAME}.log</file>
        <!-- 日志文件固定窗口滚动策略，每日生成一个日志文件 最大保存90天（3个月） -->
        <rollingPolicy class="ch.qos.logback.core.rolling.TimeBasedRollingPolicy">
            <fileNamePattern>${DTS_HOME}/${TRANSLOG_NAME}.%d{yyyy-MM-dd-HH}.log.zip</fileNamePattern>
            <maxHistory>90</maxHistory>
        </rollingPolicy>
        <encoder>
            <pattern>%msg%n</pattern>
        </encoder>
    </appender>

    <logger name="msgTranLog" level="INFO" additivity="true">
        <appender-ref ref="MSG_TRAN_LOG"/>
    </logger>


    <logger name="com.air.security.sodb.receive" level="INFO">
        <appender-ref ref="DTS_RECEIVE_JOB"/>
    </logger>
    <logger name="com.air.security.sodb.receive" level="ERROR">
        <appender-ref ref="DTS_RECEIVE_JOB_ERROR"/>
    </logger>
    <logger name="com.air.security.sodb.save" level="INFO">
        <appender-ref ref="DTS_SAVE_JOB"/>
    </logger>
    <logger name="com.air.security.sodb.save" level="ERROR">
        <appender-ref ref="DTS_SAVE_JOB_ERROR"/>
    </logger>
    <logger name="com.air.security.sodb.srvc" level="INFO">
        <appender-ref ref="DTS_SRVC_JOB"/>
    </logger>
    <logger name="com.air.security.sodb.srvc" level="ERROR">
        <appender-ref ref="DTS_SRVC_JOB_ERROR"/>
    </logger>
    <logger name="com.air.security.sodb" level="DEBUG"/>
    <logger name="org.springframework" level="ERROR"/>
    <logger name="org.apache" level="ERROR"/>
    <logger name="com.air.security.sodb" level="DEBUG"/>
    <!--root-->
    <root>
        <!-- level：用来设置打印级别，大小写无关：TRACE, DEBUG, INFO, WARN, ERROR, ALL 和 OFF，不能设置为INHERITED或者同义词NULL -->
        <level value="INFO"/>
        <appender-ref ref="CONSOLE"/>
        <appender-ref ref="FILE"/>
        <appender-ref ref="FILE_ERROR" level="ERROR"/>
    </root>
</configuration>