#\u8BBE\u7F6E\u4E0A\u4E0B\u6587\u6839 \u9ED8\u8BA4 \u201C/\u201D
#server.servlet.path=/sodb-ams
#\u8BBE\u7F6E\u8BBF\u95EE\u7AEF\u53E3 \u9ED8\u8BA4\u201C8080\u201D
server.port=11014
#\u6587\u4EF6\u4E0A\u4F20\u5927\u5C0F\u914D\u7F6E
spring.http.multipart.maxFileSize=100Mb
spring.http.multipart.maxRequestSize=100Mb
spring.activiti.check-process-definitions=false
spring.autoconfigure.exclude=org.springframework.boot.autoconfigure.jdbc.DataSourceAutoConfiguration,org.springframework.boot.autoconfigure.jdbc.DataSourceTransactionManagerAutoConfiguration
db.type=mysql
#\u6570\u636E\u5E93\u8FDE\u63A5\u914D\u7F6E\uFF1Adts
datasource.driverClassName=com.mysql.jdbc.Driver
datasource.url=*****************************************************************************************************************
#datasource.url=********************************************
datasource.username=root
datasource.password=XJJCabzx@2021
datasource.initialSize=5
datasource.maxActive=300
datasource.maxIdle=10
datasource.minIdle=2
datasource.maxWait=60000
datasource.validationQuery=select VERSION()
datasource.testOnBorrow=true
datasource.testOnReturn=true
datasource.timeBetweenEvictionRunsMillis=60000
datasource.minEvictableIdleTimeMillis=180000
datasource.removeAbandoned=true
datasource.removeAbandonedTimeout=250

#redis\u914D\u7F6E
redis.host=**********
#redis.host=**************
redis.port=6379
redis.pass=123456
redis.maxIdle=300
redis.maxTotal=200
redis.maxWaitMillis=2000
redis.testOnBorrow=true
redis.timeout=2000