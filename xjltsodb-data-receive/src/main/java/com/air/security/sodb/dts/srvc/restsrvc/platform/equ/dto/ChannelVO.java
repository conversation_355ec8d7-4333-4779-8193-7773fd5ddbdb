package com.air.security.sodb.dts.srvc.restsrvc.platform.equ.dto;

import lombok.Data;

/**
 * 通道信息实体类
 */
@Data
public class ChannelVO {
    /**
     * 通道编码
     */
    private String channelCode;

    /**
     * 通道名称
     */
    private String channelName;

    /**
     * 通道序号
     */
    private int channelSeq;

    /**
     * 通道唯一标识码
     */
    private String channelSn;

    /**
     * 通道类型
     */
    private String channelType;

    /**
     * 通道能力集
     */
    private String capability;

    /**
     * 是否已接入：0-未接入，1-接入
     */
    private int access;

    /**
     * 经度
     */
    private String gpsX;

    /**
     * 纬度
     */
    private String gpsY;

    /**
     * Z轴
     */
    private String gpsZ;

    /**
     * 通道描述
     */
    private String memo;

    /**
     * 状态 ：0-不启用，1-启用
     */
    private int stat;

    /**
     * 通道扩展属性 自定义 ，具体参考附录->设备扩展信息->通道扩展协议
     */
    private Object chExt;

}

