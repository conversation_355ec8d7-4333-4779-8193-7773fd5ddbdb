package com.air.security.sodb.dts.srvc.restsrvc.receive.alarm;

import com.air.security.sodb.data.core.base.Meta;
import com.air.security.sodb.data.core.constant.MsgIdConstant;
import com.air.security.sodb.data.core.util.HaLog;
import com.air.security.sodb.data.core.util.PropertyUtil;
import com.air.security.sodb.data.core.util.RequestUtil;
import com.air.security.sodb.data.core.util.UuidUtil;
import com.air.security.sodb.dts.receive.server.MqMessageRecvServer;
import com.air.security.sodb.dts.receive.server.MqMessageRecvServerImpl;
import com.alibaba.fastjson.JSONArray;
import com.alibaba.fastjson.JSONObject;
import org.slf4j.Logger;
import org.slf4j.LoggerFactory;
import org.springframework.web.bind.annotation.CrossOrigin;
import org.springframework.web.bind.annotation.RequestMapping;
import org.springframework.web.bind.annotation.RequestMethod;
import org.springframework.web.bind.annotation.RestController;

import javax.servlet.http.HttpServletRequest;
import java.util.Arrays;
import java.util.List;
import java.util.Optional;
import java.util.stream.Collectors;

/**
 * @Description: 大华订阅获取数据
 * @author: mk
 * @Date: 2020-08-13
 */
@CrossOrigin
@RestController
@RequestMapping("/api/release/DH/subscriptionData")
public class DHSubscriptionDataReceive {

    private static final Logger log = LoggerFactory.getLogger(DHSubscriptionDataReceive.class);

    private static final Logger log1 = LoggerFactory.getLogger("msgTranLog");

    private static String doorAlarmEventType = PropertyUtil.getProperty("doorAlarmEventType");

    private static String recordEventType = PropertyUtil.getProperty("recordEventType");

    private static String handAlarmEventTypes = PropertyUtil.getProperty("handAlarmEventTypes");

    private static String handAnalyzeAlarmEventTypes = PropertyUtil.getProperty("handAnalyzeAlarmEventTypes");

    private static String blackAlarmEventType = PropertyUtil.getProperty("blackAlarmEventType");
    /**
     *
     */
    private MqMessageRecvServer server = new MqMessageRecvServerImpl();

    /**
     * 大华订阅信息接口
     */
    @RequestMapping(value = "/receive", method = RequestMethod.POST)
    public void execute(HttpServletRequest request) {
        HaLog.info(log, MsgIdConstant.MS_INF_0001, "大华订阅信息接入接口");
        JSONObject jsonObj = RequestUtil.requestGetJson(request);
        HaLog.infoJson(log1, jsonObj.toJSONString());
        String category = jsonObj.getString("category");
        String method = jsonObj.getString("method");
        JSONObject info = jsonObj.getJSONObject("info");
        HaLog.info(log, MsgIdConstant.MS_INF_0001,  "category:" + category + ",method:" + method + ",info:" + info );

        // 使用 Optional 避免空指针异常
        Optional.ofNullable(info).ifPresent(information -> {
            switch (category) {
                case "alarm":
                    if ("alarm.msg".equals(method)) {
                        alarmData(information);
                    }
                    break;
                case "state":
                    if ("node.status".equals(method)) {
                        stateData(information);
                    }
                    break;
                case "business":

                    businessData(information, method);
                    break;
            }
        });
        HaLog.info(log, MsgIdConstant.MS_INF_0002, "大华订阅信息接入接口");
    }

    /**
     * 处理业务数据
     *
     * @param info   信息
     * @param method 方法
     */
    private void businessData(JSONObject info, String method) {
        switch (method) {
            case "admin.car.inspection.report":
                handleMeta("dhCrossingCarPass", info);
            case "car.access":
                handleMeta("dhCarParkInfo", info);
                break;
            case "alarmhost.status":
                processAlarmHostStatus(info);
                break;
        }
    }

    /**
     * 处理警报主机状态
     *
     * @param info 信息
     */
    private void processAlarmHostStatus(JSONObject info) {
        String sendTime = info.getString("sendTime");
        JSONObject alarmHostBean = info.getJSONObject("alarmHostBean");

        if (alarmHostBean != null) {
            JSONArray subSystemList = alarmHostBean.getJSONArray("subSystemList");

            if (subSystemList != null && subSystemList.size() > 0) {
                subSystemList.forEach(o -> {
                    JSONObject jsonObject = (JSONObject) o;
                    JSONArray defenceAreaList = jsonObject.getJSONArray("defenceAreaList");

                    if (defenceAreaList != null && defenceAreaList.size() > 0) {
                        defenceAreaList.forEach(o1 -> {
                            JSONObject jsonObject1 = (JSONObject) o1;
                            jsonObject1.put("sendTime", sendTime);
                            handleMeta("dhEquHandStatus", jsonObject1);
                        });
                    }
                });
            }
        }
    }

    /**
     * 处理状态数据
     *
     * @param info 信息
     */
    private void stateData(JSONObject info) {
        handleMeta("dhEquChannelStatus", info);
    }

    /**
     * 处理警报数据
     *
     * @param info 信息
     */
    private void alarmData(JSONObject info) {
        String alarmType = info.getString("alarmType");
        String alarmStat = info.getString("alarmStat");
        String nodeType = info.getString("nodeType");

        if (!"1".equals(alarmStat)) {
            return;
        }

        List<String> doorAlarmEventTypes = Arrays.stream(doorAlarmEventType.split(",")).map(String::trim).collect(Collectors.toList());
        List<String> recordEventTypes = Arrays.stream(recordEventType.split(",")).map(String::trim).collect(Collectors.toList());
        List<String> blackAlarmEventTypes = Arrays.stream(blackAlarmEventType.split(",")).map(String::trim).collect(Collectors.toList());
        List<String> handAlarmEventType = Arrays.stream(handAlarmEventTypes.split(",")).map(String::trim).collect(Collectors.toList());
        List<String> handAnalyzeAlarmEventType = Arrays.stream(handAnalyzeAlarmEventTypes.split(",")).map(String::trim).collect(Collectors.toList());

        if ("2".equals(nodeType)) {
            //门禁报警
            if (doorAlarmEventTypes.contains(alarmType)) {
                handleMeta("dhChannelAlarm", info);
            }
            //刷卡事件
            if (recordEventTypes.contains(alarmType)) {
                handleMeta("dhRecordCard", info);
            }
            //手报报警
            if (handAlarmEventType.contains(alarmType)) {
                handleMeta("dhHandAlarm", info);
            }
        } else {
            //黑名单车辆报警
            if (blackAlarmEventTypes.contains(alarmType)) {
                handleMeta("dhCarAlarm", info);
            }
            if (handAnalyzeAlarmEventType.contains(alarmType)) {
                handleMeta("dhHandAnalyzeAlarm", info);
            }
        }
    }

    /**
     * 处理元数据
     *
     * @param eventType 事件类型
     * @param info      信息
     */
    private void handleMeta(String eventType, JSONObject info) {
        Meta meta = new Meta();
        meta.setEventType(eventType);
        meta.setRecvSequence(UuidUtil.getUuid32());
        server.handle(meta, info.toJSONString());
    }


}
