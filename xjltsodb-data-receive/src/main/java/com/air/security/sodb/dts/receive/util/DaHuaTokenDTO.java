package com.air.security.sodb.dts.receive.util;

public class DaHuaTokenDTO {
    private String token;
    private String id;
    private String loginName;

    public String getToken() {
        return token;
    }

    public void setToken(String token) {
        this.token = token;
    }

    public String getId() {
        return id;
    }

    public void setId(String id) {
        this.id = id;
    }

    public String getLoginName() {
        return loginName;
    }

    public void setLoginName(String loginName) {
        this.loginName = loginName;
    }

    @Override
    public String toString() {
        return "DaHuaTokenDTO{" +
                "token='" + token + '\'' +
                ", id='" + id + '\'' +
                ", loginName='" + loginName + '\'' +
                '}';
    }
}
