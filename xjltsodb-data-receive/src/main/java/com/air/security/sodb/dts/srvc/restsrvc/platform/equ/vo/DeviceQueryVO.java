package com.air.security.sodb.dts.srvc.restsrvc.platform.equ.vo;

import lombok.Data;

import java.util.List;

/**
 * 设备查询实体类
 */
@Data
public class DeviceQueryVO {
    /**
     * 当前页, 默认1
     */
    private Integer pageNum;

    /**
     * 每页记录数, 默认10
     */
    private Integer pageSize;

    /**
     * 设备所属组织编码集合
     */
    private List<String> ownerCodes;

    /**
     * 是否获取设备所属组织子节点下设备记录
     */
    private Integer showChildNodeData;

    /**
     * 单元类型,多个
     */
    private List<Integer> unitTypes;

    /**
     * 设备大类
     */
    private List<String> categorys;

    /**
     * 设备小类,设备小类的格式是:大类_小类，categorys与types同时存在，此字段无效
     */
    private List<String> types;

    /**
     * 设备编码列表 最大支持500
     */
    private List<String> deviceCodes;

    /**
     * 设备标识码列表 最大支持500
     */
    private List<String> deviceSns;

    /**
     * 设备地址 最大支持500
     */
    private List<String> deviceIps;

    /**
     * 在线状态
     */
    private Integer isOnline;

    /**
     * 0:获取非虚拟设备 1 只获取虚拟设备
     */
    private Integer syncVirtualData;
}

