package com.air.security.sodb.dts.receive.listener;

import com.air.security.sodb.data.core.base.Meta;
import com.air.security.sodb.data.core.constant.GlobalCodeConstant;
import com.air.security.sodb.data.core.constant.MsgIdConstant;
import com.air.security.sodb.data.core.util.*;
import com.air.security.sodb.dts.receive.server.MqMessageRecvServer;
import com.air.security.sodb.dts.receive.server.MqMessageRecvServerImpl;
import com.alibaba.fastjson.JSON;
import com.alibaba.fastjson.JSONObject;
import org.apache.commons.lang.StringUtils;
import org.apache.kafka.clients.consumer.ConsumerRecord;
import org.apache.kafka.clients.consumer.ConsumerRecords;
import org.apache.kafka.clients.consumer.KafkaConsumer;
import org.slf4j.Logger;
import org.slf4j.LoggerFactory;

import java.time.Duration;
import java.util.Objects;

/**
 * @Description: 围界设备状态变更数据同步消息监听
 * @author: zhangc
 * @Date: 2018年12月18日
 */
public class KafkaWJEquStateChangeSyncMsgListener implements Runnable {

	private static final Logger log = LoggerFactory.getLogger(KafkaWJEquStateChangeSyncMsgListener.class);

	/**
	 * 消息分发处理器
	 */
	private MqMessageRecvServer server = new MqMessageRecvServerImpl();

	/**
	 * 报警消息发布服务主题
	 */
	private static String topic = PropertyUtil.getProperty("kafka.wj.equ.state.change.msg.input.topics");

	@Override
	public void run() {

		HaLog.info(log, MsgIdConstant.MS_INF_0001, "kafka事件消息监听");

		KafkaConsumer<String, String> consumer = KafkaConscumerUtil.getConsumer(new String[] { topic });
		while (true) {
			ThreadUtil.sleepThreadUnit();

			// 消费消息
			ConsumerRecords<String, String> records = consumer.poll(Duration.ZERO);
			for (ConsumerRecord<String, String> record : records) {
				String message = record.value();

				if (StringUtils.isBlank(message)) {
					continue;
				}
				// 接收消息日志
				HaLog.infoJson(log, message);

				try {
					JSONObject jsonObject = JSONObject.parseObject(message);

					if (jsonObject == null) {
						return;
					}

					// 获取消息头标签
					Meta meta = JSON.parseObject(jsonObject.getString("meta"), Meta.class);
					if (null == meta) {
						return;
					}

					meta.setRecvTime(DateTimeUtil.getCurrentTimestampStr("yyyyMMddHHmmss"));

					// 记录数据交互日志
					DataTransferLogRecordUtil.record(meta, jsonObject, GlobalCodeConstant.LOG_TYPE_RECV,
							GlobalCodeConstant.LOG_SERVICE_TYPE_RELS);

					// 获取消息头标签的消息类型styp
					String eventType = meta.getEventType();
					if (StringUtils.isNotBlank(eventType)) {

						JSONObject body = jsonObject.getJSONObject("body");

						if (Objects.isNull(body)) {
							return;
						}
						String devices = body.getString("device_status");

						server.handle(meta, devices);
					}

				} catch (Exception e) {
					HaLog.error(log, e, MsgIdConstant.MS_ERR_0001);
				}
			}
		}
	}

}
