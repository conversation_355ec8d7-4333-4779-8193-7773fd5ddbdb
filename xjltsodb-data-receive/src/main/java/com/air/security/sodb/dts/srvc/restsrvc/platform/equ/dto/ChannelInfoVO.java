package com.air.security.sodb.dts.srvc.restsrvc.platform.equ.dto;

import lombok.Data;


@Data
public class ChannelInfoVO {
    /**
     * 自增ID
     */
    private Long id;
    /**
     * 设备编码
     */
    private String deviceCode;
    /**
     * 单元类型
     */
    private Integer unitType;
    /**
     * 单元序号
     */
    private Integer unitSeq;
    /**
     * 通道序号
     */
    private Integer channelSeq;
    /**
     * 通道编码
     */
    private String channelCode;
    /**
     * 通道标识码
     */
    private String channelSn;
    /**
     * 通道名称
     */
    private String channelName;
    /**
     * 通道类型
     */
    private String channelType;
    /**
     * 摄像头类型
     */
    private String cameraType;
    /**
     * 所属组织编码
     */
    private String ownerCode;
    /**
     * 经度
     */
    private String gpsX;
    /**
     * 纬度
     */
    private String gpsY;
    /**
     * z轴
     */
    private String gpsZ;
    /**
     * 光栅图ID
     */
    private Long mapId;
    /**
     * 域ID
     */
    private Long domainId;
    /**
     * 描述
     */
    private String memo;
    /**
     * 设备通道在线状态
     */
    private Integer isOnline;
    /**
     * 状态 0:关闭 1:开启
     */
    private Integer stat;
    /**
     * 是否已经接入 ：1-已接入， 0-未接入
     */
    private Integer access;
    /**
     * 能力集
     */
    private String capability;
    /**
     * 是否是虚拟通道
     */
    private String isVirtual;
    /**
     * 通道详情扩展信息,json体，具体参考附录->设备扩展信息->通道扩展协议
     */
    private String chExt;
}

