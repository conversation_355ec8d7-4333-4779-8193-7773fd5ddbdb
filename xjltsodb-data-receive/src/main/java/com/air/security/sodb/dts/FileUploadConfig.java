package com.air.security.sodb.dts;

import org.springframework.context.annotation.Bean;
import org.springframework.context.annotation.Configuration;
import org.springframework.web.multipart.commons.CommonsMultipartResolver;

/**
 * <AUTHOR>
 * @Description:文件上传配置
 * @date 2018年10月23日
 */
@Configuration
public class FileUploadConfig {

    @Bean
    public CommonsMultipartResolver multipartResolver() {
        CommonsMultipartResolver resolver = new CommonsMultipartResolver();
        resolver.setDefaultEncoding("UTF-8");

        //上传文件大小 100M
        resolver.setMaxUploadSize(100 * 1024 * 1024);
        return resolver;
    }
}
