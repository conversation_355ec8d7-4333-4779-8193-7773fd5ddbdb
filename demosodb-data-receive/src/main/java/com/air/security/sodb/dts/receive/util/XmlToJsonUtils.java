package com.air.security.sodb.dts.receive.util;

import de.odysseus.staxon.json.JsonXMLConfig;
import de.odysseus.staxon.json.JsonXMLConfigBuilder;
import de.odysseus.staxon.json.JsonXMLInputFactory;
import de.odysseus.staxon.json.JsonXMLOutputFactory;
import de.odysseus.staxon.xml.util.PrettyXMLEventWriter;

import javax.xml.stream.XMLEventReader;
import javax.xml.stream.XMLEventWriter;
import javax.xml.stream.XMLInputFactory;
import javax.xml.stream.XMLOutputFactory;
import java.io.IOException;
import java.io.StringReader;
import java.io.StringWriter;

/**
 * json and xml converter
 * <AUTHOR>
 * @see https://github.com/beckchr/staxon
 * @see https://github.com/beckchr/staxon/wiki
 *
 */
public class XmlToJsonUtils {

    /**
     * json string convert to xml string
     */
    public static String json2xml(String json){
        StringReader input = new StringReader(json);
        StringWriter output = new StringWriter();
        JsonXMLConfig config = new JsonXMLConfigBuilder().multiplePI(false).repairingNamespaces(false).build();
        try {
            XMLEventReader reader = new JsonXMLInputFactory(config).createXMLEventReader(input);
            XMLEventWriter writer = XMLOutputFactory.newInstance().createXMLEventWriter(output);
            writer = new PrettyXMLEventWriter(writer);
            writer.add(reader);
            reader.close();
            writer.close();
        } catch( Exception e){
            e.printStackTrace();
        } finally {
            try {
                output.close();
                input.close();
            } catch (IOException e) {
                e.printStackTrace();
            }
        }
        if(output.toString().length()>=38){//remove <?xml version="1.0" encoding="UTF-8"?>
            return output.toString().substring(39);
        }
        return output.toString();
    }

    /**
     * xml string convert to json string
     */
    public static String xml2json(String xml){
        StringReader input = new StringReader(xml);
        StringWriter output = new StringWriter();
        JsonXMLConfig config = new JsonXMLConfigBuilder().autoArray(true).autoPrimitive(true).prettyPrint(true).build();
        try {
            XMLEventReader reader = XMLInputFactory.newInstance().createXMLEventReader(input);
            XMLEventWriter writer = new JsonXMLOutputFactory(config).createXMLEventWriter(output);
            writer.add(reader);
            reader.close();
            writer.close();
        } catch( Exception e){
            e.printStackTrace();
        } finally {
            try {
                output.close();
                input.close();
            } catch (IOException e) {
                e.printStackTrace();
            }
        }
        return output.toString();
    }

    public static void main(String[] args) {
        String sb ="<?xml version=\"1.0\" encoding=\"utf-8\"?>\n" +
                "\n" +
                "<MSG> \n" +
                "  <HEAD> \n" +
                "    <SND>HSDB</SND>  \n" +
                "    <RCV>EXCH</RCV>  \n" +
                "    <SEND_TIME>20210308120987</SEND_TIME>  \n" +
                "<TYPE>BCXX</TYPE>\n" +
                "<STYP>BCXX</STYP>\n" +
                "  </HEAD>  \n" +
                "  <BODY> \n" +
                "    <ID/>  \n" +
                "    <DEPARTTIME/>  \n" +
                "    <STATIONCODE/>  \n" +
                "    <STATIONNAME/>  \n" +
                "    <SCHEDULECODE/>  \n" +
                "    <ROUTENAME/>  \n" +
                "    <CHECKNO/>  \n" +
                "    <ISSTOP/>  \n" +
                "    <COMPANYNAME/>  \n" +
                "    <BUSNUMBER/>  \n" +
                "    <BUSTYPE/>  \n" +
                "    <SCHEDULETYPE/>  \n" +
                "    <TRANSPORTTYPE/>  \n" +
                "    <ENDSTOPNAME/>  \n" +
                "    <ENDSTOPCODE/>  \n" +
                "    <OPENCHECKOPERATOR/>  \n" +
                "    <OPENCHECKTIME/>  \n" +
                "    <CLOSECHECKOPERATOR/>  \n" +
                "    <CLOSECHECKTIME/>  \n" +
                "    <STARTSCHEDULEID/>  \n" +
                "    <STARTSEATNO/>  \n" +
                "    <ENDSEATNO/>  \n" +
                "    <SEATNUM/>  \n" +
                "    <ENDSTOPMILEAGE/>  \n" +
                "    <ARRIVELEVEL/>  \n" +
                "    <STOPREASON/>  \n" +
                "    <PARKNO/>  \n" +
                "    <REMARK/>  \n" +
                "    <SCHEDULEPRICELIST> \n" +
                "      <STOPCODE/>  \n" +
                "      <TICKETTYPE/>  \n" +
                "      <STOPNAME/>  \n" +
                "      <TICKETPRICE/>  \n" +
                "      <ISSTOP/>  \n" +
                "      <STOPMILEAGE/> \n" +
                "    </SCHEDULEPRICELIST>  \n" +
                "    <SCHEDULESEATLIST> \n" +
                "      <SEATNO/>  \n" +
                "      <SEATTYPE/> \n" +
                "    </SCHEDULESEATLIST>  \n" +
                "    <STARTSTATIONLIST> \n" +
                "      <STARTSTATIONCODE/>  \n" +
                "      <STARTSTATIONNAME/>  \n" +
                "      <DEPARTTIME/>  \n" +
                "      <ISSTOP/> \n" +
                "    </STARTSTATIONLIST>  \n" +
                "    <STARTSTATIONPRICELIST> \n" +
                "      <STARTSTATIONCODE/>  \n" +
                "      <STARTSTATIONNAME/>  \n" +
                "      <STOPCODE/>  \n" +
                "      <STOPNAME/>  \n" +
                "      <FULLPRICE/>  \n" +
                "      <HALFPRICE/>  \n" +
                "      <OTHERPRICE/> \n" +
                "    </STARTSTATIONPRICELIST>  \n" +
                "    <SCHEDULEDRIVERLIST> \n" +
                "      <TYPE/>  \n" +
                "      <NAME/>  \n" +
                "      <TIME/> \n" +
                "    </SCHEDULEDRIVERLIST> \n" +
                "  </BODY> \n" +
                "</MSG>";
        String s = xml2json(sb);
        System.out.println(s);

    }


}

