package com.air.security.sodb.dts.receive.job;


import org.apache.rocketmq.client.producer.LocalTransactionState;
import org.apache.rocketmq.client.producer.TransactionListener;
import org.apache.rocketmq.client.producer.TransactionMQProducer;
import org.apache.rocketmq.common.message.Message;
import org.apache.rocketmq.common.message.MessageExt;

/**
 * 发送同步消息
 */
public class Producer {

    public static void main(String[] args) throws Exception {
        //1.创建消息生产者producer，并制定生产者组名
        TransactionMQProducer producer = new TransactionMQProducer("group5");
        //2.指定Nameserver地址
        producer.setNamesrvAddr("192.168.112.191:9876");

        // 添加事务监听器
        producer.setTransactionListener(new TransactionListener() {
            @Override
            public LocalTransactionState executeLocalTransaction(Message msg, Object arg) {
                try {
                    // 执行本地事务（例如，数据库操作）
                    boolean isOrderCreated = createOrder();
                    boolean isPaymentConfirmed = confirmPayment();

                    if (isOrderCreated && isPaymentConfirmed) {
                        // 如果本地事务成功，提交消息
                        return LocalTransactionState.COMMIT_MESSAGE;
                    } else {
                        // 如果任何操作失败，回滚消息
                        return LocalTransactionState.ROLLBACK_MESSAGE;
                    }
                } catch (Exception e) {
                    e.printStackTrace();
                    // 如果发生异常，标记状态为未知，稍后进行回查
                    return LocalTransactionState.UNKNOW;
                }
            }

            @Override
            public LocalTransactionState checkLocalTransaction(MessageExt msg) {
                // 实现事务状态的回查逻辑
                // 这里简化为总是提交消息，实际应用中应根据本地事务状态决定
                return LocalTransactionState.COMMIT_MESSAGE;
            }
        });

        // 启动producer
        producer.start();

        // 创建并发送事务消息到TopicA
        Message msgA = new Message("TopicA", "OrderCreated", "Order details".getBytes());
        producer.sendMessageInTransaction(msgA, null);

        // 创建并发送事务消息到TopicB
        Message msgB = new Message("TopicB", "PaymentConfirmed", "Payment details".getBytes());
        producer.sendMessageInTransaction(msgB, null);

        // 注意：在实际应用中，消息的发送可能需要根据业务逻辑进行调整
        // 例如，根据executeLocalTransaction方法的执行结果来决定是否发送特定消息

        // 生产者不应立即关闭，因为需要等待事务状态的回查
        // producer.shutdown();
    }

    //6.关闭生产者producer
    //producer.shutdown();
    private static boolean createOrder() {
        // 模拟订单创建逻辑
        return false; // 假设订单创建成功
    }

    private static boolean confirmPayment() {
        // 模拟支付确认逻辑
        return true; // 假设支付确认成功
    }
}
