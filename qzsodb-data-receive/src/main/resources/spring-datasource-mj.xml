<?xml version="1.0" encoding="UTF-8"?>
<beans xmlns="http://www.springframework.org/schema/beans"
       xmlns:xsi="http://www.w3.org/2001/XMLSchema-instance"
       xmlns:context="http://www.springframework.org/schema/context"
       xmlns:mvc="http://www.springframework.org/schema/mvc"
       xmlns:aop="http://www.springframework.org/schema/aop"
       xmlns:tx="http://www.springframework.org/schema/tx"
       xsi:schemaLocation="http://www.springframework.org/schema/context
        http://www.springframework.org/schema/context/spring-context-4.0.xsd
        http://www.springframework.org/schema/beans
        http://www.springframework.org/schema/beans/spring-beans-4.0.xsd
        http://www.springframework.org/schema/mvc
        http://www.springframework.org/schema/mvc/spring-mvc.xsd
        http://www.springframework.org/schema/aop
        http://www.springframework.org/schema/aop/spring-aop-4.2.xsd
        http://www.springframework.org/schema/tx
        http://www.springframework.org/schema/tx/spring-tx.xsd">

    <bean id="mjDataSource" class="org.apache.tomcat.jdbc.pool.DataSource"
          destroy-method="close">
        <property name="driverClassName" value="${mj.datasource.driverClassName}"/>
        <property name="url" value="${mj.datasource.url}"/>
        <property name="username" value="${mj.datasource.username}"/>
        <property name="password" value="${mj.datasource.password}"/>
        <property name="initialSize" value="${mj.datasource.initialSize}"/>
        <property name="maxActive" value="${mj.datasource.maxActive}"/>
        <property name="maxIdle" value="${mj.datasource.maxIdle}"/>
        <property name="minIdle" value="${mj.datasource.minIdle}"/>
        <property name="maxWait" value="${mj.datasource.maxWait}"/>
        <property name="validationQuery" value="${mj.datasource.validationQuery}"/>
        <property name="testOnBorrow" value="${mj.datasource.testOnBorrow}"/>
        <property name="testOnReturn" value="${mj.datasource.testOnReturn}"/>
        <property name="timeBetweenEvictionRunsMillis"
                  value="${mj.datasource.timeBetweenEvictionRunsMillis}"/>
        <property name="minEvictableIdleTimeMillis"
                  value="${mj.datasource.minEvictableIdleTimeMillis}"/>
    </bean>

    <bean id="mjTransactionManager"
          class="org.springframework.jdbc.datasource.DataSourceTransactionManager">
        <property name="dataSource" ref="mjDataSource"/>
    </bean>

    <bean id="mjSqlSessionFactory" class="org.mybatis.spring.SqlSessionFactoryBean">
        <property name="dataSource" ref="mjDataSource"/>
        <property name="databaseIdProvider" ref="mjDatabaseIdProvider"/>
        <property name="mapperLocations" value="classpath*:mapper/**/*.xml"></property>
        <property name="configLocation" value="classpath:mybatis/mybatis-config.xml"></property>
        <property name="plugins">
            <array>
                <bean class="com.github.pagehelper.PageHelper">
                    <property name="properties">
                        <value>
                            dialect=sqlserver
                            <!--返回count -->
                            rowBoundsWithCount=true
                            <!--offset会当成pageNum使用，limit和pageSize含义相同 -->
                            offsetAsPageNum=true
                            <!--这时如果pageNum<1会查询第一页，如果pageNum>总页数会查询最后一页 -->
                            reasonable=true
                        </value>
                    </property>
                </bean>
            </array>
        </property>
        <property name="transactionFactory">
            <bean class="org.mybatis.spring.transaction.SpringManagedTransactionFactory"/>
        </property>
    </bean>

    <bean id="mjSqlSession" class="org.mybatis.spring.SqlSessionTemplate">
        <constructor-arg index="0" ref="mjSqlSessionFactory"/>
    </bean>

    <bean id="mjDatabaseIdProvider" class="org.apache.ibatis.mapping.VendorDatabaseIdProvider">
        <property name="properties" ref="mjVendorProperties"/>
    </bean>

    <bean id="mjVendorProperties"
          class="org.springframework.beans.factory.config.PropertiesFactoryBean">
        <property name="properties">
            <props>
                <prop key="Oracle">oracle</prop>
                <prop key="MySQL">mysql</prop>
                <prop key="SqlServer">sqlserver</prop>
            </props>
        </property>
    </bean>

    <bean class="tk.mybatis.spring.mapper.MapperScannerConfigurer">
        <property name="basePackage" value="com.air.security.sodb.dts.srvc.mj.mapper"/>
        <property name="sqlSessionFactoryBeanName" value="mjSqlSessionFactory"/>
        <property name="properties">
            <value>
                mappers=tk.mybatis.mapper.common.Mapper,tk.mybatis.mapper.common.ConditionMapper
            </value>
        </property>
    </bean>
</beans>