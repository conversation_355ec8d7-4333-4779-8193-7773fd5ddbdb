#\u6D88\u606F\u6807\u51C6\u5316\u5904\u7406Service\u914D\u7F6E
#\u95E8\u7981\u8BBE\u5907\u4FE1\u606F
SB_EQU_BASIC=MessageRecvFormatServiceImpl,cContainerEqu,SB_EQU_BASIC,EQU
#\u95E8\u7981\u62A5\u8B66\u4FE1\u606F
MJ_ALARM=MessageRecvFormatServiceImpl,cContainerAlarm,MJ_ALARM,ALARM
##\u95E8\u7981\u5237\u5361\u65E5\u5FD7
ACS_RECORD_CARD=MessageRecvFormatServiceImpl,cContainerRecord,ACS_RECORD_CARD,RECORD_CARD
#\u6D77\u5EB7\u89C6\u9891\u76EE\u5F55\u6811\u4FE1\u606F
#hkVideoRegion=MessageRecvFormatServiceImpl,cContainerEqu,VIDEO_REGION,EQU
#\u6D77\u5EB7\u89C6\u9891\u8D44\u6E90\u4FE1\u606F
#hkvideoEquInfo=EquInfoMessageRecvFormatServiceImpl,cContainerEqu,VIDEO_EQU,EQU
#\u6D77\u5EB7\u89C6\u9891\u8D44\u6E90\u53D8\u66F4\u4FE1\u606F
#hkvideoEquState=HkChannelStatusRecvServiceImpl,cContainerEqu,VIDEO_EQU_STATE,EQU_STATES
#\u6D77\u5EB7\u624B\u52A8\u62A5\u8B66\u8D44\u6E90\u4FE1\u606F
hkhandEquInfo=MessageRecvFormatServiceImpl,cContainerEqu,HAND_EQU,EQU
#\u6D77\u5EB7\u624B\u52A8\u62A5\u8B66\u8D44\u6E90\u53D8\u66F4\u4FE1\u606F
hkhandEquState=MessageRecvFormatServiceImpl,cContainerEqu,HAND_EQU_STATE,EQU_STATES
#\u6D77\u5EB7\u5165\u4FB5\u62A5\u8B66\u4E8B\u4EF6\uFF08\u624B\u52A8\u62A5\u8B66\u62A5\u8B66\uFF09
handAlarmMessage=MessageRecvFormatServiceImpl,cContainerAlarm,HAND_ALARM,ALARM
#\u8FD0\u5355\u57FA\u672C\u4FE1\u606F
WAY_BILL_LOG=CargoWayBillLogRecvFormatServiceImpl,cContainerCargo,WAY_BILL_LOG,CARGO
#\u5F00\u5305\u4FE1\u606F
OPEN_LOG=CargoOpenLogRecvFormatServiceImpl,cContainerCargo,OPEN_LOG,CARGO
#\u5B89\u68C0\u4FE1\u606F
SECURITY_LOG=CargoSecurityLogRecvFormatServiceImpl,cContainerCargo,SECURITY_LOG,CARGO
#\u65C5\u5BA2\u8FC7\u68C0\u8BB0\u5F55
PSGR_SECURITY=PsgrSecurityRecvFormatServiceImpl,cContainerPassenger,PSGR_SECURITY,PSGR_DATA
#\u65C5\u5BA2\u968F\u8EAB\u884C\u674E\u5F00\u5305\u4FE1\u606F
PSGR_BAG_OPEN=PsgrBagOpenRecvFormatServiceImpl,cContainerPassenger,PSGR_BAG_OPEN,PSGR_DATA
#\u56F4\u754C\u8D44\u6E90\u4FE1\u606F
WSBUE=MessageRecvFormatServiceImpl,cContainerEqu,WSBUE,EQU
#\u56F4\u754C\u72B6\u6001\u4FE1\u606F
WSSTUE=QzWsStueMessageRecvFormatServiceImpl,cContainerEqu,WSSTUE,EQU_STATES
#\u56F4\u754C\u5173\u8054\u8BBE\u5907\u4FE1\u606F
WSEQREL=QzWseqrelMessageRecvFormatServiceImpl,cContainerEqu,WJ_EQU_REL,EQU_REL
#\u56F4\u754C\u62A5\u8B66\u4FE1\u606F
WSALARM=QzWsAlarmMessageRecvFormatServiceImpl,cContainerAlarm,WJ_ALARM,ALARM
#\u822A\u73ED\u52A8\u6001\u4FE1\u606F
ACDM_DFUE=QzFightMessageRecvFormatServiceImpl,cContainerFlight,ACDMDFUE,FLIGHT_DATA

#\u9053\u53E3\u4EBA\u5458\u76D1\u63A7
crossPsgrMessage = CrossPsgrRecvFormatServiceImpl,cContainerCrossing,CROSSING_STAFF_PASS_INFO,CROSSING_DATA
STAFF_TRAFFIC=CrossRecvFormatServiceImpl,cContainerStaff,STAFF_TRAFFIC,STAFF