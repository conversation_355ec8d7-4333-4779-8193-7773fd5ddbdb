#è®¾ç½®ä¸ä¸ææ ¹ é»è®¤ â/â
#server.servlet.path=/sodb-ams
#è®¾ç½®è®¿é®ç«¯å£ é»è®¤â8080â
server.port=11013
#æä»¶ä¸ä¼ å¤§å°éç½®
spring.http.multipart.maxFileSize=100Mb
spring.http.multipart.maxRequestSize=100Mb
spring.activiti.check-process-definitions=false
spring.autoconfigure.exclude=org.springframework.boot.autoconfigure.jdbc.DataSourceAutoConfiguration,org.springframework.boot.autoconfigure.jdbc.DataSourceTransactionManagerAutoConfiguration
db.type=mysql
#æ°æ®åºè¿æ¥éç½®ï¼dts
datasource.driverClassName=com.mysql.jdbc.Driver
datasource.url=********************************************************
#datasource.url=********************************************
datasource.username=root
datasource.password=Qz123456.
datasource.initialSize=5
datasource.maxActive=300
datasource.maxIdle=10
datasource.minIdle=2
datasource.maxWait=60000
datasource.validationQuery=select VERSION()
datasource.testOnBorrow=true
datasource.testOnReturn=true
datasource.timeBetweenEvictionRunsMillis=60000
datasource.minEvictableIdleTimeMillis=180000
datasource.removeAbandoned=true
datasource.removeAbandonedTimeout=250
##############################################################################################
#æ°æ®åºè¿æ¥éç½®ï¼habs
##############################################################################################
bs.datasource.driverClassName=com.mysql.jdbc.Driver
bs.datasource.url=*********************************************************
bs.datasource.username=root
bs.datasource.password=Qz123456.
bs.datasource.initialSize=5
bs.datasource.maxActive=300
bs.datasource.maxIdle=1
bs.datasource.minIdle=2
bs.datasource.maxWait=60000
bs.datasource.validationQuery=select VERSION()
bs.datasource.testOnBorrow=true
bs.datasource.testOnReturn=true
bs.datasource.timeBetweenEvictionRunsMillis=60000
bs.datasource.minEvictableIdleTimeMillis=180000
bs.datasource.removeAbandoned=true
bs.datasource.removeAbandonedTimeout=250
##############################################################################################
#æ°æ®åºè¿æ¥éç½®ï¼é¨ç¦
##############################################################################################
mj.datasource.driverClassName=com.mysql.jdbc.Driver
mj.datasource.url=****************************************************************
mj.datasource.username=root
mj.datasource.password=Qz123456.
mj.datasource.initialSize=5
mj.datasource.maxActive=300
mj.datasource.maxIdle=1
mj.datasource.minIdle=2
mj.datasource.maxWait=60000
mj.datasource.validationQuery=select VERSION()
mj.datasource.testOnBorrow=true
mj.datasource.testOnReturn=true
mj.datasource.timeBetweenEvictionRunsMillis=60000
mj.datasource.minEvictableIdleTimeMillis=180000
mj.datasource.removeAbandoned=true
mj.datasource.removeAbandonedTimeout=250

##############################################################################################
#æ°æ®åºè¿æ¥éç½®ï¼å´ç
##############################################################################################
wj.datasource.driverClassName=com.mysql.jdbc.Driver
wj.datasource.url=**********************************************************************
wj.datasource.username=root
wj.datasource.password=Qz123456.
wj.datasource.initialSize=5
wj.datasource.maxActive=300
wj.datasource.maxIdle=1
wj.datasource.minIdle=2
wj.datasource.maxWait=60000
wj.datasource.validationQuery=select VERSION()
wj.datasource.testOnBorrow=true
wj.datasource.testOnReturn=true
wj.datasource.timeBetweenEvictionRunsMillis=60000
wj.datasource.minEvictableIdleTimeMillis=180000
wj.datasource.removeAbandoned=true
wj.datasource.removeAbandonedTimeout=250

#rediséç½®
redis.host=**********
#redis.host=**************
redis.port=6379
redis.pass=Qz123456.
redis.maxIdle=300
redis.maxTotal=200
redis.maxWaitMillis=2000
redis.testOnBorrow=true
redis.timeout=2000