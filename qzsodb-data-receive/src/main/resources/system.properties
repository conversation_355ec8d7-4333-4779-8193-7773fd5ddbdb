#\u7EBF\u7A0B\u6C60\u5927\u5C0F
threadpool.count=20
#tlq\u76D1\u542C
tlq.service=true
#kafkaConsumer kafka\u6D88\u8D39\u8005\u914D\u7F6E\u6587\u4EF6\u8DEF\u5F84
#kafka.consumer.config.path=D:\\config\\kafkaConsumer.properties
kafka.consumer.config.path=/data/server/dtsDataServer/qzDataRecv/config/kafkaConsumer.properties
#kafkaProducer kafka\u6D88\u8D39\u8005\u914D\u7F6E\u6587\u4EF6\u8DEF\u5F84
#kafka.producer.config.path=D:\\config\\kafkaProducer.properties
kafka.producer.config.path=/data/server/dtsDataServer/qzDataRecv/config/kafkaProducer.properties
#\u8BB0\u5F55\u6570\u636E\u4F20\u8F93\u65E5\u5FD7\u7684topic
kafka.msg.transfer.log.topic=msgTranLog
#\u5E94\u7528\u5173\u95ED\u76D1\u63A7\u7AEF\u53E3
qzsodb.stop.port=20028
#\u6D77\u5EB7\u670D\u52A1ip:port
rest.hk.host=*********:443
#\u6D77\u5EB7\u670D\u52A1appkey
rest.hk.appKey=25454562
#\u6D77\u5EB7\u670D\u52A1appsecret
rest.hk.appSecret=S7n8NL2Rkju3U5d5bbZm
##\u6D77\u5EB7\u89C6\u9891\u76EE\u5F55\u6811code
#hk.videoRegion.service.code=hkVideoRegion
##\u6D77\u5EB7\u83B7\u53D6\u89C6\u9891\u76EE\u5F55\u6811url
#hk.videoRegion.url=/api/resource/v1/regions
##\u6D77\u5EB7\u89C6\u9891\u8D44\u6E90\u4FE1\u606Fcode
#hk.videoEquInfo.service.code=hkvideoEquInfo
##\u6D77\u5EB7\u83B7\u53D6\u89C6\u9891\u8D44\u6E90\u4FE1\u606Furl
#hk.videoEquInfo.url=/api/resource/v1/cameras
##\u6D77\u5EB7\u89C6\u9891\u8D44\u6E90\u53D8\u66F4\u4FE1\u606Fcode
#hk.videoEquState.service.code=hkvideoEquState
##\u6D77\u5EB7\u83B7\u53D6\u89C6\u9891\u8D44\u6E90\u53D8\u66F4\u4FE1\u606Furl
#hk.videoEquState.url=/api/nms/v1/online/camera/get
#\u6D77\u5EB7\u624B\u52A8\u62A5\u8B66\u8D44\u6E90\u4FE1\u606Fcode
hk.handEquInfo.service.code=hkhandEquInfo
#\u6D77\u5EB7\u83B7\u53D6\u624B\u52A8\u62A5\u8B66\u8D44\u6E90\u4FE1\u606Furl
hk.handEquInfo.url=/api/irds/v2/deviceResource/resources
#\u6D77\u5EB7\u624B\u52A8\u62A5\u8B66\u8D44\u6E90\u53D8\u66F4\u4FE1\u606Fcode
hk.handEquState.service.code=hkhandEquState
#\u6D77\u5EB7\u83B7\u53D6\u624B\u52A8\u62A5\u8B66\u8D44\u6E90\u53D8\u66F4\u4FE1\u606Furl
hk.handEquState.url=/api/scpms/v1/defence/status
#\u6D77\u5EB7\u83B7\u53D6\u624B\u52A8\u62A5\u8B66\u8D44\u6E90\u53D8\u66F4\u4FE1\u606Furl
hk.handEquIoInState.url=/api/pems/v1/defense/ioIn/state
#\u6D77\u5EB7\u83B7\u53D6\u6309\u4E8B\u4EF6\u7C7B\u578B\u8BA2\u9605\u4E8B\u4EF6\u4FE1\u606Furl
hk.eventSubscription.url=/api/eventService/v1/eventSubscriptionByEventTypes
#\u6D77\u5EB7\u62A5\u8B66\u8F93\u5165\u4E8B\u4EF6\uFF08\u89C6\u9891\u62A5\u8B66\uFF09code
hk.videoAlarmMessage.service.code=videoAlarmMessage
#\u6D77\u5EB7\u5165\u4FB5\u62A5\u8B66\u4E8B\u4EF6\uFF08\u624B\u52A8\u62A5\u8B66\u62A5\u8B66\uFF09code
hk.handAlarmMessage.service.code=handAlarmMessage
#sodb\u624B\u52A8\u62A5\u8B66\u8D44\u6E90\u72B6\u6001\u8BF7\u6C42\u63A5\u53E3
sodb.ec03.equ.status.req.url=http://**********/api/bs/equ/info/open/getEquInfoByCode/EC03
##sodb\u89C6\u9891\u8D44\u6E90\u72B6\u6001\u8BF7\u6C42\u63A5\u53E3
#sodb.video.equ.status.req.url=http://**********/api/bs/equ/info/open/getEquInfoByCode/EC01
##\u6D77\u5EB7\u9053\u53E3\u4EBA\u5458\u8FDB\u51FA\u4FE1\u606Fcode
#hk.crossPsgr.service.code=crossPsgrMessage
#//\u83B7\u53D6\u95E8\u7981\u4E8B\u4EF6\u7684\u56FE\u7247url
#hk.cameraPicUrl.url=/api/acs/v1/event/pictures
#//\u4EBA\u5458\u56FE\u7247url
#hk.certificatesPicUrl.url=/api/resource/v1/person/picture
#//\u6839\u636E\u4EBA\u5458\u552F\u4E00\u5B57\u6BB5\u83B7\u53D6\u4EBA\u5458\u8BE6\u7EC6\u4FE1\u606F
#hk.personinfoUrl.url=/api/resource/v1/person/condition/personInfo
#//\u6839\u636E\u7F16\u53F7\u67E5\u8BE2\u8D44\u6E90\u8BE6\u7EC6\u4FE1\u606F
#hk.door.url=/api/resource/v1/resource/indexCodes/search
//\u673A\u573A\u4E09\u5B57\u5417
airport.iata=JJN
##minio\u53C2\u6570
#endpoint=http://10.68.1.53:9001
#access_key=miniominio
#secret_key=hayc@123
#bucket=datarecv
cross_key=cross2956dceacab44ecc82373d1ed5f923bd
#gis\u5750\u6807\u540C\u6B65url
gis.request.url=http://192.168.156.75:8080/GISW1GisService/incrementRest/gisdata.do?systemCode=GIS&dataTheme=hayc&outsr=berghaus
gis.layer.config.path=/data/server/dtsDataServer/qzDataRecv/config/gisLayer.json
EC01_CONF_PATH=/data/server/dtsDataServer/qzDataRecv/config/EC01_CONF_PATH.json
EC02_CONF_PATH=/data/server/dtsDataServer/qzDataRecv/config/EC02_CONF_PATH.json
#\u6D77\u5EB7\u5E73\u53F0\uFF1Adzgs Dzgs1234@



