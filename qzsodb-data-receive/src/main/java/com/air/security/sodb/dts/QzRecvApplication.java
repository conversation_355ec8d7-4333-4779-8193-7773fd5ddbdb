package com.air.security.sodb.dts;

import com.air.security.mq.client.service.ConsumerService;
import com.air.security.mq.client.vo.ConsumerResultVO;
import com.air.security.sodb.data.core.constant.MsgIdConstant;
import com.air.security.sodb.data.core.quartz.SchedulerManager;
import com.air.security.sodb.data.core.util.HaLog;
import com.air.security.sodb.data.core.util.PropertyUtil;
import com.air.security.sodb.data.core.util.ThreadUtil;
import com.air.security.sodb.dts.receive.listener.TlqListener;
import org.slf4j.Logger;
import org.slf4j.LoggerFactory;
import org.springframework.boot.ApplicationArguments;
import org.springframework.boot.ApplicationRunner;
import org.springframework.boot.SpringApplication;
import org.springframework.boot.autoconfigure.SpringBootApplication;
import org.springframework.context.annotation.ImportResource;

import java.io.*;
import java.net.ServerSocket;
import java.net.Socket;

/**
 * @Description 泉州数据接入服务
 * <AUTHOR>
@ImportResource("classpath:/applicationContext.xml")
@SpringBootApplication(scanBasePackages = "com.air.security.sodb")
public class QzRecvApplication implements ApplicationRunner {

    private static final Logger log = LoggerFactory.getLogger(QzRecvApplication.class);

    private static boolean tlqService = Boolean.parseBoolean(PropertyUtil.getProperty("tlq.service"));

    private static ServerSocket server;

    private static final String BUCKET = PropertyUtil.getProperty("bucket");

    public static void main(String[] args) {
        SpringApplication.run(QzRecvApplication.class, args);
    }

    /**
     * 启动服务
     * @param args
     * @throws Exception
     */
    @Override
    public void run(ApplicationArguments args) throws Exception {
        // 启动定时任务
        runQuartzJob();
        //MinioUtil.makeBucket(BUCKET);
        if (tlqService) {
            TlqListener listener = new TlqListener();
            ConsumerResultVO consumerResultVO= ConsumerService.consumer(listener);
            log.info(consumerResultVO.toString());
        }
        stopJob(Integer.valueOf(PropertyUtil.getProperty("qzsodb.stop.port")), "qzsodb");

    }

    private static void stop() {
        HaLog.info(log, MsgIdConstant.MS_INF_0001, "job停止处理");
        Socket command = null;
        BufferedWriter writer = null;
        try {

            // 创建Socket
            command = new Socket("127.0.0.1", Integer.valueOf(PropertyUtil.getProperty("qzsodb.stop.port")));
            writer = new BufferedWriter(new OutputStreamWriter(command.getOutputStream()));

            // 发送STOP命令
            writer.write("STOP");
            writer.newLine();
            writer.flush();
        } catch (Exception e) {
            HaLog.error(log, e, MsgIdConstant.MS_ERR_0001);
        } finally {
            if (writer != null) {
                try {
                    writer.close();
                } catch (IOException e) {
                    HaLog.error(log, e, MsgIdConstant.MS_ERR_0001);
                }
            }
            if (command != null) {
                try {
                    command.close();
                } catch (IOException e) {
                    HaLog.error(log, e, MsgIdConstant.MS_ERR_0001);
                }
            }
        }

        HaLog.info(log, MsgIdConstant.MS_INF_0002, "job停止处理");
        System.exit(0);
    }

    /**
     * 监听停止服务命令
     *
     * @param port        端口号
     * @param projectName 工程名
     */
    private static void stopJob(int port, String projectName) {
        Socket commandSocket = null;
        BufferedReader reader = null;

        try {
            server = new ServerSocket(port);
            commandSocket = server.accept();
            reader = new BufferedReader(new InputStreamReader(commandSocket.getInputStream()));
        } catch (IOException e) {
            HaLog.info(log, MsgIdConstant.MS_ERR_0001, e);
            System.exit(0);
        }

        while (true) {
            try {
                String commandStr = reader.readLine();
                if ("STOP".equals(commandStr)) {

                    // 关闭数据流
                    reader.close();
                    commandSocket.close();
                    server.close();

                    ThreadUtil.shutdown();

                    HaLog.info(log, MsgIdConstant.MS_INF_0002, projectName);
                    System.exit(0);
                }

                Thread.sleep(1000);
            } catch (IOException e) {
                HaLog.error(log, e, MsgIdConstant.MS_ERR_0001);
            } catch (InterruptedException e) {
                HaLog.error(log, e, MsgIdConstant.MS_ERR_0001);
            }
        }
    }

    /**
     * 启动定时任务
     */
    private static void runQuartzJob() {
        HaLog.info(log, MsgIdConstant.MS_INF_0001, "quartz定时任务");
        SchedulerManager manager = new SchedulerManager();
        manager.runJob("quartzJob.xml");
        HaLog.info(log, MsgIdConstant.MS_INF_0002, "quartz定时任务");
    }

    /**
     * 停止定时任务
     */
    private static void shutQuartzJob() {
        HaLog.info(log, MsgIdConstant.MS_INF_0001, "quartz定时任务");
        SchedulerManager manager = new SchedulerManager();
        manager.shutJob();
        HaLog.info(log, MsgIdConstant.MS_INF_0002, "quartz定时任务");
    }
}
