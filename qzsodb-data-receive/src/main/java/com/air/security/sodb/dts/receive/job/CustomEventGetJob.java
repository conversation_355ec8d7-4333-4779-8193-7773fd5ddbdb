package com.air.security.sodb.dts.receive.job;

import com.air.security.sodb.data.core.base.Meta;
import com.air.security.sodb.data.core.constant.MsgIdConstant;
import com.air.security.sodb.data.core.quartz.AbstractJob;
import com.air.security.sodb.data.core.spring.ApplicationContextUtil;
import com.air.security.sodb.data.core.util.DateTimeUtil;
import com.air.security.sodb.data.core.util.HaLog;
import com.air.security.sodb.dts.receive.server.MqMessageRecvServer;
import com.air.security.sodb.dts.receive.server.MqMessageRecvServerImpl;
import com.air.security.sodb.dts.srvc.mj.entity.CustomEventViewEntity;
import com.air.security.sodb.dts.srvc.mj.service.CustomEventViewService;
import com.alibaba.fastjson.JSONObject;
import org.quartz.JobDataMap;
import org.slf4j.Logger;
import org.slf4j.LoggerFactory;

import java.text.SimpleDateFormat;
import java.util.Calendar;
import java.util.Date;
import java.util.List;

/**
 * @Description:刷卡日志-定时任务Job
 * @Author: Cs
 * @Date: 2020/8/12
 **/
public class CustomEventGetJob extends AbstractJob {
    private static final Logger log = LoggerFactory.getLogger(CustomEventGetJob.class);

    /**
     * 消息接收处理器
     */
    private static MqMessageRecvServer server = new MqMessageRecvServerImpl();

    private static Date startTime = null;

    private static final int INTERVAL = -10;

    /**
     * 定时任务执行方法
     *
     * @param paramMap
     */
    @Override
    public void executeJob(JobDataMap paramMap) {
        HaLog.info(log, MsgIdConstant.MS_INF_0001, "获取刷卡日志-定时job");
        CustomEventViewService customEventViewService = (CustomEventViewService) ApplicationContextUtil
                .getBean("CustomEventViewServiceImpl");

        //格式转换
        SimpleDateFormat sdf = new SimpleDateFormat("yyyy-MM-dd HH:mm:ss");
        List<CustomEventViewEntity> list;
        String format;
        if (startTime != null) {
            format = sdf.format(startTime);
        } else {

            //设置当前时间前五分钟
            Date nextDate = DateTimeUtil.getNextDate(Calendar.MINUTE, INTERVAL, new Date());
            format = DateTimeUtil.getDateString(nextDate, "yyyy-MM-dd HH:mm:ss");
        }
		Date endTime = new Date();

		list = customEventViewService.execute(format,DateTimeUtil.getDateString(endTime, "yyyy-MM-dd HH:mm:ss"));

        //赋值给开始时间
        startTime = endTime;
        Meta meta = new Meta();

        //设置消息类型
        meta.setEventType("ACS_RECORD_CARD");
        for (CustomEventViewEntity customEventViewEntity : list) {

            //消息分发处理
            server.handle(meta, JSONObject.toJSONString(customEventViewEntity));
        }
        HaLog.info(log, MsgIdConstant.MS_INF_0002, "获取刷卡日志-定时job");
    }
}
