package com.air.security.sodb.dts.srvc.mj.service;

import com.air.security.sodb.data.core.exception.SystemBaseException;
import com.air.security.sodb.dts.srvc.mj.entity.CustomAlarmViewEntity;

import java.util.List;

/**
 * @Description:门禁报警信息查询
 * <AUTHOR>
public interface CustomAlarmViewService {

    /**
     * 执行业务处理
     * @param strTime
     * @param endTime
     * @return
     * @throws SystemBaseException
     */
    List<CustomAlarmViewEntity> execute(String strTime, String endTime) throws SystemBaseException;
}
