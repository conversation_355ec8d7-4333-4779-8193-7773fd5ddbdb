/*
package com.air.security.sodb.dts.receive.listener;

import com.air.security.sodb.data.core.util.Md5Util;
import com.air.security.sodb.data.core.util.PropertyUtil;
import com.airport.dwh.tlq.api.TlqGetMsgApi;
import com.airport.dwh.tlq.client.TlqClientFactory;
*/
/**
 * tlq工具类
 * <AUTHOR>

public class TlqGetClientInitialize {

    */
/**
     * TlqGetClientInitialize实例
     *//*

    private static TlqGetClientInitialize instance;

    */
/**
     * 配置文件绝对路径
     *//*

    private static String tlqSdkConfig = PropertyUtil.getProperty("tlq.sdk.config.path");

    */
/**
     * 航班信息Tlq用户名
     *//*

    private static String username = PropertyUtil.getProperty("tlq.sdk.username");

    */
/**
     * 航班信息Tlq密码
     *//*

    private static String password = PropertyUtil.getProperty("tlq.sdk.password");

    */
/**
     * TlqSendMsgClient实例
     *//*

    private static TlqGetMsgApi getClient;

    */
/**
     * 私有化构造方法
     *//*

    private TlqGetClientInitialize() {

    }

    */
/**
     * 获取TlqGetClientInitialize实例
     *
     * @param clientQue 对列名
     * @return
     *//*

    public static TlqGetClientInitialize getInstance() {
        init();
        if (null == instance) {
            instance = new TlqGetClientInitialize();
        }

        return instance;
    }

    */
/**
     * TlqSendClient初始化
     *//*

    private static void init() {
        if (null == getClient) {

            // 创建客户端
            getClient = TlqClientFactory.createGetMsgClient();

            // 初始化客户端
            getClient.initClient(tlqSdkConfig);

            // 登录
            getClient.login(username, Md5Util.string2Md5(password));

            // 建立连接
            getClient.openConnection();
        }
    }


    */
/**
     * 获取TlqGetMsgApi实例
     *
     * @return TlqGetMsgApi
     *//*

    public TlqGetMsgApi getGetClient() {
        return getClient;
    }

    public static void main(String[] args) {
        getInstance();
    }

}
*/
