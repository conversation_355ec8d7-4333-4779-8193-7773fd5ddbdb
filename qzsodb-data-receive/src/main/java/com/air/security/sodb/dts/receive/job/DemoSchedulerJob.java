package com.air.security.sodb.dts.receive.job;

import org.quartz.JobDataMap;
import org.slf4j.Logger;
import org.slf4j.LoggerFactory;

import com.air.security.sodb.data.core.constant.MsgIdConstant;
import com.air.security.sodb.data.core.exception.SystemBaseException;
import com.air.security.sodb.data.core.quartz.AbstractJob;
import com.air.security.sodb.data.core.util.HaLog;

/**
 * 大华门禁设备信息获取任务
 *
 * <AUTHOR>
 */
public class DemoSchedulerJob extends AbstractJob {

    private static final Logger log = LoggerFactory.getLogger(DemoSchedulerJob.class);

    @Override
    public void executeJob(JobDataMap paramMap) {
        HaLog.info(log, MsgIdConstant.MS_INF_0001, "获取大华门禁设备信息定时任务");

        try {
            System.out.println("测试定时任务...");
        } catch (Exception e) {
            throw new SystemBaseException("获取大华门禁设备信息失败", e);
        } finally {
            HaLog.info(log, MsgIdConstant.MS_INF_0002, "获取大华门禁设备信息定时任务");
        }

    }

}
