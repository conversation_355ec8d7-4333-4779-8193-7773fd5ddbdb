package com.air.security.sodb.dts.receive.service;

import com.air.security.sodb.data.core.base.Meta;
import com.air.security.sodb.data.core.exception.SystemBaseException;
import com.alibaba.fastjson.JSONObject;

/**
 * <AUTHOR>
 */
public abstract class AbstractSimpleMessageRecvService extends AbstractMessageRecvService {

    /**
     * 消息处理
     * @param meta
     * @param messageBody
     * @return
     * @throws SystemBaseException
     */
    public abstract Object executeGetData(Meta meta, String messageBody) throws SystemBaseException;

    /**
     * 消息处理
     *
     * @param meta
     * @param messageBody
     * @throws SystemBaseException
     */
    @Override
    public void execute(Meta meta, String messageBody) throws SystemBaseException {
        // TODO Auto-generated method stub

        try {
            Object body = this.executeGetData(meta, messageBody);
            // 响应json消息
            JSONObject outputMsg = new JSONObject();

            // 响应消息头
            outputMsg.put("meta", meta);

            // 响应消息体
            outputMsg.put("body", body);
            // 转换为JSON格式，并发送消息到指定主题
            super.putSendMessage(outputMsg);
        } catch (Exception e) {
            if (e instanceof SystemBaseException) {
                throw e;
            }
            throw new SystemBaseException("处理数据异常", e);
        }

    }

}
