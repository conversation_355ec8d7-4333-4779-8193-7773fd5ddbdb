package com.air.security.sodb.dts.srvc.mj.service.impl;


import com.air.security.sodb.data.core.constant.MsgIdConstant;
import com.air.security.sodb.data.core.exception.SystemBaseException;
import com.air.security.sodb.data.core.util.HaLog;
import com.air.security.sodb.dts.srvc.mj.entity.DoorsViewEntity;
import com.air.security.sodb.dts.srvc.mj.mapper.DoorsViewMapper;
import com.air.security.sodb.dts.srvc.mj.service.DoorsViewGetService;
import org.slf4j.Logger;
import org.slf4j.LoggerFactory;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.stereotype.Service;

import java.util.ArrayList;
import java.util.List;

/**
 * @Description:查询资源设备Service
 * <AUTHOR>
@Service("DoorsViewGetServiceImpl")
public class DoorsViewGetServiceImpl implements DoorsViewGetService {

    private static final Logger log = LoggerFactory.getLogger(DoorsViewGetServiceImpl.class);

    @Autowired
    private DoorsViewMapper doorsViewMapper;

    @Override
    public List<DoorsViewEntity> execute() throws SystemBaseException {

        List<DoorsViewEntity> list = doorsViewMapper.selectAll();
        if (list == null) {
            list = new ArrayList<>();
            HaLog.info(log, MsgIdConstant.MS_ERR_0001, "查询资源设备异常");
        }
        return list;
    }
}
