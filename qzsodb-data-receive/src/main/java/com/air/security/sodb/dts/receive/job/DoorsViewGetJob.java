package com.air.security.sodb.dts.receive.job;

import com.air.security.sodb.data.core.base.Meta;
import com.air.security.sodb.data.core.constant.MsgIdConstant;
import com.air.security.sodb.data.core.quartz.AbstractJob;
import com.air.security.sodb.data.core.spring.ApplicationContextUtil;
import com.air.security.sodb.data.core.util.HaLog;
import com.air.security.sodb.dts.receive.server.MqMessageRecvServer;
import com.air.security.sodb.dts.receive.server.MqMessageRecvServerImpl;
import com.air.security.sodb.dts.srvc.mj.entity.DoorsViewEntity;
import com.air.security.sodb.dts.srvc.mj.service.DoorsViewGetService;
import com.alibaba.fastjson.JSONObject;
import org.quartz.JobDataMap;
import org.slf4j.Logger;
import org.slf4j.LoggerFactory;

import java.util.List;


/**
 * @Description: 门禁设备点-定时job
 * @Author: Cs
 * @Date: 2020/8/10
 **/
public class DoorsViewGetJob extends AbstractJob {
    private static final Logger log = LoggerFactory.getLogger(DoorsViewGetJob.class);

    /**
     * 消息接收处理器
     */
    private static MqMessageRecvServer server = new MqMessageRecvServerImpl();

    /**
     * 定时任务执行方法
     *
     * @param paramMap
     */
    @Override
    public void executeJob(JobDataMap paramMap) {
        HaLog.info(log, MsgIdConstant.MS_INF_0001, "获取门禁设备点-定时job");
        DoorsViewGetService doorsViewGetService = (DoorsViewGetService) ApplicationContextUtil
                .getBean("DoorsViewGetServiceImpl");

        List<DoorsViewEntity> list = doorsViewGetService.execute();
        Meta meta = new Meta();

        //设置消息类型
        meta.setEventType("SB_EQU_BASIC");
        for (DoorsViewEntity doorsViewEntity : list) {

            //消息分发处理
            server.handle(meta, JSONObject.toJSONString(doorsViewEntity));
        }
        HaLog.info(log, MsgIdConstant.MS_INF_0002, "获取门禁设备点-定时job");
    }

}
