package com.air.security.sodb.dts.srvc.mj.entity;

import javax.persistence.Column;
import javax.persistence.Table;
import java.util.Date;

/**
 * @Description:门禁报警信息
 * @Author: Cs
 * @Date: 2020/8/12
 **/
@Table(name = "Alarms")
public class CustomAlarmViewEntity {

    /**
     * 报警时间
     */
    @Column(name = "AlarmTime")
    private Date alarmTime;

    /**
     * 资源编号
     */
    @Column(name = "PointAddr")
    private String pointAddr;

    /**
     * 事件类型
     */
    @Column(name = "EventTypeNdx")
    private String eventTypeNdx;

    /**
     * 报警类型
     */
    @Column(name = "AlarmType")
    private String alarmType;

    public Date getAlarmTime() {
        return alarmTime;
    }

    public void setAlarmTime(Date alarmTime) {
        this.alarmTime = alarmTime;
    }

    public String getPointAddr() {
        return pointAddr;
    }

    public void setPointAddr(String pointAddr) {
        this.pointAddr = pointAddr;
    }

    public String getEventTypeNdx() {
        return eventTypeNdx;
    }

    public void setEventTypeNdx(String eventTypeNdx) {
        this.eventTypeNdx = eventTypeNdx;
    }

    public String getAlarmType() {
        return alarmType;
    }

    public void setAlarmType(String alarmType) {
        this.alarmType = alarmType;
    }
}
