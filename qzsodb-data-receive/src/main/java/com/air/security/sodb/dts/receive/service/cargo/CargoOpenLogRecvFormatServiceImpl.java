package com.air.security.sodb.dts.receive.service.cargo;

import com.air.security.sodb.data.core.base.Meta;
import com.air.security.sodb.data.core.constant.MsgIdConstant;
import com.air.security.sodb.data.core.exception.SystemBaseException;
import com.air.security.sodb.data.core.util.HaLog;
import com.air.security.sodb.dts.receive.service.AbstractMessageRecvService;
import com.alibaba.fastjson.JSONArray;
import com.alibaba.fastjson.JSONObject;
import org.apache.commons.lang3.StringUtils;
import org.json.XML;
import org.slf4j.Logger;
import org.slf4j.LoggerFactory;
import org.springframework.stereotype.Service;

/**
 * @Description: 开包信息
 * @Author: mk
 * @Date: 2020/8/19
 **/
@Service("CargoOpenLogRecvFormatServiceImpl")
public class CargoOpenLogRecvFormatServiceImpl extends AbstractMessageRecvService {
    private static final Logger log = LoggerFactory.getLogger(CargoOpenLogRecvFormatServiceImpl.class);

    /**
     * 业务处理
     *
     * @param meta        消息头
     * @param messageBody 消息体
     * @throws SystemBaseException
     */
    @Override
    public void execute(Meta meta, String messageBody) throws SystemBaseException {
        HaLog.info(log, MsgIdConstant.MS_INF_0001, meta.getEventType());

        //xml转json
        org.json.JSONObject jsonObj = XML.toJSONObject(messageBody);
        JSONObject inputRootJson = JSONObject.parseObject(jsonObj.toString());

        JSONObject billbaseinfo = inputRootJson.getJSONObject("MSG").getJSONObject("OPENPACKAGEINFO");
        JSONArray array = new JSONArray();
        String ximages = billbaseinfo.getString("XIMAGES");
        if (StringUtils.isNotBlank(ximages)) {
            getMagesSplit(ximages, array, "0");
        }
        String rximages = billbaseinfo.getString("RXIMAGES");
        if (StringUtils.isNotBlank(rximages)) {
            getMagesSplit(rximages, array, "1");
        }

        billbaseinfo.put("imageInfo", array);

        // 获取到的json业务数据
        JSONObject jsonObject = new JSONObject();

        //响应消息头
        jsonObject.put("meta", meta);

        //响应消息体
        jsonObject.put("body", billbaseinfo);

        // 转换为JSON格式，并发送消息到指定主题
        super.putSendMessage(jsonObject);

    }

    /**
     * 存储图片
     *
     * @param rximages 图片url
     * @param array    数组
     */
    private void getMagesSplit(String rximages, JSONArray array, String flag) {
        String[] split = rximages.split(",");
        for (String ximage : split) {
            JSONObject json = new JSONObject();
            json.put("imageUrl", ximage);
            json.put("abnormalFlag", flag);
            array.add(json);
        }
    }
}
