package com.air.security.sodb.dts.receive.service.alarm;

import com.air.security.sodb.data.core.base.Meta;
import com.air.security.sodb.data.core.constant.GlobalCodeConstant;
import com.air.security.sodb.data.core.constant.MsgIdConstant;
import com.air.security.sodb.data.core.exception.SystemBaseException;
import com.air.security.sodb.data.core.util.DateTimeUtil;
import com.air.security.sodb.data.core.util.HaLog;
import com.air.security.sodb.dts.receive.service.AbstractMessageRecvService;
import com.air.security.sodb.dts.receive.util.HkVideoEquStatus;
import com.air.security.sodb.dts.receive.util.HkVideoEquStatusCache;
import com.alibaba.fastjson.JSONArray;
import com.alibaba.fastjson.JSONObject;
import org.apache.commons.lang3.StringUtils;
import org.slf4j.Logger;
import org.slf4j.LoggerFactory;
import org.springframework.stereotype.Service;

import java.util.List;
import java.util.Map;

/**
 * 视频资源变更信息
 *
 * <AUTHOR>
 */
@Service("HkChannelStatusRecvServiceImpl")
public class HkChannelStatusRecvServiceImpl extends AbstractMessageRecvService {

	private static final Logger log = LoggerFactory.getLogger(HkChannelStatusRecvServiceImpl.class);

	@Override
	public void execute(Meta meta, String messageBody) throws SystemBaseException {

		HaLog.info(log, MsgIdConstant.MS_INF_0001, meta.getEventType());

		JSONArray inputBody = new JSONArray();

		// 获取到的json业务数据
		JSONArray array = JSONArray.parseArray(messageBody);
		Map<String, String> map = HkVideoEquStatusCache.getCacheList();
		for (Object object : array) {
			JSONObject jsonObj = (JSONObject) object;
			String channelCode = jsonObj.getString("indexCode");
			String resStatus = jsonObj.getString("online");
			//获取摄像机的状态
			String timeStateId = map.get(channelCode);
			if (timeStateId == null) {
				continue;
			}
			String timeState = formatStatus(resStatus);
			if (timeState.equals(timeStateId)){
				continue;
			}
			HkVideoEquStatusCache.getCacheList().remove(channelCode);
			HkVideoEquStatusCache.getCacheList().put(channelCode,resStatus);
			JSONObject json = new JSONObject();
			json.put("indexCode",channelCode);
			json.put("timeStateId", timeState);
			json.put("operateTime", DateTimeUtil.getCurrentTimestampStr("yyyy-MM-dd HH:mm:ss"));
			inputBody.add(json);
		}

		HaLog.info(log, MsgIdConstant.MS_INF_0001, "1111>>>>>>"+inputBody.toString() +"   size"+inputBody.size());

		// 响应json消息
		JSONObject outputMsg = new JSONObject();

		// 响应消息头
		outputMsg.put("meta", meta);

		// 响应消息体
		outputMsg.put("body", inputBody);

		super.putSendMessage(outputMsg);

		HaLog.info(log, MsgIdConstant.MS_INF_0002, meta.getEventType());
	}
	private String formatStatus(String onlineStatus) {
		if (GlobalCodeConstant.ONE.equalsIgnoreCase(onlineStatus)) {
			onlineStatus = "ES01";
		}else {
			onlineStatus = "ES02";
		}
		return onlineStatus;
	}

}
