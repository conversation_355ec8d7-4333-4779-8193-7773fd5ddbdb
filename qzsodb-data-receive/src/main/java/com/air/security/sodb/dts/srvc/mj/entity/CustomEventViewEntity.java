package com.air.security.sodb.dts.srvc.mj.entity;


import javax.persistence.Column;
import javax.persistence.Id;
import javax.persistence.Table;
import java.util.Date;

/**
 * <AUTHOR> @Description:刷卡日志表
 */
@Table(name = "t_event_record")
public class CustomEventViewEntity {

    /**
     * 刷卡时间
     */
    @Column(name = "event_time")
    private Date recordTime;

    /**
     * 资源编号
     */
    @Column(name = "logic_device_id")
    private String deviceCode;

    /**
     * 卡号
     */
    @Column(name = "card_no")
    private String cardNo;

    /**
     * 部门
     */
    @Column(name = "last_name")
    private String lastName;

    /**
     * 卡状态
     */
    @Column(name = "status_code")
    private String statusCode;

    /**
     * 姓名
     */
    @Column(name = "first_name")
    private String firstName;

    /**
     * 资源名称
     */
    @Column(name = "logic_device_desc")
    private String deviceName;

    /**
     * 事件ID
     */
    @Id
    @Column(name = "event_id")
    private String eventId;

    /**
     * 事件类型ID
     */
    @Column(name = "event_type_id")
    private Integer eventTypeId;

    public Date getRecordTime() {
        return recordTime;
    }

    public void setRecordTime(Date recordTime) {
        this.recordTime = recordTime;
    }

    public String getDeviceCode() {
        return deviceCode;
    }

    public void setDeviceCode(String deviceCode) {
        this.deviceCode = deviceCode;
    }

    public String getCardNo() {
        return cardNo;
    }

    public void setCardNo(String cardNo) {
        this.cardNo = cardNo;
    }

    public String getLastName() {
        return lastName;
    }

    public void setLastName(String lastName) {
        this.lastName = lastName;
    }

    public String getStatusCode() {
        return statusCode;
    }

    public void setStatusCode(String statusCode) {
        this.statusCode = statusCode;
    }

    public String getFirstName() {
        return firstName;
    }

    public void setFirstName(String firstName) {
        this.firstName = firstName;
    }

    public String getDeviceName() {
        return deviceName;
    }

    public void setDeviceName(String deviceName) {
        this.deviceName = deviceName;
    }

    public String getEventId() {
        return eventId;
    }

    public void setEventId(String eventId) {
        this.eventId = eventId;
    }

    public Integer getEventTypeId() {
        return eventTypeId;
    }

    public void setEventTypeId(Integer eventTypeId) {
        this.eventTypeId = eventTypeId;
    }
}
