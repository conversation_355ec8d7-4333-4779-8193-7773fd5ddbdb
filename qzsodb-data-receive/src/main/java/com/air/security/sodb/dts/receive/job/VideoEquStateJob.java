package com.air.security.sodb.dts.receive.job;

import com.air.security.sodb.data.core.base.Meta;
import com.air.security.sodb.data.core.constant.MsgIdConstant;
import com.air.security.sodb.data.core.exception.SystemBaseException;
import com.air.security.sodb.data.core.quartz.AbstractJob;
import com.air.security.sodb.data.core.util.HaLog;
import com.air.security.sodb.data.core.util.PropertyUtil;
import com.air.security.sodb.data.core.util.UuidUtil;
import com.air.security.sodb.dts.receive.server.MqMessageRecvServer;
import com.air.security.sodb.dts.receive.server.MqMessageRecvServerImpl;
import com.air.security.sodb.dts.receive.util.CertificationInfoUtil;
import com.air.security.sodb.dts.receive.util.PageInfo;
import com.air.security.sodb.dts.receive.util.SrvcHelper;
import com.alibaba.fastjson.JSONArray;
import com.alibaba.fastjson.JSONObject;
import org.quartz.JobDataMap;
import org.slf4j.Logger;
import org.slf4j.LoggerFactory;

import java.io.IOException;

/**
 * 视频资源变更信息获取任务
 *
 * <AUTHOR>
 */
public class VideoEquStateJob extends AbstractJob {

    private static final Logger log = LoggerFactory.getLogger(VideoEquStateJob.class);

    private static String videoEquState = PropertyUtil.getProperty("hk.videoEquState.service.code");

    private static String url = PropertyUtil.getProperty("hk.videoEquState.url");


    /**
     *
     */
    private MqMessageRecvServer server = new MqMessageRecvServerImpl();

	@Override
	public void executeJob(JobDataMap paramMap) {
		HaLog.info(log, MsgIdConstant.MS_INF_0001, "获取视频资源变更信息定时任务");
		JSONArray array = new JSONArray();
		try {
			execPost(array,url, SrvcHelper.PAGE_SIZE, 1);
			if (array!=null && array.size() > 0){
				Meta meta = new Meta();
				meta.setEventType(videoEquState);
				meta.setRecvSequence(UuidUtil.getUuid32());
				server.handle(meta, array.toString());
			}

		} catch (Exception e) {
			throw new SystemBaseException("获取视频资源变更信息失败", e);
		} finally {
			HaLog.info(log, MsgIdConstant.MS_INF_0002, "获取视频资源变更信息定时任务");
		}

	}

	/**
	 * @param url
	 * @param pageSize
	 * @param pageNo
	 * @throws IOException
	 */
	private void execPost(JSONArray array,String url, int pageSize, int pageNo) throws IOException {
		JSONObject jsonObject = SrvcHelper.getPageParam(pageNo, pageSize);

		String messageBody = CertificationInfoUtil.send(url, jsonObject.toJSONString());

		// 获取分页数据
		PageInfo page = SrvcHelper.getPageInfo(messageBody);
		if (page.getTotalRows() > 0) {
			JSONObject json = JSONObject.parseObject(messageBody);
			JSONArray jsonJsonArray = json.getJSONObject("data").getJSONArray("list");
			for (Object o : jsonJsonArray) {
				JSONObject jsonbject = (JSONObject)o;
				array.add(jsonbject);
			}
			if (page.hasNextPage()) {
				// 查询下一页
				this.execPost(array,url, pageSize, page.getCurrentPage() + 1);
			}
		}
	}

}
