#çº¿ç¨æ± å¤§å°
threadpool.count=20
rest.dh.pageSize=1000
#kafkaConsumer kafkaæ¶è´¹èéç½®æä»¶è·¯å¾
#kafka.consumer.config.path=D:\\config\\kafkaConsumer.properties
kafka.consumer.config.path=/server/dtsServer/xjjcDataRecv/config/kafkaConsumer.properties
#kafkaProducer kafkaæ¶è´¹èéç½®æä»¶è·¯å¾
#kafka.producer.config.path=D:\\config\\kafkaProducer.properties
kafka.producer.config.path=/server/dtsServer/xjjcDataRecv/config/kafkaProducer.properties
whmjsodb.stop.port=20032

############################################æµ·åº·å¹³å°å¯¹æ¥##################################################################
#ipå°å
rest.hk.host=***********:443

#å¯é¥
rest.hk.appKey=28717708

#å¯é¥
rest.hk.appSecret=KbR6deAQNiJ72boopjaj

picUrl = http://**************:6120

#è·åèµæºåè¡¨v2
hk.mjEquInfo.url=/api/irds/v2/deviceResource/resources

#é¨ç¦æ¥è­¦åæ´èµæºurl
hk.mjEquState.url=/api/acs/v1/door/states

#é¨ç¦è®¢éurl
hk.eventSubscription.url=/api/eventService/v1/eventSubscriptionByEventTypes

#é¨ç¦æ¥è­¦èµæº
sodb.ec02.equ.status.req.url=http://***********/api/bs/equ/info/open/getEquInfoByCode/EC02

hk.EventType =198657,198400,198914,198915,197121,197122,197633,197634,197635,200519




