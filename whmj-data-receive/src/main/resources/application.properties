#è®¾ç½®ä¸ä¸ææ ¹ é»è®¤ â/â
#server.servlet.path=/sodb-ams
#è®¾ç½®è®¿é®ç«¯å£ é»è®¤â8080â
server.port=11013
#æä»¶ä¸ä¼ å¤§å°éç½®
spring.http.multipart.maxFileSize=100Mb
spring.http.multipart.maxRequestSize=100Mb
spring.activiti.check-process-definitions=false
spring.autoconfigure.exclude=org.springframework.boot.autoconfigure.jdbc.DataSourceAutoConfiguration,org.springframework.boot.autoconfigure.jdbc.DataSourceTransactionManagerAutoConfiguration
db.type=mysql
#æ°æ®åºè¿æ¥éç½®ï¼dts
datasource.driverClassName=com.mysql.jdbc.Driver
datasource.url=**********************************************************
#datasource.url=********************************************
datasource.username=root
datasource.password=abcABC123!!!
datasource.initialSize=5
datasource.maxActive=300
datasource.maxIdle=10
datasource.minIdle=2
datasource.maxWait=60000
datasource.validationQuery=select VERSION()
datasource.testOnBorrow=true
datasource.testOnReturn=true
datasource.timeBetweenEvictionRunsMillis=60000
datasource.minEvictableIdleTimeMillis=180000
datasource.removeAbandoned=true
datasource.removeAbandonedTimeout=250

##############################################################################################
#æ°æ®åºè¿æ¥éç½®ï¼habs
##############################################################################################
habs.datasource.driverClassName=com.mysql.jdbc.Driver
habs.datasource.url=***********************************************************
habs.datasource.username=root
habs.datasource.password=abcABC123!!!
habs.datasource.initialSize=5
habs.datasource.maxActive=300
habs.datasource.maxIdle=10
habs.datasource.minIdle=2
habs.datasource.maxWait=60000
habs.datasource.validationQuery=select VERSION()
habs.datasource.testOnBorrow=true
habs.datasource.testOnReturn=true
habs.datasource.timeBetweenEvictionRunsMillis=60000
habs.datasource.minEvictableIdleTimeMillis=180000
habs.datasource.removeAbandoned=true
habs.datasource.removeAbandonedTimeout=250
##############################################################################################
#æ°æ®åºè¿æ¥éç½®ï¼é¨ç¦
##############################################################################################

mj.datasource.driverClassName=com.mysql.jdbc.Driver
mj.datasource.url=***********************************************************
mj.datasource.username=root
mj.datasource.password=abcABC123!!!
mj.datasource.initialSize=5
mj.datasource.maxActive=300
mj.datasource.maxIdle=10
mj.datasource.minIdle=2
mj.datasource.maxWait=60000
mj.datasource.validationQuery=select VERSION()
mj.datasource.testOnBorrow=true
mj.datasource.testOnReturn=true
mj.datasource.timeBetweenEvictionRunsMillis=60000
mj.datasource.minEvictableIdleTimeMillis=180000
mj.datasource.removeAbandoned=true
mj.datasource.removeAbandonedTimeout=250

#rediséç½®
redis.host=************
redis.port=6379
redis.pass=Soms123654
redis.maxIdle=300
redis.maxTotal=200
redis.maxWaitMillis=2000
redis.testOnBorrow=true
redis.timeout=2000