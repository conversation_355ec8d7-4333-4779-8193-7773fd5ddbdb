package com.air.security.sodb.dts.receive.job;

import com.air.security.sodb.data.core.base.Meta;
import com.air.security.sodb.data.core.constant.GlobalCodeConstant;
import com.air.security.sodb.data.core.constant.MsgIdConstant;
import com.air.security.sodb.data.core.exception.SystemBaseException;
import com.air.security.sodb.data.core.quartz.AbstractJob;
import com.air.security.sodb.data.core.util.DateTimeUtil;
import com.air.security.sodb.data.core.util.HaLog;
import com.air.security.sodb.data.core.util.PropertyUtil;
import com.air.security.sodb.data.core.util.UuidUtil;
import com.air.security.sodb.dts.receive.server.MqMessageRecvServer;
import com.air.security.sodb.dts.receive.server.MqMessageRecvServerImpl;
import com.air.security.sodb.dts.receive.util.*;
import com.alibaba.fastjson.JSONArray;
import com.alibaba.fastjson.JSONObject;
import org.apache.commons.lang3.StringUtils;
import org.quartz.JobDataMap;
import org.slf4j.Logger;
import org.slf4j.LoggerFactory;

import java.io.IOException;
import java.util.ArrayList;
import java.util.List;

/**
 * 门禁资源变更信息获取任务
 *
 * <AUTHOR>
 */
public class HkMjEquStateJob extends AbstractJob {

    private static final Logger log = LoggerFactory.getLogger(HkMjEquStateJob.class);


    private static String URLINFO = PropertyUtil.getProperty("hk.mjEquInfo.url");

    private static String URL = PropertyUtil.getProperty("hk.mjEquState.url");

    private static String videoEquinfo = PropertyUtil.getProperty("sodb.ec02.equ.status.req.url");

    /**
     *
     */
    private MqMessageRecvServer server = new MqMessageRecvServerImpl();

    @Override
    public void executeJob(JobDataMap paramMap) {
        HaLog.info(log, MsgIdConstant.MS_INF_0001, "获取门禁资源变更信息定时任务");

        try {
            List<String> list = new ArrayList<>();
            execPostInfo(list, URLINFO, SrvcHelper.PAGE_SIZE, 1);
            if (null != list && list.size() > 0) {

                //查询数据库中的资源编码和状态
                ResponseData<YsEquInfo> haObj = HaBaseUtils.getInstance().getHaObj(null, videoEquinfo, ResponseData.class, null);
                List<YsEquInfo> cacheList = haObj.getDataListObj(YsEquInfo.class);
                for (YsEquInfo ysEquInfo : cacheList) {
                    String equCode = ysEquInfo.getEquCode();

                    boolean contains = list.contains(equCode);
                    if (!contains) {
                        continue;
                    }
                    String[] arr = new String[1];
                    JSONObject jsonObject = new JSONObject();
                    arr[0] = equCode;
                    //资源状态

                    jsonObject.put("doorIndexCodes", arr);

                    //查询对应的状态
                    String message = CertificationInfoUtil.send(URL, jsonObject.toString());

                    JSONObject result = JSONObject.parseObject(message);
                    String msg = result.getString("msg");
                    String timeStateId = ysEquInfo.getTimeStateId();
                    if (!"success".equals(msg)) {
                        continue;
                    }
                    JSONArray jsonArray = result.getJSONObject("data").getJSONArray("authDoorList");

                    if (null == jsonArray || jsonArray.size() == 0) {
                        continue;
                    }
                    JSONObject json = (JSONObject) jsonArray.get(0);
                    String onlineStatus = json.getString("doorState");

                    //资源编码转换
                    onlineStatus = formatStatus(onlineStatus);
                    if (!StringUtils.equals(timeStateId, onlineStatus)) {
                        json.put("operateTime", DateTimeUtil.getCurrentTimestampStr("yyyy-MM-dd HH:mm:ss"));
                        json.put("timeStateId", onlineStatus);
                        //编辑消息头
                        Meta meta = new Meta();
                        meta.setEventType("mjEquState");
                        meta.setRecvSequence(UuidUtil.getUuid32());
                        server.handle(meta, json.toString());
                    }
                }
            }

        } catch (Exception e) {
            throw new SystemBaseException("获取门禁资源变更信息失败", e);
        } finally {
            HaLog.info(log, MsgIdConstant.MS_INF_0002, "获取门禁资源变更信息定时任务");
        }

    }

    /**
     * @param list
     * @param url
     * @param pageSize
     * @param pageNo
     * @throws IOException
     */
    private void execPostInfo(List<String> list, String url, int pageSize, int pageNo) throws IOException {
        JSONObject jsonObject = SrvcHelper.getPageParam(pageNo, pageSize);
        jsonObject.put("resourceType", "door");
        String messageBody = CertificationInfoUtil.send(url, jsonObject.toJSONString());

        // 获取分页数据
        PageInfo page = SrvcHelper.getPageInfo(messageBody);
        if (page.getTotalRows() > 0) {
            JSONObject json = JSONObject.parseObject(messageBody);
            JSONArray jsonJsonArray = json.getJSONObject("data").getJSONArray("list");
            for (Object o : jsonJsonArray) {
                JSONObject json1 = (JSONObject) o;
                String indexCode = json1.getString("indexCode");
                list.add(indexCode);
            }
            if (page.hasNextPage()) {
                // 查询下一页
                this.execPostInfo(list, url, pageSize, page.getCurrentPage() + 1);
            }
        }
    }


    /**
     * 资源编码转换
     *
     * @param onlineStatus 状态
     * @return
     */
    private String formatStatus(String onlineStatus) {
        if (GlobalCodeConstant.ZERO.equalsIgnoreCase(onlineStatus)) {
            onlineStatus = "ES01";
        }
        if (GlobalCodeConstant.ONE.equalsIgnoreCase(onlineStatus)) {
            onlineStatus = "ES01";
        }
        if (GlobalCodeConstant.TWO.equalsIgnoreCase(onlineStatus)) {
            onlineStatus = "ES01";
        }
        if (GlobalCodeConstant.THREE.equalsIgnoreCase(onlineStatus)) {
            onlineStatus = "ES02";
        }
        return onlineStatus;
    }


}
