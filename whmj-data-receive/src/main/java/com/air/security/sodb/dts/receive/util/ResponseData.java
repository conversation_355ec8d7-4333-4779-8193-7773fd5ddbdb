package com.air.security.sodb.dts.receive.util;

import com.alibaba.fastjson.JSONArray;
import com.alibaba.fastjson.JSONObject;
import com.github.pagehelper.PageInfo;

import java.util.Collection;
import java.util.List;
import java.util.Map;

/**
 * 处理API请求返回数据
 * <AUTHOR>
 * @param <T>
 */
public class ResponseData<T> {

    /**
     * 返回码
     */
    private Integer resultCode;

    /**
     * 返回信息 错误或正确信息
     */
    private String msg;

    /**
     * 返回的数据对象
     */
    private T data;

    /**
     * 数据对象的集合
     */
    private List<T> datalist;

    /**
     * 分页参数
     */
    private Page page;


    public ResponseData(){
        super();
    }

    public ResponseData(Integer resultCode, String msg){
        super();
        this.resultCode = resultCode;
        this.msg = msg;
    }

    @SuppressWarnings("unchecked")
    public ResponseData(Integer resultCode, String msg, Object obj){
        super();
        this.resultCode = resultCode;
        this.msg = msg;
        if( obj instanceof Collection){
            this.datalist = (List<T>) obj;
        } else if( obj instanceof PageInfo){
            PageInfo<T> pa = (PageInfo<T>)obj;
            this.datalist = pa.getList();
            this.page = new Page();
            this.page.setNowPage(pa.getPageNum());
            this.page.setPageCount(pa.getPages());
            this.page.setPageSize(pa.getPageSize());
            this.page.setRecordCount(pa.getTotal());
        } else {
            this.data = (T) obj;
        }
    }

    public ResponseData(Integer resultCode, String msg, List<T> obj){
        super();
        this.resultCode = resultCode;
        this.msg = msg;
        this.datalist = obj;
    }

    public ResponseData(Integer resultCode, String msg, PageInfo<T> page){
        super();
        this.resultCode = resultCode;
        this.msg = msg;
        this.datalist = page.getList();
        this.page.setNowPage(page.getPageNum());
        this.page.setPageCount(page.getPages());
        this.page.setPageSize(page.getPageSize());
        this.page.setRecordCount(page.getTotal());
    }

    public static <T> ResponseData<T> success(){

        return new ResponseData<T>(HaConstant.HA_API_RESULT_CODE_SUCCESS,HaConstant.HA_API_RESULT_CODE_SUCCESS_MSG);
    }

    public static <T> ResponseData<T> success(Object obj){

        return new ResponseData<T>(HaConstant.HA_API_RESULT_CODE_SUCCESS,HaConstant.HA_API_RESULT_CODE_SUCCESS_MSG,obj);
    }

    public static <T> ResponseData<T> failed(){

        return new ResponseData<T>(HaConstant.HA_API_RESULT_CODE_FAILED,HaConstant.HA_API_RESULT_CODE_FAILED_MSG);
    }

    public static <T> ResponseData<T> failed(Integer code, String msg){

        return new ResponseData<T>(code,msg);
    }

    public static <T> ResponseData<T> loginFailed(){

        return new ResponseData<T>(HaConstant.HA_API_RESULT_CODE_LOGIN_ERROR,
                HaConstant.HA_API_RESULT_CODE_LOGIN_ERROR_MSG);
    }

    public Integer getResultCode() {
        return resultCode;
    }

    public void setResultCode(Integer resultCode) {
        this.resultCode = resultCode;
    }

    public String getMsg() {
        return msg;
    }

    public void setMsg(String msg) {
        this.msg = msg;
    }

    public T getData() {
        return data;
    }

    public void setData(T data) {
        this.data = data;
    }

    public List<T> getDatalist() {
        return datalist;
    }

    public void setDatalist(List<T> datalist) {
        this.datalist = datalist;
    }

    public Page getPage() {
        return page;
    }

    public void setPage(Page page) {
        this.page = page;
    }

    /**
     * 获取返回的数据对象
     * @param res
     * @return
     */
    @SuppressWarnings("unchecked")
    public <H> H getDataObj(Class<H> res){
        if(null != data){
            if(data instanceof Map){
                String json = JSONObject.toJSONString(data);
                return JSONObject.toJavaObject(JSONObject.parseObject(json),res);
            } else {
                return (H) this.getData();
            }
        } else {
            return null;
        }
    }

    /**
     * 获取返回的数据对象
     * @param res
     * @return
     */
    public List<T> getDataListObj(Class<T> res){
        if(null != datalist){
            String json = JSONArray.toJSONString(datalist);
            return JSONArray.parseArray(json,res);
        } else {
            return null;
        }
    }

    public class Page{
        /**
         * 当前页
         */
        private Integer nowPage;

        /**
         * 每页显示的记录数
         */
        private Integer pageSize;

        /**
         * 总页数
         */
        private Integer pageCount;

        /**
         * 总记录数
         */
        private Long recordCount;

        public Integer getNowPage() {
            return nowPage;
        }

        public void setNowPage(Integer nowPage) {
            this.nowPage = nowPage;
        }

        public Integer getPageSize() {
            return pageSize;
        }

        public void setPageSize(Integer pageSize) {
            this.pageSize = pageSize;
        }

        public Integer getPageCount() {
            return pageCount;
        }

        public void setPageCount(Integer pageCount) {
            this.pageCount = pageCount;
        }

        public Long getRecordCount() {
            return recordCount;
        }

        public void setRecordCount(Long recordCount) {
            this.recordCount = recordCount;
        }

    }
}