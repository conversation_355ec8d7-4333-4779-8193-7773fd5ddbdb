package com.air.security.sodb.data.receive.service.scims;

import sun.misc.BASE64Decoder;
import sun.misc.BASE64Encoder;

import javax.crypto.*;
import javax.crypto.spec.DESKeySpec;
import javax.crypto.spec.IvParameterSpec;
import java.io.IOException;
import java.io.UnsupportedEncodingException;
import java.security.InvalidAlgorithmParameterException;
import java.security.InvalidKeyException;
import java.security.Key;
import java.security.NoSuchAlgorithmException;
import java.security.spec.InvalidKeySpecException;
import java.util.Arrays;

/**
 * DES加密解密
 * <AUTHOR>
public class DesUtils {
    public final static String ALGORITHM_DES = "DES";
    public final static String PREFIX = "XMCares";
    public final static String XJKY = "XMCaresXJKY";
    private static final char PADDING_CHAR = '`';

    /**
     * 加密字符串
     *
     * @param code       需要加密的字符串
     * @param encryptKey 对称密钥串
     * @throws NoSuchAlgorithmException
     * @throws UnsupportedEncodingException
     * @throws InvalidKeyException
     * @throws NoSuchPaddingException
     * @throws IllegalBlockSizeException
     * @throws BadPaddingException
     * @throws InvalidKeySpecException
     */
    public static String encryptDes(String code, String encryptKey)
            throws NoSuchAlgorithmException, UnsupportedEncodingException,
            InvalidKeyException, NoSuchPaddingException,
            IllegalBlockSizeException, BadPaddingException,
            InvalidKeySpecException, InvalidAlgorithmParameterException {
        BASE64Encoder base64en = new BASE64Encoder();
        byte[] bytesMing = encryptDes(code.getBytes("UTF-8"), encryptKey);
        return base64en.encode(bytesMing).replaceAll(
                "(?:\\r\\n|\\n\\r|\\n|\\r)", "");
    }

    /**
     * 解密字符串
     *
     * @param code       需要解密的字符串
     * @param encryptKey 对称密钥串
     * @throws IOException
     * @throws InvalidKeyException
     * @throws NoSuchAlgorithmException
     * @throws NoSuchPaddingException
     * @throws IllegalBlockSizeException
     * @throws BadPaddingException
     * @throws InvalidKeySpecException
     */
    public static String decryptDes(String code, String encryptKey)
            throws IOException, InvalidKeyException, NoSuchAlgorithmException,
            NoSuchPaddingException, IllegalBlockSizeException,
            BadPaddingException, InvalidKeySpecException,
            InvalidAlgorithmParameterException {
        BASE64Decoder base64De = new BASE64Decoder();
        byte[] bytesMi = base64De.decodeBuffer(code);
        byte[] bytesMing = decryptDes(bytesMi, encryptKey);
        int offset = -1;
        for (int i = bytesMing.length - 1; i >= 0; i--) {
            if (bytesMing[i] == PADDING_CHAR) {
                offset = i;
            } else {
                break;
            }
        }
        if (offset != -1) {
            bytesMing = Arrays.copyOf(bytesMing, offset);
        }
        return new String(bytesMing, "UTF8");
    }

    /**
     * 加密
     *
     * @param bytes      明文字节
     * @param encryptKey 对称密钥串
     * @throws NoSuchAlgorithmException
     * @throws NoSuchPaddingException
     * @throws InvalidKeyException
     * @throws IllegalBlockSizeException
     * @throws BadPaddingException
     * @throws InvalidKeySpecException
     */
    public static byte[] encryptDes(byte[] bytes, String encryptKey)
            throws NoSuchAlgorithmException, NoSuchPaddingException,
            InvalidKeyException, IllegalBlockSizeException,
            BadPaddingException, InvalidKeySpecException,
            InvalidAlgorithmParameterException {

        Key key = generateKey(encryptKey);
        Cipher cipher = Cipher.getInstance(ALGORITHM_DES + "/CBC/NoPadding");
        IvParameterSpec ips = new IvParameterSpec(new byte[]{0, 0, 0, 0, 0,
                0, 0, 0});
        cipher.init(Cipher.ENCRYPT_MODE, key, ips);
        return cipher.doFinal(padding(bytes));
    }

    private static byte[] padding(byte[] bytes) {
        int len = bytes.length % 8;
        if (len == 0) {
            return bytes;
        }
        len = 8 - len;
        byte[] padding = new byte[len];
        for (int i = 0; i < len; i++) {
            padding[i] = PADDING_CHAR;
        }
        bytes = concat(bytes, padding);
        return bytes;
    }

    private static byte[] concat(byte[] first, byte[] second) {
        byte[] result = Arrays.copyOf(first, first.length + second.length);
        System.arraycopy(second, 0, result, first.length, second.length);
        return result;
    }

    /**
     * 生成密钥
     *
     * @param encryptKey
     * @throws NoSuchAlgorithmException
     * @throws InvalidKeyException
     * @throws InvalidKeySpecException
     */
    public static Key generateKey(String encryptKey)
            throws NoSuchAlgorithmException, InvalidKeyException,
            InvalidKeySpecException {
        SecretKeyFactory keyFactory = SecretKeyFactory
                .getInstance(ALGORITHM_DES);
        DESKeySpec keySpec = new DESKeySpec(encryptKey.getBytes());
        return keyFactory.generateSecret(keySpec);
    }

    /**
     * 解密
     *
     * @param bytes      密文字节
     * @param encryptKey 对称密钥串
     * @throws NoSuchAlgorithmException
     * @throws NoSuchPaddingException
     * @throws InvalidKeyException
     * @throws IllegalBlockSizeException
     * @throws BadPaddingException
     * @throws InvalidKeySpecException
     */
    public static byte[] decryptDes(byte[] bytes, String encryptKey)
            throws NoSuchAlgorithmException, NoSuchPaddingException,
            InvalidKeyException, IllegalBlockSizeException,
            BadPaddingException, InvalidKeySpecException,
            InvalidAlgorithmParameterException {
        Key key = generateKey(encryptKey);
        Cipher cipher = Cipher.getInstance(ALGORITHM_DES + "/CBC/NoPadding");
        IvParameterSpec ips = new IvParameterSpec(new byte[]{0, 0, 0, 0, 0,
                0, 0, 0});
        cipher.init(Cipher.DECRYPT_MODE, key, ips);
        return cipher.doFinal(bytes);
    }

    public static void main(String[] args) throws Exception {
        String data = "戴学虔";

        System.out.println("hex:" + bytes2HexString(data.getBytes()));
        System.out.println(data.getBytes("utf-8").length);
        System.out.println(data.getBytes().length);
        String key = DesUtils.XJKY;

        String encData = DesUtils.encryptDes(data, key);
        System.out.println(encData);
        System.out.println(DesUtils.decryptDes("y3kDZDOuD0t1nmV9NfU8tQ==", key));
    }

    public static String bytes2HexString(byte[] b) {
        String ret = "";
        for (int i = 0; i < b.length; i++) {
            String hex = Integer.toHexString(b[i] & 0xFF);
            if (hex.length() == 1) {
                hex = '0' + hex;
            }
            ret += hex.toUpperCase();
        }
        return ret;
    }
}

