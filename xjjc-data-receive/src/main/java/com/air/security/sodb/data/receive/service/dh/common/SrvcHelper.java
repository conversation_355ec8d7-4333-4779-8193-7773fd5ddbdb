package com.air.security.sodb.data.receive.service.dh.common;

import com.air.security.sodb.data.receive.constant.SrvcCodeConstants.OpenType;
import com.air.security.sodb.data.core.exception.SystemBaseException;
import com.air.security.sodb.data.core.util.PropertyUtil;
import com.air.security.sodb.data.receive.ifdomain.PageInfo;
import com.air.security.sodb.data.receive.util.IntegerUtil;
import com.alibaba.fastjson.JSONObject;

/**
 * <AUTHOR>
 */
public class SrvcHelper {

    /**
     *
     */
    public static final int PAGE_SIZE = getPageSize();

    private static final int DEFAULT_PAGE_SIZE = 20;

    /**
     * 大华服务ip:port
     */
    public static final String DH_SRVC_HOST = PropertyUtil.getProperty("rest.dh.host");

    /**
     * 获取分页长度
     *
     * @return
     */
    public static int getPageSize() {
        try {
            String pageSize = PropertyUtil.getProperty("rest.dh.pageSize");
            return Integer.parseInt(pageSize);
        } catch (Exception e) {
            return DEFAULT_PAGE_SIZE;
        }
    }

    /**
     * 获取分页参数
     *
     * @param pageNo
     * @param pageSize
     * @return
     */
    public static JSONObject getPageParam(int pageNo, int pageSize) {
        JSONObject jsonObject = new JSONObject();

        jsonObject.put("pageNum", pageNo);
        jsonObject.put("pageSize", pageSize);
        return jsonObject;
    }

    /**
     * 验证请求数据结果是否正常
     *
     * @param json
     */
    public static void validHandle(JSONObject json) {
        boolean result = json.getBooleanValue("success");
        if (result == false) {
            throw new SystemBaseException("查询数据失败: " + json.getString("errMsg"));
        }
    }

    /**
     * 添加设备类型信息
     *
     * @param obj
     * @param equType
     */
    public static void addEquType(JSONObject obj, EquDataTag equType) {
        obj.put("equClassCode", equType.getEquClassCode());
        obj.put("equClassName", equType.getEquClassName());
        obj.put("equTypeCode", equType.getEquTypeCode());
        obj.put("equTypeName", equType.getEquTypeName());
    }

    /**
     * 获取分页信息
     *
     * @param messageBody
     * @return
     */
    public static PageInfo getPageInfo(String messageBody) {
        JSONObject obj = JSONObject.parseObject(messageBody);
        JSONObject dataObj = obj.getJSONObject("data");

        int currentPage = IntegerUtil.parseInt(dataObj.getString("currentPage"), -1);
        int pageSize = IntegerUtil.parseInt(dataObj.getString("pageSize"), -1);
        int totalPage = IntegerUtil.parseInt(dataObj.getString("totalPage"), -1);
        int totalRows = IntegerUtil.parseInt(dataObj.getString("totalRows"), -1);

        PageInfo page = new PageInfo();
        page.setCurrentPage(currentPage);
        page.setPageSize(pageSize);
        page.setTotalPage(totalPage);
        page.setTotalRows(totalRows);
        return page;
    }

    /**
     * 告警开门类型
     */
    public static final int[] ALARM_OPEN_TYPE = new int[]{
            OpenType.T1,
            OpenType.T3,
            OpenType.T4,
            OpenType.T6,
            OpenType.T7,
            OpenType.T12,
            OpenType.T14,
            OpenType.T15,
            OpenType.T18,
            OpenType.T19,
            OpenType.T22,
            OpenType.T25,
            OpenType.T26,
            OpenType.T27,
            OpenType.T29,
            OpenType.T31,
            OpenType.T32
    };

    /**
     * 判断是否是告警类型
     *
     * @param type
     * @return
     */
    public static boolean isAlarmOpenType(String type) {
        for (int alarmType : ALARM_OPEN_TYPE) {
            if (String.valueOf(alarmType).equals(type)) {
                return true;
            }
        }
        return false;
    }

}
