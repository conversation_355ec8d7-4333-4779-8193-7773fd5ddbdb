package com.air.security.sodb.data.receive.listener;

import com.air.security.sodb.data.core.constant.MsgIdConstant;
import com.air.security.sodb.data.core.util.HaLog;
import com.air.security.sodb.data.core.util.PropertyUtil;
import io.netty.bootstrap.ServerBootstrap;
import io.netty.channel.Channel;
import io.netty.channel.ChannelFuture;
import io.netty.channel.ChannelInitializer;
import io.netty.channel.EventLoopGroup;
import io.netty.channel.nio.NioEventLoopGroup;
import io.netty.channel.socket.nio.NioServerSocketChannel;
import io.netty.handler.codec.http.HttpObjectAggregator;
import io.netty.handler.codec.http.HttpRequestDecoder;
import io.netty.handler.codec.http.HttpResponseEncoder;
import io.netty.handler.timeout.ReadTimeoutHandler;
import org.slf4j.Logger;
import org.slf4j.LoggerFactory;

/**
 * 围界数据接入的HTTPServer
 *
 * <AUTHOR>
 */
public class WallHttpServer implements Runnable {

    private static final Logger log = LoggerFactory.getLogger(WallHttpServer.class);

    private int port = Integer.parseInt(PropertyUtil.getProperty("wall.http.server.port"));
    private int readTimeout = Integer.parseInt(PropertyUtil.getProperty("wall.http.timeout"));

    @Override
    public void run() {
        EventLoopGroup bossGroup = new NioEventLoopGroup();
        EventLoopGroup workerGroup = new NioEventLoopGroup(100);
        try {
            ServerBootstrap b = new ServerBootstrap();
            b.group(bossGroup, workerGroup)
                    .channel(NioServerSocketChannel.class)
                    .childHandler(new ChannelInitializer<Channel>() {
                        @Override
                        protected void initChannel(Channel ch) throws Exception {
                            if (readTimeout > 0) {
                                ch.pipeline().addLast(new ReadTimeoutHandler(readTimeout));
                                // server端发送的是httpResponse，所以要使用HttpResponseEncoder进行编码
                                ch.pipeline().addLast(new HttpResponseEncoder());
                                // server端接收到的是httpRequest，所以要使用HttpRequestDecoder进行解码
                                ch.pipeline().addLast(new HttpRequestDecoder());
                                ch.pipeline().addLast(new HttpObjectAggregator(1048576));
                                ch.pipeline().addLast(new WallHttpServerInboundHandler());
                            }

                        }
                    });

            ChannelFuture f = b.bind(port).sync();
            f.channel().closeFuture().sync();

        } catch (Exception e) {
            HaLog.error(log, MsgIdConstant.MS_ERR_0001, e, "");
        } finally {
            workerGroup.shutdownGracefully();
            bossGroup.shutdownGracefully();
        }
    }

    public int getPort() {
        return port;
    }

    public void setPort(int port) {
        this.port = port;
    }

    public int getReadTimeout() {
        return readTimeout;
    }

    public void setReadTimeout(int readTimeout) {
        this.readTimeout = readTimeout;
    }

}
