package com.air.security.sodb.data;

import com.air.security.sodb.data.core.constant.MsgIdConstant;
import com.air.security.sodb.data.core.quartz.SchedulerManager;
import com.air.security.sodb.data.core.spring.SpringUtil;
import com.air.security.sodb.data.core.util.HaLog;
import com.air.security.sodb.data.core.util.MinioUtil;
import com.air.security.sodb.data.core.util.PropertyUtil;
import com.air.security.sodb.data.core.util.ThreadUtil;
import com.air.security.sodb.data.receive.listener.*;
import org.slf4j.Logger;
import org.slf4j.LoggerFactory;

import java.io.*;
import java.net.ServerSocket;
import java.net.Socket;

/**
 * @Description: 前端数据交互启动-启动所有
 * @author: zhangc
 * @Date: 2018年12月18日
 */
public class RunStartAll {

    private static final Logger log = LoggerFactory.getLogger(RunStartAll.class);

    private static boolean ftiService = Boolean.parseBoolean(PropertyUtil.getProperty("fti.status.service"));
    private static boolean videoService = Boolean.parseBoolean(PropertyUtil.getProperty("video.status.service"));
    private static boolean wallHttpService = Boolean.parseBoolean(PropertyUtil.getProperty("wall.http.service"));
    private static boolean lkajService = Boolean.parseBoolean(PropertyUtil.getProperty("lkaj.service"));
    private static boolean cargoService = Boolean.parseBoolean(PropertyUtil.getProperty("cargo.service"));

    private static ServerSocket server;

    private static final String BUCKET = PropertyUtil.getProperty("bucket");

    public static void main(String[] args) throws Exception {
        start();
//        if (StringUtils.equals("start", args[0])) {
//        } else if (StringUtils.equals("stop", args[0])) {
//        	stop();
//        }
    }

    private static void start() throws Exception {
        // 初始化spring
        SpringUtil.initSpring();

        MinioUtil.makeBucket(BUCKET);

        if (ftiService) {
            FtiRabbitMqConsumer rabbitMqConsumer = new FtiRabbitMqConsumer();
            rabbitMqConsumer.receiveData();
        }

        if (videoService) {
            VideoStatusRabbitMqConsumer videoStatusRabbitMqConsumer = new VideoStatusRabbitMqConsumer();
            videoStatusRabbitMqConsumer.receiveData();
        }

        if (wallHttpService) {
            WallHttpServer wallHttpServer = new WallHttpServer();
            ThreadUtil.runWithNewThread(wallHttpServer);
        }

        if (lkajService) {
            LkajActiveMqListener lkajActiveMqListener = new LkajActiveMqListener();
            lkajActiveMqListener.receiveData(lkajActiveMqListener);
        }

        if (cargoService) {
            ThreadUtil.runWithNewThread(new KafkaCargoListener());
        }
        runQuartzJob();

        stopJob(Integer.valueOf(PropertyUtil.getProperty("bjjc.data.receive.stop.port")), "xjjc-data-receive");
    }

    private static void stop() {
        HaLog.info(log, MsgIdConstant.MS_INF_0001, "job停止处理");
        Socket command = null;
        BufferedWriter writer = null;
        try {

            // 创建Socket
            command = new Socket("127.0.0.1", Integer.valueOf(PropertyUtil.getProperty("bjjc.data.receive.stop.port")));
            writer = new BufferedWriter(new OutputStreamWriter(command.getOutputStream()));

            // 发送STOP命令
            writer.write("STOP");
            writer.newLine();
            writer.flush();
        } catch (Exception e) {
            HaLog.info(log, MsgIdConstant.MS_ERR_0001, e);
        } finally {
            if (writer != null) {
                try {
                    writer.close();
                } catch (IOException e) {
                    HaLog.info(log, MsgIdConstant.MS_ERR_0001, e);
                }
            }
            if (command != null) {
                try {
                    command.close();
                } catch (IOException e) {
                    HaLog.info(log, MsgIdConstant.MS_ERR_0001, e);
                }
            }
        }

        HaLog.info(log, MsgIdConstant.MS_INF_0002, "job停止处理");
        System.exit(0);
    }

    /**
     * 启动定时任务
     */
    private static void runQuartzJob() {
        HaLog.info(log, MsgIdConstant.MS_INF_0001, "quartz定时任务");
        SchedulerManager manager = new SchedulerManager();
        manager.runJob("quartzJob.xml");
        HaLog.info(log, MsgIdConstant.MS_INF_0002, "quartz定时任务");
    }

    /**
     * 停止定时任务
     */
    private static void shutQuartzJob() {
        HaLog.info(log, MsgIdConstant.MS_INF_0001, "quartz定时任务");
        SchedulerManager manager = new SchedulerManager();
        manager.shutJob();
        HaLog.info(log, MsgIdConstant.MS_INF_0002, "quartz定时任务");
    }

    /**
     * 监听停止服务命令
     *
     * @param port        端口号
     * @param projectName 工程名
     */
    private static void stopJob(int port, String projectName) {
        Socket commandSocket = null;
        BufferedReader reader = null;

        try {
            server = new ServerSocket(port);
            commandSocket = server.accept();
            reader = new BufferedReader(new InputStreamReader(commandSocket.getInputStream()));
        } catch (IOException e) {
            HaLog.info(log, MsgIdConstant.MS_ERR_0001, e);
            System.exit(0);
        }

        while (true) {
            try {
                String commandStr = reader.readLine();
                if ("STOP".equals(commandStr)) {

                    // 关闭数据流
                    reader.close();
                    commandSocket.close();
                    server.close();

                    shutQuartzJob();
                    ThreadUtil.shutdown();

                    HaLog.info(log, MsgIdConstant.MS_INF_0002, projectName);
                    System.exit(0);
                }

                Thread.sleep(1000);
            } catch (IOException e) {
                HaLog.info(log, MsgIdConstant.MS_ERR_0001, e);
            } catch (InterruptedException e) {
                HaLog.info(log, MsgIdConstant.MS_ERR_0001, e);
            }
        }
    }

}
