package com.air.security.sodb.data.receive.service.svms;

import org.slf4j.Logger;
import org.slf4j.LoggerFactory;
import org.springframework.stereotype.Service;

import com.air.security.sodb.data.core.constant.MsgIdConstant;
import com.air.security.sodb.data.core.exception.SystemBaseException;
import com.air.security.sodb.data.core.util.HaLog;
import com.air.security.sodb.data.core.base.Meta;
import com.air.security.sodb.data.receive.service.AbstractMessageRecvService;
import com.alibaba.fastjson.JSONObject;

/**
 * @Description: 视频结构化数据格式化的Service实现
 * @author: zhangc
 * @Date: 2018年12月26日
 */
@Service("SvmsMessageRecvFormatServiceImpl")
public class SvmsMessageRecvFormatServiceImpl extends AbstractMessageRecvService {

    private static final Logger log = LoggerFactory.getLogger(SvmsMessageRecvFormatServiceImpl.class);

    /**
     * 业务处理
     */
    @Override
    public void execute(Meta meta, String messageBody) throws SystemBaseException {
        HaLog.info(log, MsgIdConstant.MS_INF_0001, meta.getEventType());

        // 获取到的json业务数据
        JSONObject inputJson = JSONObject.parseObject(messageBody);
        Object inputBody = inputJson.get("SPSBaseResultInfo");

        // 响应json消息
        JSONObject outputMsg = new JSONObject();

        // 响应消息头
        outputMsg.put("meta", meta);

        // 响应消息体
        outputMsg.put("body", inputBody);

        // 转换为JSON格式，并发送消息到指定主题
        super.putSendMessage(outputMsg);

        HaLog.info(log, MsgIdConstant.MS_INF_0002, meta.getEventType());
    }

}
