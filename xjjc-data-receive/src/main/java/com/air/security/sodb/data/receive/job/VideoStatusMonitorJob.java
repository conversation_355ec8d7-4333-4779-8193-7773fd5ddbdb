package com.air.security.sodb.data.receive.job;

import com.air.security.sodb.data.core.base.Meta;
import com.air.security.sodb.data.core.constant.MsgIdConstant;
import com.air.security.sodb.data.core.quartz.AbstractJob;
import com.air.security.sodb.data.core.util.HaLog;
import com.air.security.sodb.data.core.util.PropertyUtil;
import com.air.security.sodb.data.core.util.UuidUtil;
import com.air.security.sodb.data.receive.server.MqMessageRecvServer;
import com.air.security.sodb.data.receive.server.MqMessageRecvServerImpl;
import com.alibaba.fastjson.JSONArray;
import com.alibaba.fastjson.JSONObject;
import com.rabbitmq.client.*;
import org.apache.commons.lang3.StringUtils;
import org.quartz.JobDataMap;
import org.slf4j.Logger;
import org.slf4j.LoggerFactory;

import java.io.IOException;
import java.util.concurrent.TimeoutException;
/**
 * 流媒体服务状态通知采集任务 定时任务
 * <AUTHOR>
public class VideoStatusMonitorJob extends AbstractJob {

    private static final Logger log = LoggerFactory.getLogger(VideoStatusMonitorJob.class);

//	private static String queneName = PropertyUtil.getProperty("video.rabbit.mq.queue");

    private static String exchangeName = PropertyUtil.getProperty("video.rabiit.mq.exchange");

    private static String uri = PropertyUtil.getProperty("video.rabbit.mq.uri");

    private static String routeKey = PropertyUtil.getProperty("video.rabiit.mq.routeKey");

    private static String videoEquStatusCode = PropertyUtil.getProperty("video.equ.status.service.code");

    private static int videoEquStatusCacheSize = Integer.parseInt(PropertyUtil.getProperty("video.equ.status.cache.size"));

    private MqMessageRecvServer server = new MqMessageRecvServerImpl();

    @Override
    public void executeJob(JobDataMap paramMap) {
        HaLog.info(log, MsgIdConstant.MS_INF_0001, "流媒体服务状态通知采集任务");
        ConnectionFactory factory = new ConnectionFactory();
        try {
            factory.setUri(uri);
            Connection connection = factory.newConnection();
            Channel channel = connection.createChannel();

            // 声明交换机类型
            channel.exchangeDeclare(exchangeName, "direct", true);
            String queueName = channel.queueDeclare().getQueue();
            channel.queueBind(queueName, exchangeName, routeKey);
            JSONArray jsonArray = new JSONArray();
            Consumer consumer = new DefaultConsumer(channel) {
                @Override
                public void handleDelivery(String consumerTag, Envelope envelope, AMQP.BasicProperties properties,
                                           byte[] body) throws IOException {

                    // 获取到的数据
                    String message = new String(body, "UTF-8");
                    JSONObject msgJson = JSONObject.parseObject(message);

                    // 数据类型
                    String cmdtype = msgJson.getString("cmdtype");
                    String str = "10011";
                    if (StringUtils.equals(str, cmdtype)) {
                        Meta meta = new Meta();
                        meta.setEventType(videoEquStatusCode);
                        meta.setRecvSequence(UuidUtil.getUuid32());

                        JSONArray equStatusArray = msgJson.getJSONArray("status");
                        jsonArray.addAll(equStatusArray);
                        if (jsonArray.size() >= videoEquStatusCacheSize) {
                            server.handle(meta, jsonArray.toJSONString());
                            jsonArray.clear();
                            try {
                                channel.close();
                            } catch (TimeoutException e) {
                                e.printStackTrace();
                            }
                            return;
                        }
                    }

                }
            };

            channel.basicConsume(queueName, true, consumer);
        } catch (Exception e) {
            HaLog.error(log, MsgIdConstant.MS_ERR_0001, e, "");
        } finally {
            HaLog.info(log, MsgIdConstant.MS_INF_0002, "大华摄像机通道状态采集任务");
        }
    }

}
