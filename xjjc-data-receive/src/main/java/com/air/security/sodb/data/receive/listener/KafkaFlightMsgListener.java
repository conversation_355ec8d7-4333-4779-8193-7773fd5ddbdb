package com.air.security.sodb.data.receive.listener;

import com.air.security.sodb.data.core.base.Meta;
import com.air.security.sodb.data.core.constant.MsgIdConstant;
import com.air.security.sodb.data.core.util.HaLog;
import com.air.security.sodb.data.core.util.KafkaConscumerUtil;
import com.air.security.sodb.data.core.util.PropertyUtil;
import com.air.security.sodb.data.core.util.ThreadUtil;
import com.air.security.sodb.data.receive.server.MqMessageRecvServer;
import com.air.security.sodb.data.receive.server.MqMessageRecvServerImpl;
import com.alibaba.fastjson.JSONArray;
import com.alibaba.fastjson.JSONObject;
import org.apache.commons.lang.StringUtils;
import org.apache.kafka.clients.consumer.ConsumerRecord;
import org.apache.kafka.clients.consumer.ConsumerRecords;
import org.apache.kafka.clients.consumer.KafkaConsumer;
import org.slf4j.Logger;
import org.slf4j.LoggerFactory;

import java.time.Duration;

/**
 * @Description: kafka事件消息监听
 * @author: zhangc
 * @Date: 2018年12月18日
 */
public class KafkaFlightMsgListener implements Runnable {

    private static final Logger log = LoggerFactory.getLogger(KafkaFlightMsgListener.class);

    /**
     * 消息分发处理器
     */
    private MqMessageRecvServer server = new MqMessageRecvServerImpl();

    /**
     * 报警消息发布服务主题
     */
    private static String topic = PropertyUtil.getProperty("kafka.flight.msg.input.topics");

    @Override
    public void run() {

        HaLog.info(log, MsgIdConstant.MS_INF_0001, "kafka事件消息监听");

        KafkaConsumer<String, String> consumer = KafkaConscumerUtil.getConsumer(new String[]{topic});
        while (true) {
            ThreadUtil.sleepThreadUnit();

            // 消费消息
            ConsumerRecords<String, String> records = consumer.poll(Duration.ZERO);
            for (ConsumerRecord<String, String> record : records) {
                String message = record.value();

                if (StringUtils.isBlank(message)) {
                    continue;
                }

                // 接收消息日志
                HaLog.infoJson(log, message);

                JSONObject msgJson = JSONObject.parseObject(message);

                String tableName = msgJson.getString("tableName");
                JSONArray messageBody = msgJson.getJSONArray("rows");

                Meta meta = new Meta();
                meta.setEventType(tableName);
                server.handle(meta, messageBody.toJSONString());
            }
        }
    }

}
