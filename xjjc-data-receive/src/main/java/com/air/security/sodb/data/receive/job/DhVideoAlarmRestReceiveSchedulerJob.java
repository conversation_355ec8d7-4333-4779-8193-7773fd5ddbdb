package com.air.security.sodb.data.receive.job;

import com.air.security.sodb.data.core.base.Meta;
import com.air.security.sodb.data.core.constant.MsgIdConstant;
import com.air.security.sodb.data.core.exception.SystemBaseException;
import com.air.security.sodb.data.core.quartz.AbstractJob;
import com.air.security.sodb.data.core.util.DateTimeUtil;
import com.air.security.sodb.data.core.util.HaLog;
import com.air.security.sodb.data.core.util.PropertyUtil;
import com.air.security.sodb.data.core.util.UuidUtil;
import com.air.security.sodb.data.receive.constant.SrvcCodeConstants;
import com.air.security.sodb.data.receive.ifdomain.PageInfo;
import com.air.security.sodb.data.receive.server.MqMessageRecvServer;
import com.air.security.sodb.data.receive.server.MqMessageRecvServerImpl;
import com.air.security.sodb.data.receive.service.dh.common.SrvcHelper;
import com.air.security.sodb.data.receive.util.IntegerUtil;
import com.alibaba.fastjson.JSONArray;
import com.alibaba.fastjson.JSONObject;
import org.apache.cxf.endpoint.Client;
import org.apache.cxf.jaxws.endpoint.dynamic.JaxWsDynamicClientFactory;
import org.quartz.JobDataMap;
import org.slf4j.Logger;
import org.slf4j.LoggerFactory;

import java.text.SimpleDateFormat;
import java.util.Calendar;
import java.util.Date;

/**
 * <AUTHOR>
 * @Description: 大华视频综合应用平台系统报警日志数据接入
 * @date 2019年12月3日 下午4:53:31
 */
public class DhVideoAlarmRestReceiveSchedulerJob extends AbstractJob {

    private static final Logger log = LoggerFactory.getLogger(DhVideoAlarmRestReceiveSchedulerJob.class);

    private static String dhVideoMethod = SrvcCodeConstants.DhAlarmCode.DH_VIDEO_METHOD;

    private static String dhAlarmInterfaceId = SrvcCodeConstants.DhAlarmCode.DH_VIDEO_ALARM_INTERFACEID;
    /**
     * 获取大华报警日志-系统报警日志服务URI
     */
    private static String getAlarmURI = PropertyUtil.getProperty("rest.dh.alarmLog.url");

    private static String orderType = SrvcCodeConstants.DhAlarmCode.ORDER_TYPE;

    private MqMessageRecvServer server = new MqMessageRecvServerImpl();

    private static String dhVideoAlarmServiceCode = PropertyUtil.getProperty("dh.video.alarm.service.code");

    private static int timeInterVal = Integer.parseInt(PropertyUtil.getProperty("time.interval"));

    private String token = PropertyUtil.getProperty("dh.video.token");

    private static String startTimeCache = null;

    @Override
    public void executeJob(JobDataMap paramMap) {
        HaLog.info(log, MsgIdConstant.MS_INF_0001, "获取大华报警日志数据定时任务");

        try {
            SimpleDateFormat sdf = new SimpleDateFormat("yyyy-MM-dd HH:mm:ss");
            Date endTime = new Date();
            String maxAlarmDateStr = sdf.format(endTime);
            if (null == startTimeCache) {
                String minAlarmDateStr = sdf.format(DateTimeUtil.getNextDate(Calendar.SECOND, timeInterVal, endTime));
                startTimeCache = minAlarmDateStr;
            }
            getAllMessage(getAlarmURI, token, dhVideoMethod, dhAlarmInterfaceId, SrvcHelper.PAGE_SIZE, 1, maxAlarmDateStr, startTimeCache);
            startTimeCache = maxAlarmDateStr;
        } catch (Exception e) {
            throw new SystemBaseException("获取大华告警信息失败", e);
        } finally {
            HaLog.info(log, MsgIdConstant.MS_INF_0002, "获取大华报警日志数据定时任务");
        }

    }

    public void getAllMessage(String url, String token, String method, String interfaceId, int pageSize, int pageNo, String maxAlarmDateStr, String minAlarmDateStr)
            throws Exception {

        Client client = JaxWsDynamicClientFactory.newInstance().createClient(url);

        JSONObject json = new JSONObject();
        JSONObject paramJson = new JSONObject();
        JSONObject pageJson = SrvcHelper.getPageParam(pageNo, pageSize);
        paramJson.put("pagination", pageJson);
        JSONObject authorinizeJson = new JSONObject();
        authorinizeJson.put("loginCode", token);
        paramJson.put("authorinize", authorinizeJson);
        JSONArray orders = new JSONArray();
        String[] orderTypes = orderType.split(",");
        for (String order : orderTypes) {
            JSONObject orderJson = new JSONObject();
            orderJson.put("propertyName", order);
            orderJson.put("ascending", false);
            orders.add(orderJson);
        }
        paramJson.put("orders", orders);

        JSONObject param = new JSONObject();
        param.put("alarmCategory", "device");
        // 结束时间
        param.put("maxAlarmDateStr", maxAlarmDateStr);
        // 开始时间
        param.put("minAlarmDateStr", minAlarmDateStr);

        paramJson.put("param", param);

        HaLog.info(log, MsgIdConstant.MS_INF_0009, "视频报警接口调用参数", paramJson.toJSONString());

        json = JSONObject.parseObject(
                (client.invoke(method, new Object[]{interfaceId, paramJson.toJSONString()})[0]).toString());
        HaLog.info(log, MsgIdConstant.MS_INF_0003, "大华视频报警日志", json);

        // 获取分页数据
        PageInfo page = getPageInfo(json);
        HaLog.info(log, MsgIdConstant.MS_INF_0003, "大华视频报警pageinfo", JSONObject.toJSONString(page));
        paramJson = null;
        if (page.getTotalRows() > 0) {

            Meta meta = new Meta();
            meta.setEventType(dhVideoAlarmServiceCode);
            meta.setRecvSequence(UuidUtil.getUuid32());
            server.handle(meta, json.toJSONString());

            if (page.hasNextPage()) {
                // 查询下一页
                this.getAllMessage(url, token, method, interfaceId, pageSize, page.getCurrentPage() + 1, minAlarmDateStr, minAlarmDateStr);
            }
        }
    }

    /**
     * 获取分页信息
     *
     * @param messageBody
     * @return
     */
    public static PageInfo getPageInfo(JSONObject obj) {
        JSONObject dataObj = obj.getJSONObject("pagination");

        int currentPage = IntegerUtil.parseInt(dataObj.getString("currentPage"), -1);
        int pageSize = IntegerUtil.parseInt(dataObj.getString("pageSize"), -1);
        int totalPage = IntegerUtil.parseInt(dataObj.getString("pagesCount"), -1);
        int totalRows = IntegerUtil.parseInt(dataObj.getString("recordsCount"), -1);

        PageInfo page = new PageInfo();
        page.setCurrentPage(currentPage);
        page.setPageSize(pageSize);
        page.setTotalPage(totalPage);
        page.setTotalRows(totalRows);
        return page;
    }

    public static void main(String[] args) {
        DhVideoAlarmRestReceiveSchedulerJob job = new DhVideoAlarmRestReceiveSchedulerJob();
        job.executeJob(null);
    }

}
