package com.air.security.sodb.data.receive.job;

import java.io.IOException;

import com.air.security.sodb.data.core.base.Meta;
import org.quartz.JobDataMap;
import org.slf4j.Logger;
import org.slf4j.LoggerFactory;

import com.air.security.sodb.data.core.constant.MsgIdConstant;
import com.air.security.sodb.data.core.exception.SystemBaseException;
import com.air.security.sodb.data.core.quartz.AbstractJob;
import com.air.security.sodb.data.core.util.HaLog;
import com.air.security.sodb.data.core.util.HttpRequestUtil;
import com.air.security.sodb.data.core.util.PropertyUtil;
import com.air.security.sodb.data.core.util.UuidUtil;
import com.air.security.sodb.data.receive.ifdomain.GetDahuaToken;
import com.air.security.sodb.data.receive.ifdomain.PageInfo;
import com.air.security.sodb.data.receive.server.MqMessageRecvServer;
import com.air.security.sodb.data.receive.server.MqMessageRecvServerImpl;
import com.air.security.sodb.data.receive.service.dh.common.SrvcHelper;
import com.alibaba.fastjson.JSONArray;
import com.alibaba.fastjson.JSONObject;

/**
 * 大华门禁设备状态获取任务
 *
 * <AUTHOR>
 */
public class DhChannelRestReceiveSchedulerJob extends AbstractJob {

    private static final Logger log = LoggerFactory.getLogger(DhChannelRestReceiveSchedulerJob.class);

    private static String dhChannelRestServiceCode = PropertyUtil.getProperty("dh.channelstatus.service.code");
    /**
     * 获取大华通道设备信息定时任务URI
     */
    private static String getChannelURI = PropertyUtil.getProperty("rest.dh.getChannel.uri");

    private MqMessageRecvServer server = new MqMessageRecvServerImpl();

    private JSONArray jsonArray;

    @Override
    public void executeJob(JobDataMap paramMap) {
        HaLog.info(log, MsgIdConstant.MS_INF_0001, "获取大华通道设备信息定时任务");

        String token = GetDahuaToken.getToken();
        try {
            if (null != jsonArray) {
                jsonArray.clear();
                jsonArray = null;
            }

            jsonArray = new JSONArray();
            execPost(SrvcHelper.DH_SRVC_HOST + getChannelURI + "?token=" + token, SrvcHelper.PAGE_SIZE, 1);
        } catch (Exception e) {
            throw new SystemBaseException("获取大华通道设备信息失败", e);
        } finally {
            HaLog.info(log, MsgIdConstant.MS_INF_0002, "获取大华通道设备信息定时任务");
        }

    }

    /**
     * @param url
     * @param pageSize
     * @param pageNo
     * @throws IOException
     */
    private void execPost(String url, int pageSize, int pageNo) throws IOException {
        JSONObject jsonObject = SrvcHelper.getPageParam(pageNo, pageSize);

        String messageBody = HttpRequestUtil.send(url, jsonObject, "utf-8");

        JSONObject obj = JSONObject.parseObject(messageBody);
        SrvcHelper.validHandle(obj);

        JSONArray array = obj.getJSONObject("data").getJSONArray("pageData");

        jsonArray.addAll(array);

        // 获取分页数据
        PageInfo page = SrvcHelper.getPageInfo(messageBody);
        if (page.getTotalRows() > 0) {

            if (page.hasNextPage()) {
                // 查询下一页
                this.execPost(url, pageSize, page.getCurrentPage() + 1);
            }

            if (!page.hasNextPage()) {
                Meta meta = new Meta();
                meta.setEventType(dhChannelRestServiceCode);
                meta.setRecvSequence(UuidUtil.getUuid32());
                server.handle(meta, jsonArray.toJSONString());
            }
        }
    }

}
