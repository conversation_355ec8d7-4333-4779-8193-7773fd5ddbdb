package com.air.security.sodb.data.receive.service.video;

import com.air.security.sodb.data.core.util.HttpRequestUtil;
import com.air.security.sodb.data.core.util.PropertyUtil;
import com.alibaba.fastjson.JSONArray;
import com.alibaba.fastjson.JSONObject;

import java.util.List;
/**
 * 同步获取摄像机设备信息
 * <AUTHOR>
public class VideoEquStatusCache {

    private static List<VideoEquStatus> acsCacheList;

    private static String equStatusRequUrl = PropertyUtil.getProperty("sodb.video.equ.status.req.url");

    public static void init() {
        if (acsCacheList == null) {
            String result = HttpRequestUtil.sendPost(equStatusRequUrl, "");
            JSONObject resJson = JSONObject.parseObject(result);
            JSONArray cacheArray = resJson.getJSONArray("dataList");
            acsCacheList = JSONArray.parseArray(cacheArray.toJSONString(), VideoEquStatus.class);
        }
    }

    public static List<VideoEquStatus> getCacheList() {
        if (acsCacheList == null) {
            String result = HttpRequestUtil.sendPost(equStatusRequUrl, "");
            JSONObject resJson = JSONObject.parseObject(result);
            JSONArray cacheArray = resJson.getJSONArray("datalist");
            acsCacheList = JSONArray.parseArray(cacheArray.toJSONString(), VideoEquStatus.class);
        }
        return acsCacheList;
    }

    public static void main(String[] args) {
        String param = "{ \"page\": { \"nowPage\": 1, \"pageSize\": 1 } }";
        String result = HttpRequestUtil.sendPost("http://192.168.111.70:8080/api/sf/userinfo/open/sync/page", param);
        System.out.println(result);
    }

}
