package com.air.security.sodb.data.receive.service.dh;

import org.springframework.stereotype.Service;

import com.air.security.sodb.data.core.exception.SystemBaseException;
import com.air.security.sodb.data.receive.service.AbstractSimpleMessageRecvService;
import com.air.security.sodb.data.receive.service.dh.common.SrvcHelper;
import com.air.security.sodb.data.core.base.Meta;
import com.alibaba.fastjson.JSONArray;
import com.alibaba.fastjson.JSONObject;

/**
 * 大华刷卡信息
 *
 * <AUTHOR>
 */
@Service("DahuaRecordCardRecvServiceImpl")
public class DahuaRecordCardRecvServiceImplAbstract extends AbstractSimpleMessageRecvService {

    @Override
    public Object executeGetData(Meta meta, String messageBody) throws SystemBaseException {
        // TODO Auto-generated method stub

        JSONObject obj = JSONObject.parseObject(messageBody);
        SrvcHelper.validHandle(obj);

        JSONArray array = obj.getJSONObject("data").getJSONArray("pageData");


        JSONArray arrayData = new JSONArray();
        for (int i = 0; i < array.size(); i++) {
            JSONObject item = array.getJSONObject(i);

            JSONObject alarmObj = getAlarmObj(item);
            // 是告警类型
            arrayData.add(alarmObj);

        }
        return arrayData;
    }

    /**
     * 门禁刷卡记录
     *
     * @param item
     * @return
     */
    private static JSONObject getAlarmObj(JSONObject item) {
        JSONObject obj = new JSONObject();
        obj.put("id", item.get("id"));
        // 卡号
        obj.put("cardNumber", item.get("cardNumber"));
        // 卡状态
        obj.put("cardStatus", item.get("cardStatus"));
        // 卡类型
        obj.put("cardType", item.get("cardType"));
        // 通道编码
        obj.put("channelCode", item.get("channelCode"));
        // 通道名称
        obj.put("channelName", item.get("channelName"));
        // 部门
        obj.put("deptName", item.get("deptName"));
        // 设备编码
        obj.put("deviceCode", item.get("deviceCode"));
        // 设备名称
        obj.put("deviceName", item.get("deviceName"));
        // 进出门  1：进 2：出 3：进/出
        obj.put("enterOrExit", item.get("enterOrExit"));
        // 开门结果 1：成功 0：失败
        obj.put("openResult", item.get("openResult"));
        // 开门类型
        obj.put("openType", item.get("openType"));
        // 证件号码
        obj.put("paperNumber", item.get("paperNumber"));
        // 人员编号
        obj.put("personCode", item.get("personCode"));
        // 人员ID
        obj.put("personId", item.get("personId"));
        // 人员名称
        obj.put("personName", item.get("personName"));
        // 刷卡时间
        obj.put("swingTime", item.get("swingTime"));
        return obj;
    }

}
