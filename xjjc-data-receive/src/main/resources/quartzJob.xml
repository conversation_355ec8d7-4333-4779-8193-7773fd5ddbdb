<?xml version="1.0" encoding="UTF-8"?>
<!--定时任务配置文件： -->
<quartz>
    <job>
        <job-detail>
            <name>videoStatusMonitor-Job</name>
            <group>videoStatusMonitorJobGroup</group>
            <description>流媒体服务状态通知采集任务Job</description>
            <job-class>com.air.security.sodb.data.receive.job.VideoStatusMonitorJob</job-class>
        </job-detail>
        <trigger>
            <!-- JOB开关，on:开启 off:关闭 -->
            <switch>off</switch>
            <name>videoStatusMonitor-Job-trigger</name>
            <group>videoStatusMonitorJobGroup</group>
            <cron-expression>0/60 * * * * ?</cron-expression>
        </trigger>
    </job>

    <job>
        <job-detail>
            <name>dhAlarmRecord-Job</name>
            <group>dhAlarmRecordGroup</group>
            <description>大华门禁报警数据采集任务Job</description>
            <job-class>com.air.security.sodb.data.receive.job.DhAlarmRestReceiveSchedulerJob</job-class>
        </job-detail>
        <trigger>
            <!-- JOB开关，on:开启 off:关闭 -->
            <switch>off</switch>
            <name>dhAlarmRecord-Job-trigger</name>
            <group>dhAlarmRecordGroup</group>
            <!-- 每30秒执行一次 -->
            <cron-expression>*/30 * * * * ?</cron-expression>
        </trigger>
    </job>

    <job>
        <job-detail>
            <name>dhChannleStatus-Job</name>
            <group>dhChannleStatusGroup</group>
            <description>大华门禁状态采集任务Job</description>
            <job-class>com.air.security.sodb.data.receive.job.DhChannelRestReceiveSchedulerJob</job-class>
        </job-detail>
        <trigger>
            <switch>off</switch>
            <name>dhChannleStatus-Job-trigger</name>
            <group>dhChannleStatusGroup</group>
            <!-- 每分钟执行一次 -->
            <cron-expression>0 */1 * * * ?</cron-expression>
        </trigger>
    </job>

    <job>
        <job-detail>
            <name>dhAcsEqu-Job</name>
            <group>dhAcsEquGroup</group>
            <description>大华门禁设备采集任务Job</description>
            <job-class>com.air.security.sodb.data.receive.job.DhAcsEquRestReceiveSchedulerJob</job-class>
        </job-detail>
        <trigger>
            <switch>off</switch>
            <name>dhAcsEqu-Job-trigger</name>
            <group>dhAcsEquGroup</group>
            <!-- 每4小时执行一次 -->
            <cron-expression>0 0 */4 * * ?</cron-expression>
        </trigger>
    </job>

    <job>
        <job-detail>
            <name>dhAcsRecord-Job</name>
            <group>dhAcsRecordGroup</group>
            <description>大华门禁刷卡记录采集任务Job</description>
            <job-class>com.air.security.sodb.data.receive.job.DhRecordCardRestReceiveSchedulerJob</job-class>
        </job-detail>
        <trigger>
            <switch>off</switch>
            <name>dhAcsRecord-Job-trigger</name>
            <group>dhAcsRecordGroup</group>
            <!-- 每分钟执行一次 -->
            <cron-expression>0 0/1 * * * ?</cron-expression>
        </trigger>
    </job>

    <job>
        <job-detail>
            <name>dhVideoAlarm-Job</name>
            <group>dhVideoAlarmGroup</group>
            <description>大华摄像机报警采集任务Job</description>
            <job-class>com.air.security.sodb.data.receive.job.DhVideoAlarmRestReceiveSchedulerJob</job-class>
        </job-detail>
        <trigger>
            <switch>off</switch>
            <name>dhVideoAlarm-Job-trigger</name>
            <group>dhVideoAlarmGroup</group>
            <!-- 每30秒执行一次 -->
            <cron-expression>*/30 * * * * ?</cron-expression>
        </trigger>
    </job>

</quartz>