#çº¿ç¨æ± å¤§å°
threadpool.count=20
#åºç¨å³é­çæ§ç«¯å£
bjjc.data.receive.stop.port=20007
#kafkaConsumer kafkaæ¶è´¹èéç½®æä»¶è·¯å¾
#kafka.consumer.config.path=D:\\xjjcDataRecv\\config\\kafkaConsumer.properties
kafka.consumer.config.path=/server/dtsServer/xjjcDataRecv/config/kafkaConsumer.properties
#kafkaProducer kafkaæ¶è´¹èéç½®æä»¶è·¯å¾
#kafka.producer.config.path=D:\\xjjcDataRecv\\config\\kafkaProducer.properties
kafka.producer.config.path=/server/dtsServer/xjjcDataRecv/config/kafkaProducer.properties
#åå¸æå¡çæ°æ®ç±»åæ±æ»
release.msg.type=SECURITY_EVENT,BLACK_LIST,SAFE_STATE,TOOL_MATTER,BIT_TOOL,LIQUID,FIRST_CARD_DATA_UE
#release.msg.type=ALARM
#è®°å½æ°æ®ä¼ è¾æ¥å¿çtopic
kafka.msg.transfer.log.topic=msgTranLog
#æ¥è­¦æ¶æ¯åå¸æå¡ä¸»é¢
kafka.alarm.msg.output.topic=oSodbAmsAlarm
#å®å¨äºä»¶æ¶æ¯åå¸æå¡ä¸»é¢
kafka.event.msg.output.topic=oSodbEmsEvent
#å¸æ§äººåæ¶æ¯åå¸æå¡ä¸»é¢
kafka.black.msg.output.topic=oSodbSiaBlack
#å®å¨æå¿æ¶æ¯åå¸æå¡ä¸»é¢
kafka.state.msg.output.topic=oSodbSiaState
#å®¡æ¹ç»ææ¶æ¯åå¸æå¡ä¸»é¢
kafka.approval.msg.output.topic=oSodbSomApproval
#sodbç³»ç»æ¥å¿ä¸»é¢
kafka.sodb.sys.log.output.topic=oSodbSysLog
#éè¿kafka-sdkåå¸èªç­æ°æ®çä¸»é¢
kafka.flight.msg.input.topics=relsTopic1
#è·åesb tokençurl
esb.token.url=http://esb.bdia.com.cn:8080/jcesb/autapi/token/getToken
#esbåæ­¥æå¡ä»£çurl
esb.proxy.url=http://esb.bdia.com.cn:7080/proxy/http/rest
#esbæå¡æµéä»£çæ¥å£
esb.smes.proxy.url=http://esb.bdia.com.cn:7800/proxy/ws/TOMS_FWZXCLSJ/capairport/ws/gaugeWebService
#æºåºå¨è¾¹äº¤éå®æ¶æ¥å µè¯·æ±æ¥å£
hrns.rest.service.code=HOMS_JCZBYDSSXX
#æå¡æ§è¡æµéæ°æ®è¯·æ±æ¥å£
smes.rest.service.code=TOMS_FWZXCLSJ
#é©¾é©¶ååºæ¬ä¿¡æ¯è¯·æ±æå¡ç¼ç 
driver.rest.service.code=RVMS_JSYJBXXSJFW
#é©¾é©¶åè¯ä»¶å¤ç½è¯·æ±æ¥å£
driver.pec.rest.service.code=RVMS_ZJCFSJFW
#è´§æ£åéæ¥å£
cargo.service=false
kafka.cargo.msg.output.topic=relsTopic2
########################  æä»¶æå¡å¨minioåæ°ç¸å³éç½®   ########################
#minioåæ°
endpoint=http://192.168.112.165:9000
access_key=miniominio
secret_key=hayc@123
bucket=datarecv
#æºåºä¸å­ç 
airport.iata=URC
#è´§æ£åéæ¥å£
fti.status.service=false
fti.rabbit.mq.queue=Q_ABPT
fti.rabbit.mq.uri=amqp://admin:123abc,@**********28:5672/cloud.acsmis
#æµåªä½åéæ¥å£
video.status.service=false
video.rabbit.mq.queue=sodb.alarm
#video.rabbit.mq.uri=**************************************
video.rabbit.mq.uri=**************************************
video.rabiit.mq.exchange=cctv.notify
video.rabiit.mq.routeKey=cctv.notify.key
# å¤§åè·åtokenå°å
dahua.getToken=http://**********/WPMS/login
# å¤§åç»å½å
dahua.loginName=system
# å¤§åè·åtokenå°åå¯ç 
dahua.passWord=admin123
# å¤§åæå¡å°å
rest.dh.host=http://**********
# å¤§åè®¾å¤æå¡URI
rest.dh.getEqu.uri=/CardSolution/card/accessControl/device/bycondition/combined
# å¤§åééæå¡URI
rest.dh.getChannel.uri=/CardSolution/card/accessControl/channel/bycondition/combined
# å¤§åå·å¡æå¡URI
rest.dh.getRecordCard.uri=/CardSolution/card/accessControl/swingCardRecord/bycondition/combined
# å¤§åæ¥è­¦æ¥å¿-ç³»ç»æ¥è­¦æ¥å¿æå¡URL
rest.dh.alarmLog.url=http://**********/admin/services/AdminWebService?wsdl
# å¤§åè§é¢æ¥å£token
dh.video.token=ce3a03dd49752ba29604dc4667fd9c38
# å¤§åæ¥è¯¢æå¡æ¯é¡µæ¡æ°
rest.dh.pageSize=20
# å¤§åè®¾å¤ä¿¡æ¯æå¡code
dh.device.service.code=DahuaDeviceInfo
# å¤§åééç¶ææå¡code
dh.channelstatus.service.code=DahuaChannelEquStatus
# å¤§åæ¥è­¦æ°æ®æå¡code
dh.alarmrecord.service.code=DahuaAlarmRecord
# å¤§åå·å¡æ°æ®æå¡code
dh.recordcard.service.code=DahuaRecordCard
# å¤§åç³»ç»æ¥è­¦æ¥å¿æå¡code
dh.video.alarm.service.code=DahuaVideoAlarmRecord
#sodbé¨ç¦èµæºç¶æè¯·æ±æ¥å£
sodb.ec02.equ.status.req.url=http://172.28.6.125/api/bs/equ/info/open/getEquInfoByCode/EC02
#æµåªä½èµæºç¶ææ°æ®æå¡
video.equ.status.service.code=VideoEquStatus
video.equ.status.online.service.code=VideoEquStatusOnline
video.equ.status.offline.service.code=VideoEquStatusOffline
#æµåªä½ç¼å­åè¡¨æ°æ®éå¤§å°
video.equ.status.cache.size=10000
#sodbæµåªä½èµæºç¶æè¯·æ±æ¥å£
sodb.video.equ.status.req.url=http://172.28.6.125/api/bs/equ/info/open/getEquInfoByCode/EC01
#å´çhttpserveréç½®
wall.http.service=true
wall.http.server.port=9199
wall.http.timeout=0
#æ¥è­¦æ°æ®è¯·æ±é´é(åä½ï¼s)
time.interval=-60
#æå®¢å®æ£çå¬éç½®
#activeMqçå¬url
lkaj.service=true
lkaj.active.mq.url=failover://tcp://172.17.31.200:61616
lkaj.active.mq.username=admin
lkaj.active.mq.pwd=admin
lkaj.active.mq.ajQueue=LKXX
lkaj.active.mq.ajTopic=LKXX
