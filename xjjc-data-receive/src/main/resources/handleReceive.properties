#æ¶æ¯æ ååå¤çServiceéç½®
#è´§è¿å®æ£ç³»ç»-è¿ååºæ¬ä¿¡æ¯
WAY_BILL_LOG=MessageRecvFormatServiceImpl,cContainerCargo,WAY_BILL_LOG,CARGO
#è´§è¿å®æ£ç³»ç»-è¿åå¼åä¿¡æ¯
OPEN_LOG=CargoOpenMessageRecvFormatServiceImpl,cContainerCargo,OPEN_LOG,CARGO
#è´§è¿å®æ£ç³»ç»-è¿åå®æ£ä¿¡æ¯
SECURITY_LOG=MessageRecvFormatServiceImpl,cContainerCargo,SECURITY_LOG,CARGO
#è´§è¿å®æ£ç³»ç»-å®æ£ééçå¼æ¾ä¿¡æ¯
CHECKSTATUS_EQU=MessageRecvFormatServiceImpl,cContainerCargo,CHECKSTATUS_EQU,CARGO
# å¤§åé¨ç¦è®¾å¤ä¿¡æ¯
DahuaDeviceInfo=DahuaDeviceInfoRecvServiceImpl,cC<PERSON>rEqu,ACS_EQUINFO_UE,EQU
# å¤§åé¨ç¦è®¾å¤ç¶æ
DahuaChannelEquStatus=DahuaChannelStatusRecvServiceImpl,cContainerEqu,ACS_EQU_STATUS_UE,EQU_STATUS
# å¤§åæ¥è­¦æ°æ®
DahuaAlarmRecord=DahuaRecordCardRecvServiceImpl,cContainerAlarm,ACS_ALARM,ALARM
# å¤§åå·å¡è®°å½
DahuaRecordCard=DahuaRecordCardRecvServiceImpl,cContainerRecord,ACS_RECORD_CARD,RECORD_CARD
# å¤§åè§é¢æ¥è­¦æ°æ®
DahuaVideoAlarmRecord=SvmsDhAlarmRecvFormatServiceImpl,cContainerAlarm,SVMSDH_ALARM,ALARM
#å´çç³»ç»-å´çèµæº
DA=MessageRecvFormatServiceImpl,cContainerEqu,WALL_EQU,EQU
#å´çç³»ç»-å´çæ¥è­¦
ALARM=MessageRecvFormatServiceImpl,cContainerAlarm,WALL_ALARM,ALARM
#ç¦»æ¸¯ç³»ç»-èªç­å¨ææ°æ®
Versionhbdt=MessageRecvFormatServiceImpl,cContainerFlight,FLIGHT_DYN,FLIGHT_DATA
#æµåªä½-æåæºç¶ææ°æ®
VideoEquStatus=VideoEquStatusRecvFormatServiceImpl,cContainerEqu,SVMS_EQU_STATUS_UE,EQU_STATUS
VideoEquStatusOnline=VideoStatusOnlineRecvFormatServiceImpl,cContainerEqu,SVMS_EQU_STATUS_UE,EQU_STATUS
VideoEquStatusOffline=VideoStatusOfflineRecvFormatServiceImpl,cContainerEqu,SVMS_EQU_STATUS_UE,EQU_STATUS
#ææ£æ°æ®
PRSC=ScimsMessageRecvFormatServiceImpl,cContainerPassenger,PSGR_SECURITY,PSGR_DATA
check=ScimsImgMessageRecvFormatServiceImpl,cContainerPassenger,PRSCJSON_SECURITY,PSGR_DATA
bag=ScimsImgMessageRecvFormatServiceImpl,cContainerPassenger,PSGR_OPEN_INFO,PSGR_DATA
