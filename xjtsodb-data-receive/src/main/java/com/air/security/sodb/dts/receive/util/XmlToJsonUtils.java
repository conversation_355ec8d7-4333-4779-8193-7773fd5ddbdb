package com.air.security.sodb.dts.receive.util;

import com.alibaba.fastjson.JSONObject;
import de.odysseus.staxon.json.JsonXMLConfig;
import de.odysseus.staxon.json.JsonXMLConfigBuilder;
import de.odysseus.staxon.json.JsonXMLInputFactory;
import de.odysseus.staxon.json.JsonXMLOutputFactory;
import de.odysseus.staxon.xml.util.PrettyXMLEventWriter;

import javax.xml.stream.XMLEventReader;
import javax.xml.stream.XMLEventWriter;
import javax.xml.stream.XMLInputFactory;
import javax.xml.stream.XMLOutputFactory;
import java.io.IOException;
import java.io.StringReader;
import java.io.StringWriter;

/**
 * json and xml converter
 * <AUTHOR>
 * @see https://github.com/beckchr/staxon
 * @see https://github.com/beckchr/staxon/wiki
 *
 */
public class XmlToJsonUtils {

    /**
     * json string convert to xml string
     */
    public static String json2xml(String json){
        StringReader input = new StringReader(json);
        StringWriter output = new StringWriter();
        JsonXMLConfig config = new JsonXMLConfigBuilder().multiplePI(false).repairingNamespaces(false).build();
        try {
            XMLEventReader reader = new JsonXMLInputFactory(config).createXMLEventReader(input);
            XMLEventWriter writer = XMLOutputFactory.newInstance().createXMLEventWriter(output);
            writer = new PrettyXMLEventWriter(writer);
            writer.add(reader);
            reader.close();
            writer.close();
        } catch( Exception e){
            e.printStackTrace();
        } finally {
            try {
                output.close();
                input.close();
            } catch (IOException e) {
                e.printStackTrace();
            }
        }
        if(output.toString().length()>=38){//remove <?xml version="1.0" encoding="UTF-8"?>
            return output.toString().substring(39);
        }
        return output.toString();
    }

    /**
     * xml string convert to json string
     */
    public static String xml2json(String xml){
        StringReader input = new StringReader(xml);
        StringWriter output = new StringWriter();
        JsonXMLConfig config = new JsonXMLConfigBuilder().autoArray(true).autoPrimitive(true).prettyPrint(true).build();
        try {
            XMLEventReader reader = XMLInputFactory.newInstance().createXMLEventReader(input);
            XMLEventWriter writer = new JsonXMLOutputFactory(config).createXMLEventWriter(output);
            writer.add(reader);
            reader.close();
            writer.close();
        } catch( Exception e){
            e.printStackTrace();
        } finally {
            try {
                output.close();
                input.close();
            } catch (IOException e) {
                e.printStackTrace();
            }
        }
        return output.toString();
    }

    public static void main(String[] args) {
        String sb = "<Envelope\n" +
                "    xmlns:xsi=\"http://www.w3.org/2001/XMLSchema-instance\"\n" +
                "    xmlns:xsd=\"http://www.w3.org/2001/XMLSchema\">\n" +
                "    <Header>\n" +
                "        <MessageName>SecurityResult</MessageName>\n" +
                "        <MessageTime>2018-06-07T16:14:12.4899216+08:00</MessageTime>\n" +
                "    </Header>\n" +
                "    <Body>\n" +
                "        <Record>\n" +
                "            <BindingData>00000001</BindingData>\n" +
                "            <Result>R</Result>\n" +
                "            <ChannelId>C01</ChannelId>\n" +
                "            <OptId>001</OptId>\n" +
                "            <CheckTime>2018-06-07T16:14:12.0009246+08:00</CheckTime>\n" +
                "            <ImageGuid>4035-D2E5-3199-30BF-20120129-00002473</ImageGuid>\n" +
                "            <ImageUrl>http://192.168.1.10:8080/4035-D2E5-3199-30BF-20120129-00002473.jpg</ImageUrl>\n" +
                "            <TaggedImageUrl>http://192.168.1.10:8080/4035-D2E5-3199-30BF-20120129-00002473_T.jpg</TaggedImageUrl>\n" +
                "            <CustomsResult>R</CustomsResult>\n" +
                "        </Record>\n" +
                "    </Body>\n" +
                "</Envelope>";
        String s = xml2json(sb);
        JSONObject parse = (JSONObject) JSONObject.parse(s);

        JSONObject jsonObject = parse.getJSONObject("Envelope").getJSONObject("Body").getJSONObject("Record");
        //JSONObject jsonObject = parse.getJSONObject("MSG").getJSONObject("DATAINFO");
        System.out.println(jsonObject.toString());


        System.out.println(s);

    }


}

