package com.air.security.sodb.dts.receive.service.basic;

import com.air.security.sodb.data.core.base.Meta;
import com.air.security.sodb.data.core.constant.MsgIdConstant;
import com.air.security.sodb.data.core.exception.SystemBaseException;
import com.air.security.sodb.data.core.util.HaLog;
import com.air.security.sodb.dts.receive.service.AbstractMessageRecvService;
import com.alibaba.fastjson.JSONArray;
import com.alibaba.fastjson.JSONObject;
import org.apache.commons.lang3.StringUtils;
import org.slf4j.Logger;
import org.slf4j.LoggerFactory;
import org.springframework.stereotype.Service;

/**
 * @Description:xml信息处理
 * <AUTHOR> 异常事件
 **/
@Service("ArdlMessageRecvFormatServiceImpl")
public class ArdlMessageRecvFormatServiceImpl extends AbstractMessageRecvService {
    private static final Logger log = LoggerFactory.getLogger(ArdlMessageRecvFormatServiceImpl.class);

    @Override
    public JSONObject execute(Meta meta, String messageBody) throws SystemBaseException {
        HaLog.info(log, MsgIdConstant.MS_INF_0001, meta.getEventType());

        // 响应json消息
        JSONObject outputMsg = new JSONObject();

        JSONObject json = JSONObject.parseObject(messageBody);
        Object object = json.getJSONObject("MSG").get("ABRN");
        JSONArray josn = new JSONArray();
        if (object instanceof JSONArray) {
            JSONArray inputJsonArr = (JSONArray) object;
            for (int i = 0; i < inputJsonArr.size(); i++) {
                JSONObject childInputJson = (JSONObject) inputJsonArr.get(i);
                if (StringUtils.isNotBlank(childInputJson.getString("ARTY"))){
                    if ("E".equals(childInputJson.getString("ARTY"))){
                        childInputJson.put("classificationName","外部异常原因");
                    }else {
                        childInputJson.put("classificationName","内部异常原因");
                    }
                }
                josn.add(childInputJson);
            }
        }else if (object instanceof JSONObject) {
            JSONObject childInputJson = (JSONObject) object;
            if (StringUtils.isNotBlank(childInputJson.getString("ARTY"))){
                if ("E".equals(childInputJson.getString("ARTY"))){
                    childInputJson.put("classificationName","外部异常原因");
                }else {
                    childInputJson.put("classificationName","内部异常原因");
                }
            }
            josn.add(childInputJson);
        }
        JSONObject batch = new JSONObject();
        batch.put("batch",josn);
        // 响应消息头
        outputMsg.put("meta", meta);

        // 响应消息体
        outputMsg.put("body", batch);

        // 转换为JSON格式，并发送消息到指定主题
        super.putSendMessage(outputMsg);

        HaLog.info(log, MsgIdConstant.MS_INF_0002, meta.getEventType());

        return outputMsg;
    }
}
