package com.air.security.sodb.dts.srvc.domain.service.impl;

import com.air.security.sodb.data.core.base.ReceiveErrorType;
import com.air.security.sodb.data.core.constant.CodeConstant;
import com.air.security.sodb.data.core.exception.Be;
import com.air.security.sodb.data.core.service.ServiceOutDTO;
import com.air.security.sodb.data.core.util.*;
import com.air.security.sodb.dts.srvc.domain.entity.DemoEntity;
import com.air.security.sodb.dts.srvc.domain.mapper.DemoMapper;
import com.air.security.sodb.dts.srvc.domain.service.DemoService;
import com.air.security.sodb.dts.srvc.restsrvc.platform.demo.dto.DemoDto;
import com.air.security.sodb.dts.srvc.restsrvc.platform.demo.form.DemoForm;
import org.apache.commons.collections4.CollectionUtils;
import org.apache.commons.lang3.StringUtils;
import org.apache.ibatis.session.SqlSession;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.beans.factory.annotation.Qualifier;
import org.springframework.stereotype.Service;

import java.util.Date;
import java.util.List;
import java.util.Map;

/**
 * <AUTHOR>
 */
@Service("demoService")
public class DemoServiceImpl implements DemoService {

    @Autowired
    private DemoMapper mapper;

    @Autowired
    @Qualifier("sqlSession")
    private SqlSession sqlSession;

    /**
     * 新增
     *
     * @param demo
     * @return
     */
    @Override
    public ServiceOutDTO<Boolean> add(DemoForm demo) {
        // TODO Auto-generated method stub
        ValidateUtil.notNull(demo, "不能为空");
        ValidateUtil.notBlank(demo.getName(), "姓名不能为空");
        ValidateUtil.notBlank(demo.getNickName(), "昵称不能为空");
        ValidateUtil.notBlank(demo.getGender(), "性别信息不能为空");
        try {
            DemoEntity entity = new DemoEntity();
            entity.setBirth(demo.getBirth());
            entity.setCreateId(demo.getCreateId());
            entity.setGender(demo.getGender());
            entity.setName(demo.getName());
            entity.setPassword(demo.getPassword());
            entity.setNickName(demo.getNickName());
            entity.setCreateTime(new Date());
            entity.setIsDelete(CodeConstant.IS_DELETE_NO.toString());
            entity.setUuid(UuidUtil.getUuid36());

            int count = mapper.insertSelective(entity);

            ServiceOutDTO<Boolean> result = new ServiceOutDTO<>();
            result.setResut(count > 0);
            return result;
        } catch (Exception e) {
            throw new Be("保存数据失败", e);
        }
    }

    /**
     * 修改
     *
     * @param demo
     * @return
     */
    @Override
    public ServiceOutDTO<Boolean> update(DemoForm demo) {
        // TODO Auto-generated method stub
        ValidateUtil.notNull(demo, "不能为空");
        ValidateUtil.notBlank(demo.getUuid(), "唯一标识不能为空");

        try {
            DemoEntity record = new DemoEntity();
            record.setBirth(demo.getBirth());
            record.setCreateId(demo.getCreateId());
            record.setGender(demo.getGender());
            record.setName(demo.getName());
            record.setPassword(demo.getPassword());
            record.setNickName(demo.getNickName());
            record.setIsDelete(demo.getIsDelete());
            record.setUuid(demo.getUuid());
            int count = mapper.updateByPrimaryKeySelective(record);

            ServiceOutDTO<Boolean> result = new ServiceOutDTO<>();
            result.setResut(count > 0);
            return result;
        } catch (Exception e) {
            throw new Be("修改信息失败", e);
        }
    }

    @Override
    public ServiceOutDTO<Boolean> delete(String uuid) {
        // TODO Auto-generated method stub
        DemoEntity record = new DemoEntity();
        record.setUuid(uuid);
        record.setIsDelete(CodeConstant.IS_DELETE_YES.toString());
        record.setDeleteTime(new Date());

        try {

            int count = mapper.updateByPrimaryKeySelective(record);

            ServiceOutDTO<Boolean> result = new ServiceOutDTO<>();
            result.setResut(count > 0);
            return result;
        } catch (Exception e) {
            throw new Be("信息删除失败", e);
        }
    }

    @Override
    public ServiceOutDTO<DemoDto> getById(String uuid) {
        // TODO Auto-generated method stub
        ValidateUtil.notBlank(uuid, "唯一标识不能为空");

        Map<String, Object> map = MapUtil.getHashMap(1);
        map.put("uuid", uuid);

        try {
            List<DemoDto> list = sqlSession.selectList("demo.getById", map);
            ServiceOutDTO<DemoDto> dto = new ServiceOutDTO<>();
            if (CollectionUtils.isEmpty(list)) {
                dto.setReturnCd(ReceiveErrorType.BUSINESS_DATA_NO_EXIST);
                return dto;
            }

            dto.setReturnCd(ReceiveErrorType.SUCCESS);
            dto.setResut(list.get(0));
            return dto;
        } catch (Exception e) {
            throw new Be("查询信息失败", e);
        }

    }

    @Override
    public ServiceOutDTO<List<DemoDto>> getList(DemoForm demo) {
        // TODO Auto-generated method stub
        Map<String, Object> map = MapUtil.getHashMap(2);
        if (StringUtils.isNotBlank(demo.getName())) {
            map.put("name", StringUtil.getSqlLike(demo.getName()));
        }
        if (StringUtils.isNotBlank(demo.getNickName())) {
            map.put("nickName", StringUtil.getSqlLike(demo.getNickName()));
        }

        try {
            List<DemoDto> list = sqlSession.selectList("demo.getList", map);
            ServiceOutDTO<List<DemoDto>> dto = new ServiceOutDTO<>();
            if (CollectionUtils.isEmpty(list)) {
                dto.setReturnCd(ReceiveErrorType.BUSINESS_DATA_NO_EXIST);
                return dto;
            }

            dto.setReturnCd(ReceiveErrorType.SUCCESS);
            dto.setResut(list);
            return dto;
        } catch (Exception e) {
            throw new Be("查询信息失败", e);
        }
    }

}
