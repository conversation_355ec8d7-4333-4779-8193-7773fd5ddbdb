package com.air.security.sodb.dts.receive.util;

public class YsEquInfo {
	private String equCode;
	private String equName;
	private String equTypeCode;
	private String regionId;
	private String timeStateId;

	public String getTimeStateId() {
		return timeStateId;
	}

	public void setTimeStateId(String timeStateId) {
		this.timeStateId = timeStateId;
	}

	public String getEquCode() {
		return equCode;
	}

	public void setEquCode(String equCode) {
		this.equCode = equCode;
	}

	public String getEquName() {
		return equName;
	}

	public void setEquName(String equName) {
		this.equName = equName;
	}

	public String getEquTypeCode() {
		return equTypeCode;
	}

	public void setEquTypeCode(String equTypeCode) {
		this.equTypeCode = equTypeCode;
	}

	public String getRegionId() {
		return regionId;
	}

	public void setRegionId(String regionId) {
		this.regionId = regionId;
	}

	@Override
	public int hashCode() {
		final int prime = 31;
		int result = 1;
		result = prime * result + ((equCode == null) ? 0 : equCode.hashCode());
		return result;
	}
	@Override
	public boolean equals(Object obj) {
		if (this == obj) {
			return true;
		}
		if (obj == null) {
			return false;
		}
		if (getClass() != obj.getClass()) {
			return false;
		}
		YsEquInfo other = (YsEquInfo) obj;
		if (equCode == null) {
			if (other.equCode != null) {
				return false;
			}
		} else if (!equCode.equals(other.equCode)) {
			return false;
		}
		return true;
	}
	
}
