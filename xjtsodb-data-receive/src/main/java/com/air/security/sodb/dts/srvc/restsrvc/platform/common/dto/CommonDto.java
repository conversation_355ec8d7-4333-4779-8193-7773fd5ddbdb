package com.air.security.sodb.dts.srvc.restsrvc.platform.common.dto;

public class CommonDto {
    private String msgType;
    private String eventType;
    private String sendtTopic;

    public String getSendtTopic() {
        return sendtTopic;
    }

    public void setSendtTopic(String sendtTopic) {
        this.sendtTopic = sendtTopic;
    }

    public String getMsgType() {
        return msgType;
    }

    public void setMsgType(String msgType) {
        this.msgType = msgType;
    }

    public String getEventType() {
        return eventType;
    }

    public void setEventType(String eventType) {
        this.eventType = eventType;
    }

    @Override
    public String toString() {
        return "CommonDto{" +
                "msgType='" + msgType + '\'' +
                ", eventType='" + eventType + '\'' +
                ", sendtTopic='" + sendtTopic + '\'' +
                '}';
    }
}

