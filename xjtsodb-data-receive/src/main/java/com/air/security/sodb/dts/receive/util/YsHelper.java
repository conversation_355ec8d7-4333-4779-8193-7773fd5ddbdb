package com.air.security.sodb.dts.receive.util;

import com.air.security.sodb.data.core.util.HaLog;
import com.air.security.sodb.data.core.util.HttpRequestUtil;
import com.air.security.sodb.data.core.util.PropertyUtil;
import com.alibaba.fastjson.JSONObject;
import org.slf4j.Logger;
import org.slf4j.LoggerFactory;
import org.springframework.util.Base64Utils;


/**
 * <AUTHOR>
 *
 */
public class YsHelper {
	
	private static Logger logger = LoggerFactory.getLogger(YsHelper.class);
	
	private static final String LOGIN_URL = PropertyUtil.getProperty("yushi.login.url");
	private static final String LOGIN_USER = PropertyUtil.getProperty("yushi.login.username");
	private static final String LOGIN_PWD = PropertyUtil.getProperty("yushi.login.password");
	
	/**
	 * 获取宇视token
	 * @return
	 */
	public static String getAccessToken() {
		String result = HttpRequestUtil.sendPost(LOGIN_URL);
		HaLog.infoJson(logger, "首次登录结果：" + result);
		JSONObject json = JSONObject.parseObject(result);
		String accessCode = json.getString("AccessCode");
		String base64UserName = Base64Utils.encodeToString(LOGIN_USER.getBytes());
		String loginSignature = MD5Util.string2MD5(base64UserName + accessCode + MD5Util.string2MD5(LOGIN_PWD));
		JSONObject params = new JSONObject();
		params.put("UserName", LOGIN_USER);
		params.put("AccessCode", accessCode);
		params.put("LoginSignature", loginSignature);
		HaLog.infoJson(logger, "参数：" + params.toJSONString());
		String result1 = HttpRequestUtil.sendPost(LOGIN_URL, params.toJSONString());
		HaLog.infoJson(logger, "第二次登录结果：" + result1);
		JSONObject respObj = JSONObject.parseObject(result1);
		String accessToken = respObj.getString("AccessToken");
		return accessToken;

	}

	public static void main(String[] args) {
		String accessCode = "WTAv0tLOX9ECvUMBsvIr";
		String base64UserName = Base64Utils.encodeToString(LOGIN_USER.getBytes());
		String loginSignature = MD5Util.string2MD5(base64UserName + accessCode + MD5Util.string2MD5(LOGIN_PWD));
		JSONObject params = new JSONObject();
		params.put("UserName", LOGIN_USER);
		params.put("AccessCode", accessCode);
		params.put("LoginSignature", loginSignature);
		System.out.println(params);

	}


}
