#\u589E\u52A0\u8BBE\u5907
EQU_ADD=RecvServiceImpl,cContainerEqu,EQU_ADD,EQU
#\u66F4\u65B0\u8BBE\u5907
EQU_UPDATE=RecvServiceImpl,cContainerEqu,EQU_UPDATE,EQU
#\u5220\u9664\u8BBE\u5907
EQU_DELETE=RecvServiceImpl,cContainerEqu,EQU_DELETE,EQU
#\u6279\u91CF\u540C\u6B65\u8BBE\u5907
EQU_SYNC=SyncRecvServiceImpl,cContainerEquSync,EQU_SYNC,EQU
#\u72B6\u6001\u53D8\u66F4
EQU_STATECHANGE=RecvServiceImpl,cContainerEqu,EQU_STATECHANGE,EQU_STATES
#\u8D44\u6E90\u8054\u52A8\u89C4\u5219\u4FE1\u606F
EQU_RULE=RecvServiceImpl,iSodbRuleSync,EQU_RULE,EQU
#\u62A5\u8B66\u4FE1\u606F
ALARM_TRIGGER=RecvServiceImpl,cContainerAlarm,ALARM_TRIGGER,ALARM
#\u5904\u7F6E\u62A5\u8B66\u4FE1\u606F
ALARM_DISPOSE=RecvServiceImpl,cContainerAlarm,ALARM_DISPOSE,ALARM
EVENT_SECURITY=EventRecvServiceImpl,cContainerEventSecurity,EVENT_SECURITY,EVENT
EVENT_RECORD=RecvServiceImpl,cContainerRecord,EVENT_RECORD,EVENT

CAR_VEHICLE_ADD=RecvServiceImpl,cContainerCarInfo|cContainerEqu|cContainerControlCar,CAR_VEHICLE_ADD|CAR_EQU_ADD|CONTROL_CAR_ADD,CAR_VEHICLE|EQU|CONTROL_CAR
CAR_VEHICLE_UPDATE=RecvServiceImpl,cContainerCarInfo|cContainerEqu|cContainerControlCar,CAR_VEHICLE_UPDATE|CAR_EQU_UPDATE|CONTROL_CAR_UPDATE,CAR_VEHICLE|EQU|CONTROL_CAR
CAR_VEHICLE_DELETE=RecvServiceImpl,cContainerCarInfo|cContainerEqu|cContainerControlCar,CAR_VEHICLE_DELETE|CAR_EQU_DELETE|CONTROL_CAR_DELETE,CAR_VEHICLE|EQU|CONTROL_CAR
CAR_VEHICLE_SYNC=SyncRecvServiceImpl,cContainerCarInfoSync|cContainerEquSync|cContainerControlCarSync,CAR_VEHICLE_SYNC|CAR_EQU_SYNC|CONTROL_CAR_SYNC,CAR_VEHICLE|EQU|CONTROL_CAR



CAR_PARK_INFO=RecvServiceImpl,cContainerCar,CAR_PARK_INFO,CAR
CROSSING_PARK_INFO=RecvServiceImpl,cContainerCrossing,CROSSING_PARK_INFO,CROSSING
CROSSING_STAFF_INFO=RecvServiceImpl,cContainerCrossing,CROSSING_STAFF_INFO,CROSSING
SLOTS_ANALYSE_RESULT=RecvServiceImpl,cContainerAnalyseResult,SLOTS_ANALYSE_RESULT,SLOTS
VIDEO_ANALYSE_RESULT=RecvServiceImpl,cContainerAlarmAnalyseResult|cContainerAnalyseResult|cContainerAnalyseResult|cContainerAnalyseResult,VIDEO_ALARM_DISPOSE|RESULT_QUEUE_NUMBER|RESULT_PERSON_NUMBER|RESULT_FLIGHT_INTELLECT,ALARM|VIDEO_ANALYSE_RESULT|VIDEO_ANALYSE_RESULT|VIDEO_ANALYSE_RESULT|VIDEO_ANALYSE_RESULT


BLACK_WHITE_ADD=RecvServiceImpl,cContainerBlack|cContainerWhite,WHITE_ADD|BLACK_ADD,BLACK_WHITE|BLACK_WHITE
BLACK_WHITE_UPDATE=RecvServiceImpl,cContainerBlack|cContainerWhite,WHITE_UPDATE|BLACK_UPDATE,BLACK_WHITE|BLACK_WHITE
BLACK_WHITE_DELETE=RecvServiceImpl,cContainerBlack|cContainerWhite,WHITE_DELETE|BLACK_DELETE,BLACK_WHITE|BLACK_WHITE
BLACK_WHITE_SYNC=RecvServiceImpl,cContainerBlack|cContainerWhite,WHITE_SYNC|BLACK_SYNC,BLACK_WHITE|BLACK_WHITE


CARD_MAKE_INFO_ADD=RecvServiceImpl,cContainerMakeInfo,CARD_MAKE_INFO_ADD,CARD_MAKE_INFO
CARD_MAKE_INFO_UPDATE=RecvServiceImpl,cContainerMakeInfo,CARD_MAKE_INFO_UPDATE,CARD_MAKE_INFO
CARD_MAKE_INFO_DELETE=RecvServiceImpl,cContainerMakeInfo,CARD_MAKE_INFO_DELETE,CARD_MAKE_INFO
CARD_MAKE_INFO_SYNC=SyncRecvServiceImpl,cContainerMakeInfoSync,CARD_MAKE_INFO_SYNC,CARD_MAKE_INFO


CARD_STAFF_INFO_ADD=CardRecvServiceImpl,cContainerStaffInfo,CARD_STAFF_INFO_ADD,CARD_STAFF_INFO
CARD_STAFF_INFO_UPDATE=CardRecvServiceImpl,cContainerStaffInfo,CARD_STAFF_INFO_UPDATE,CARD_STAFF_INFO
CARD_STAFF_INFO_DELETE=CardRecvServiceImpl,cContainerStaffInfo,CARD_STAFF_INFO_DELETE,CARD_STAFF_INFO
CARD_STAFF_INFO_SYNC=SyncCardRecvServiceImpl,cContainerStaffInfoSync,CARD_STAFF_INFO_SYNC,CARD_STAFF_INFO


CARD_DEDUCTION_CLAUSE_ADD=RecvServiceImpl,cContainerDeductionClause,CARD_DEDUCTION_CLAUSE_ADD,CARD_DEDUCTION_CLAUSE
CARD_DEDUCTION_CLAUSE_UPDATE=RecvServiceImpl,cContainerDeductionClause,CARD_DEDUCTION_CLAUSE_UPDATE,CARD_DEDUCTION_CLAUSE
CARD_DEDUCTION_CLAUSE_DELETE=RecvServiceImpl,cContainerDeductionClause,CARD_DEDUCTION_CLAUSE_DELETE,CARD_DEDUCTION_CLAUSE
CARD_DEDUCTION_CLAUSE_SYNC=SyncRecvServiceImpl,cContainerDeductionClauseSync,CARD_DEDUCTION_CLAUSE_SYNC,CARD_DEDUCTION_CLAUSE


CARD_STAFF_DETAILS_ADD=RecvServiceImpl,cContainerStaffDetails,CARD_STAFF_DETAILS_ADD,CARD_STAFF_DETAILS
CARD_STAFF_DETAILS_UPDATE=RecvServiceImpl,cContainerStaffDetails,CARD_STAFF_DETAILS_UPDATE,CARD_STAFF_DETAILS
CARD_STAFF_DETAILS_DELETE=RecvServiceImpl,cContainerStaffDetails,CARD_STAFF_DETAILS_DELETE,CARD_STAFF_DETAILS
CARD_STAFF_DETAILS_SYNC=SyncRecvServiceImpl,cContainerStaffDetailsSync,CARD_STAFF_DETAILS_SYNC,CARD_STAFF_DETAILS


FACE_SENSITIVE_ADD=RecvServiceImpl,cContainerFaceSensitive,FACE_SENSITIVE_ADD,ALARM
FACE_SENSITIVE_UPDATE=RecvServiceImpl,cContainerFaceSensitive,FACE_SENSITIVE_UPDATE,ALARM
FACE_SENSITIVE_DELETE=RecvServiceImpl,cContainerFaceSensitive,FACE_SENSITIVE_DELETE,ALARM
FACE_SENSITIVE_SYNC=RecvServiceImpl,cContainerFaceSensitive,FACE_SENSITIVE_SYNC,ALARM


VIDEO_QUALITY_DIAGNOSTICS_RESULT=RecvServiceImpl,cContainerDiagnosticsResult,VIDEO_QUALITY_DIAGNOSTICS_RESULT,ALARM
VIDEO_ANALYSE_DISTRIBUTE=RecvServiceImpl,cContainerAnalyseResult,VIDEO_ANALYSE_DISTRIBUTE,VIDEO_ANALYSE
VIDEO_ANALYSE_QUEUELENGTH=RecvServiceImpl,cContainerAnalyseResult,VIDEO_ANALYSE_QUEUELENGTH,VIDEO_ANALYSE

#4.1.1.6\u822A\u73ED\u5171\u4EAB\u53D8\u66F4\u4E8B\u4EF6\uFF08SFLG\uFF09
SFLG = DfltMessageRecvFormatServiceImpl,iSodbFlight,SFLG,FLIGHT_DATA
#4.1.1.7\u822A\u73ED\u822A\u7EBF\u53D8\u66F4\u4E8B\u4EF6\uFF08AIRL\uFF09
AIRL = DfltMessageRecvFormatServiceImpl,iSodbFlight,AIRL,FLIGHT_DATA
#4.1.1.8\u822A\u73ED\u53F7\u53D8\u66F4\u4E8B\u4EF6\uFF08HBTT\uFF09
HBTT = DfltMessageRecvFormatServiceImpl,iSodbFlight,HBTT,FLIGHT_DATA
#4.1.1.9\u822A\u73ED\u524D\u7AD9\u8D77\u98DE\u4E8B\u4EF6\uFF08ONRE\uFF09
ONRE = DfltMessageRecvFormatServiceImpl,iSodbFlight,ONRE,FLIGHT_DATA
#4.1.1.10\u822A\u73ED\u5230\u8FBE\u672C\u7AD9\u4E8B\u4EF6\uFF08ARRE\uFF09
ARRE = DfltMessageRecvFormatServiceImpl,iSodbFlight,ARRE,FLIGHT_DATA
#4.1.1.11\u822A\u73ED\u672C\u7AD9\u8D77\u98DE\u4E8B\u4EF6\uFF08DEPE\uFF09
DEPE = DfltMessageRecvFormatServiceImpl,iSodbFlight,DEPE,FLIGHT_DATA
#4.1.1.13\u822A\u73ED\u5F00\u59CB\u503C\u673A\u4E8B\u4EF6\uFF08CKIE\uFF09
CKIE = DfltMessageRecvFormatServiceImpl,iSodbFlight,CKIE,FLIGHT_DATA
#4.1.1.14\u822A\u73ED\u622A\u6B62\u503C\u673A\u4E8B\u4EF6\uFF08CKOE\uFF09
CKOE = DfltMessageRecvFormatServiceImpl,iSodbFlight,CKOE,FLIGHT_DATA
#4.1.1.15\u822A\u73ED\u5F00\u59CB\u767B\u673A\u4E8B\u4EF6\uFF08BORE\uFF09
BORE = DfltMessageRecvFormatServiceImpl,iSodbFlight,BORE,FLIGHT_DATA
#4.1.1.16\u822A\u73ED\u8FC7\u7AD9\u767B\u673A\u4E8B\u4EF6\uFF08TBRE\uFF09
TBRE = DfltMessageRecvFormatServiceImpl,iSodbFlight,TBRE,FLIGHT_DATA
#4.1.1.17\u822A\u73ED\u50AC\u4FC3\u767B\u673A\u4E8B\u4EF6\uFF08LBDE\uFF09
LBDE = DfltMessageRecvFormatServiceImpl,iSodbFlight,LBDE,FLIGHT_DATA
#4.1.1.18\u822A\u73ED\u7ED3\u675F\u767B\u673A\u4E8B\u4EF6\uFF08POKE\uFF09
POKE = DfltMessageRecvFormatServiceImpl,iSodbFlight,POKE,FLIGHT_DATA
#4.1.1.19\u822A\u73ED\u5EF6\u8BEF\u4E8B\u4EF6\uFF08DLYE\uFF09
DLYE = DfltMessageRecvFormatServiceImpl,iSodbFlight,DLYE,FLIGHT_DATA
#4.1.1.20\u822A\u73ED\u53D6\u6D88\u4E8B\u4EF6\uFF08CANE\uFF09
CANE = DfltMessageRecvFormatServiceImpl,iSodbFlight,CANE,FLIGHT_DATA
#********\u822A\u73ED\u8FD4\u822A\u4E8B\u4EF6\uFF08RTNE\uFF09
RTNE = DfltMessageRecvFormatServiceImpl,iSodbFlight,RTNE,FLIGHT_DATA
#********\u822A\u73ED\u6ED1\u56DE\u4E8B\u4EF6\uFF08BAKE\uFF09
BAKE = DfltMessageRecvFormatServiceImpl,iSodbFlight,BAKE,FLIGHT_DATA
#********\u822A\u73ED\u5907\u964D\u4E8B\u4EF6\uFF08ALTE\uFF09
ALTE = DfltMessageRecvFormatServiceImpl,iSodbFlight,ALTE,FLIGHT_DATA
#********\u822A\u73ED\u66F4\u6362\u98DE\u673A\u4E8B\u4EF6\uFF08CFCE\uFF09
CFCE = DfltMessageRecvFormatServiceImpl,iSodbFlight,CFCE,FLIGHT_DATA
#********\u822A\u73EDVIP\u4E8B\u4EF6\uFF08VIP\uFF09
VIP = DfltMessageRecvFormatServiceImpl,iSodbFlight,VIP,FLIGHT_DATA
#********\u822A\u73ED\u767B\u673A\u95E8\u52A8\u6001\u4FE1\u606F\u66F4\u65B0\u4E8B\u4EF6\uFF08GTLS\uFF09
GTLS = DfltMessageRecvFormatServiceImpl,iSodbFlight,GTLS,FLIGHT_DATA
#********\u822A\u73ED\u884C\u674E\u63D0\u53D6\u8F6C\u76D8\u52A8\u6001\u4FE1\u606F\u66F4\u65B0\u4E8B\u4EF6\uFF08BLLS\uFF09
BLLS = DfltMessageRecvFormatServiceImpl,iSodbFlight,BLLS,FLIGHT_DATA
#********\u822A\u73ED\u503C\u673A\u67DC\u53F0\u52A8\u6001\u4FE1\u606F\u66F4\u65B0\u4E8B\u4EF6\uFF08CKLS\uFF09
CKLS = DfltMessageRecvFormatServiceImpl,iSodbFlight,CKLS,FLIGHT_DATA
#********\u822A\u73ED\u673A\u4F4D\u52A8\u6001\u4FE1\u606F\u66F4\u65B0\u4E8B\u4EF6\uFF08STLS\uFF09
STLS = DfltMessageRecvFormatServiceImpl,iSodbFlight,STLS,FLIGHT_DATA
#********\u822A\u73ED\u8BA1\u5212\u65F6\u95F4\u4E8B\u4EF6\uFF08FPTT\uFF09
FPTT = DfltMessageRecvFormatServiceImpl,iSodbFlight,FPTT,FLIGHT_DATA
#********\u822A\u73ED\u9884\u8BA1\u65F6\u95F4\u4E8B\u4EF6\uFF08FETT\uFF09
FETT = DfltMessageRecvFormatServiceImpl,iSodbFlight,FETT,FLIGHT_DATA
#********\u822A\u73ED\u5B9E\u9645\u65F6\u95F4\u4E8B\u4EF6\uFF08FRTT\uFF09
FRTT = DfltMessageRecvFormatServiceImpl,iSodbFlight,FRTT,FLIGHT_DATA
#********\u822A\u73ED\u8DD1\u9053\u53D8\u66F4\u4E8B\u4EF6\uFF08RWAY\uFF09
RWAY = DfltMessageRecvFormatServiceImpl,iSodbFlight,RWAY,FLIGHT_DATA
#********\u822A\u73ED\u822A\u7AD9\u697C\u53D8\u66F4\u4E8B\u4EF6\uFF08TRML\uFF09
TRML = DfltMessageRecvFormatServiceImpl,iSodbFlight,TRML,FLIGHT_DATA
#4.1.1.37\u822A\u73ED\u5C5E\u6027\u53D8\u66F4\u4E8B\u4EF6\uFF08FATT\uFF09
FATT = DfltMessageRecvFormatServiceImpl,iSodbFlight,FATT,FLIGHT_DATA
#4.1.3.5\u5F02\u5E38\u539F\u56E0\u6574\u8868\u6570\u636E\u540C\u6B65\u4E8B\u4EF6\uFF08ARDL\uFF09
ARDL = ArdlMessageRecvFormatServiceImpl,cContainerFlight,ARDL,FLIGHT_DATA
#4.1.3.52\u767B\u673A\u95E8\u6574\u8868\u6570\u636E\u540C\u6B65\u4E8B\u4EF6\uFF08GTDL\uFF09
GTDL = GtdlMessageRecvFormatServiceImpl,cContainerEqu,GTDL_EQU,EQU
#4.1.3.67\u503C\u673A\u67DC\u53F0\u4FE1\u606F\u6574\u8868\u6570\u636E\u540C\u6B65\u4E8B\u4EF6\uFF08CCDL\uFF09
CCDL = CcdlMessageRecvFormatServiceImpl,cContainerEqu,CCDL_EQU,EQU
#4.1.1.1 \u822A\u73ED\u52A8\u6001\u589E\u52A0\u4E8B\u4EF6\u8BF4\u660E\uFF08DFIE\uFF09
DFIE = DfltMessageRecvFormatServiceImpl,iSodbFlight,DFIE,FLIGHT_DATA
#4.1.1.2 \u52A8\u6001\u5220\u9664\u4E8B\u4EF6\u8BF4\u660E\uFF08DFDE\uFF09
DFDE = DfltMessageRecvFormatServiceImpl,iSodbFlight,DFDE,FLIGHT_DATA
#4.1.1.4 \u52A8\u6001\u822A\u73ED\u6574\u8868\u540C\u6B65\u4E8B\u4EF6\uFF08DFDL\uFF09
DFDL = DfltMessageRecvFormatServiceImpl,iSodbFlight,DFDL,FLIGHT_DATA
#4.1.4.1 \u822A\u73ED\u8BA1\u5212\u6574\u8868\u540C\u6B65\u4E8B\u4EF6\uFF08FPDL\uFF09
FPDL = FpltMessageRecvFormatServiceImpl,iSodbFlight,FPDL,FLIGHT_DATA
#4.1.1.27\u822A\u73ED\u884C\u674E\u6ED1\u69FD\u53E3\u52A8\u6001\u4FE1\u606F\u66F4\u65B0\u4E8B\u4EF6\uFF08CHLS\uFF09
CHLS = DfltMessageRecvFormatServiceImpl,iSodbFlight,CHLS,FLIGHT_DATA
#4.3.1.3\u822A\u73ED\u4FDD\u969C\u4FE1\u606F(HBBZ)
DPUE = DpueMessageRecvFormatServiceImpl,cContainerFlight,DPUE,FLIGHT_DATA

NXTE = DfltMessageRecvFormatServiceImpl,iSodbFlight,NXTE,FLIGHT_DATA

STIE = StndMessageRecvFormatServiceImpl,cContainerEqu,STIE_EQU_ADD,EQU
STUE = StndMessageRecvFormatServiceImpl,cContainerEqu,STUE_EQU_UPDATE,EQU
STDE = StndMessageRecvFormatServiceImpl,cContainerEqu,STDE_EQU_DELETE,EQU
STDL = StndMessageRecvFormatServiceImpl,cContainerEqu,STDL_EQU_SYNC,EQU

EQU_REQUEST=RecvServiceImpl,cSodbEquReq,EQU_REQUEST,EQU
RULE_REQUEST=RecvServiceImpl,cSodbRuleReq,RULE_REQUEST,RULE
CAR_VEHICLE_REQUEST=RecvServiceImpl,cSodbCarInfoReq,CAR_VEHICLE_REQUEST,CAR_VEHICLE
BLACK_WHITE_REQUEST=RecvServiceImpl,cSodbBlackReq,BLACK_WHITE_REQUEST,BLACK_WHITE
CARD_MAKE_INFO_REQUEST=RecvServiceImpl,cSodbMakeInfoReq,CARD_MAKE_INFO_REQUEST,CARD_MAKE_INFO
CARD_STAFF_INFO_REQUEST=RecvServiceImpl,cSodbStaffInfoReq,CARD_STAFF_INFO_REQUEST,CARD_STAFF_INFO
CARD_DEDUCTION_CLAUSE_REQUEST=RecvServiceImpl,cSodbDeductionClauseReq,CARD_DEDUCTION_CLAUSE_REQUEST,CARD_DEDUCTION_CLAUSE
CARD_STAFF_DETAILS_REQUEST=RecvServiceImpl,cSodbStaffDetailsReq,CARD_STAFF_DETAILS_REQUEST,CARD_STAFF_DETAILS
CARD_TOOL_DETAILS_REQUEST=RecvServiceImpl,cSodbCardToolDetailsReq,CARD_TOOL_DETAILS_REQUEST,CARD_TOOL_DETAILS
CARD_DRIVER_DETAILS_REQUEST=RecvServiceImpl,cSodbCardDriverDetailsReq,CARD_DRIVER_DETAILS_REQUEST,CARD_DRIVER_DETAILS
DOOR_EMPLOYEE_INFO_REQUEST=RecvServiceImpl,cSodbDoorEmployeeInfoReq,DOOR_EMPLOYEE_INFO_REQUEST,CARD_DRIVER_DETAILS
DOOR_ROLE_REQUEST=RecvServiceImpl,cSodbDoorRoleReq,DOOR_ROLE_REQUEST,DOOR_ROLE
MOTOR_SUBSCRIPTION=RecvServiceImpl,cSodbMotorAnalyseSubscription,MOTOR_SUBSCRIPTION,MOTOR
MOTOR_ANALYSE_REQUEST=RecvServiceImpl,cSodbMotorReq,MOTOR_ANALYSE_REQUEST,MOTOR

CARD_TOOL_DETAILS_ADD=RecvServiceImpl,cContainerCardToolDetalls,CARD_TOOL_DETAILS_ADD,CARD_TOOL_DETAILS
CARD_TOOL_DETAILS_DELETE=RecvServiceImpl,cContainerCardToolDetalls,CARD_TOOL_DETAILS_DELETE,CARD_TOOL_DETAILS
CARD_TOOL_DETAILS_UPDATE=RecvServiceImpl,cContainerCardToolDetalls,CARD_TOOL_DETAILS_UPDATE,CARD_TOOL_DETAILS
CARD_TOOL_DETAILS_SYNC=RecvServiceImpl,cContainerCardToolDetallsSync,CARD_TOOL_DETAILS_SYNC,CARD_TOOL_DETAILS

CARD_DRIVER_DETAILS_ADD=RecvServiceImpl,cContainerCardDriverDetails,CARD_DRIVER_DETAILS_ADD,CARD_DRIVER_DETAILS
CARD_DRIVER_DETAILS_DELETE=RecvServiceImpl,cContainerCardDriverDetails,CARD_DRIVER_DETAILS_DELETE,CARD_DRIVER_DETAILS
CARD_DRIVER_DETAILS_UPDATE=RecvServiceImpl,cContainerCardDriverDetails,CARD_DRIVER_DETAILS_UPDATE,CARD_DRIVER_DETAILS
CARD_DRIVER_DETAILS_SYNC=RecvServiceImpl,cContainerCardDriverDetails,CARD_DRIVER_DETAILS_SYNC,CARD_DRIVER_DETAILS

DOOR_EMPLOYEE_INFO_ADD=RecvServiceImpl,cContainerDoorEmployeeInfo,DOOR_EMPLOYEE_INFO_ADD,DOOR_EMPLOYEE_INFO
DOOR_EMPLOYEE_INFO_DELETE=RecvServiceImpl,cContainerDoorEmployeeInfo,DOOR_EMPLOYEE_INFO_DELETE,DOOR_EMPLOYEE_INFO
DOOR_EMPLOYEE_INFO_UPDATE=RecvServiceImpl,cContainerDoorEmployeeInfo,DOOR_EMPLOYEE_INFO_UPDATE,DOOR_EMPLOYEE_INFO
DOOR_EMPLOYEE_INFO_SYNC=RecvServiceImpl,cContainerDoorEmployeeInfo,DOOR_EMPLOYEE_INFO_SYNC,DOOR_EMPLOYEE_INFO

DOOR_RULE_ADD=RecvServiceImpl,cContainerDoorRole,DOOR_RULE_ADD,DOOR_ROLE
DOOR_RULE_DELETE=RecvServiceImpl,cContainerDoorRole,DOOR_RULE_DELETE,DOOR_ROLE
DOOR_RULE_UPDATE=RecvServiceImpl,cContainerDoorRole,DOOR_RULE_UPDATE,DOOR_ROLE
DOOR_RULE_SYNC=RecvServiceImpl,cContainerDoorRole,DOOR_RULE_SYNC,DOOR_ROLE

MOTOR_ANALYSE_RESULT=MotorAnalyseResultRecvServiceImpl,cContainerMotorAnalyseSync,MOTOR_ANALYSE_RESULT,MOTOR
MOTOR_ANALYSE=RecvServiceImpl,cContainerMotorAnalyse,MOTOR_ANALYSE,MOTOR





