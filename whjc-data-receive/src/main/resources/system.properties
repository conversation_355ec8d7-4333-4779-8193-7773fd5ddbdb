#\u7EBF\u7A0B\u6C60\u5927\u5C0F
threadpool.count=20

#\u5E94\u7528\u5173\u95ED\u76D1\u63A7\u7AEF\u53E3
whjc.data.receive.stop.port=20011

#tlq\u76D1\u542C
tlq.service=true

#\u63A5\u6536\u822A\u73ED\u4FE1\u606FTlq\u7528\u6237\u540D
tlq.sdk.username=sodb1122
#tlq.sdk.username=TEST123

#\u63A5\u6536\u822A\u73ED\u4FE1\u606FTlq\u5BC6\u7801
tlq.sdk.password=20191122
#tlq.sdk.password=123456

#tlq\u914D\u7F6E\u6587\u4EF6
tlq.sdk.config.path=config\\tlqSdkConfig.properties

#kafkaConsumer kafka\u6D88\u8D39\u8005\u914D\u7F6E\u6587\u4EF6\u8DEF\u5F84
kafka.consumer.config.path=config\\kafkaConsumer.properties
#kafka.consumer.config.path=/server/dtsServer/whjcDataRecv/config/kafkaConsumer.properties

#kafkaProducer kafka\u6D88\u8D39\u8005\u914D\u7F6E\u6587\u4EF6\u8DEF\u5F84
kafka.producer.config.path=config\\kafkaProducer.properties
#kafka.producer.config.path=/server/dtsServer/whjcDataRecv/config/kafkaProducer.properties

format.handle.recv.config.path=D:\\whjcDataRecv\\config\\formatHandleRecv.json

video.data.code.map.path=D:\\whjcDataRecv\\config\\videoDataCodeMap.json

#\u53D1\u5E03\u670D\u52A1\u7684\u6570\u636E\u7C7B\u578B\u6C47\u603B
release.msg.type=SECURITY_EVENT,BLACK_LIST,SAFE_STATE,TOOL_MATTER,BIT_TOOL,LIQUID,FIRST_CARD_DATA_UE
#release.msg.type=ALARM

#\u8BB0\u5F55\u6570\u636E\u4F20\u8F93\u65E5\u5FD7\u7684topic
kafka.msg.transfer.log.topic=msgTranLog

kafka.msg.output.data.sync.topic=oSodbDataSync
oSodbDataSync.listener.service=true

#\u6D41\u5A92\u4F53\u53D1\u9001\u63A5\u53E3
video.status.service=false
video.rabbit.mq.queue=sodb.alarm
#video.rabbit.mq.uri=**************************************
video.rabbit.mq.uri=************************************
video.rabiit.mq.exchange=cctv.notify
video.rabiit.mq.routeKey=cctv.notify.key
cctv.gateway.key=cctv.gateway.key
video.rest.url=http://172.28.6.122:8080/aic/restfull/operationRestfullApi/excuteSqlByCode
video.login.account=admin
video.service.status.request.message.path=config\\videoStatusReqConfigPath.json
camera.service.status.request.message.path=config\\cameraStatusReqConfigPath.json

sodb.storage.status.req.url=http://172.28.6.125/api/bs/policy/open/channel/list
sodb.ec01.equ.status.req.url=http://172.28.6.125/api/bs/equ/info/open/cameraList
ping.timeout=3000
ping.interval=50


video.equ.status.service.code=VideoEquStatus
video.equ.status.online.service.code=VideoEquStatusOnline
video.equ.status.offline.service.code=VideoEquStatusOffline
video.equ.status.monitor.resource.url="http://192.168.112.163:2201/api/bs/aicService/open/list"
video.equ.status.monitor.camera.url="http://192.168.112.163:2201/api/bs/equ/info/open/cameraList"
video.equ.status.monitor.service.code=VideoMediaMonitor
