<?xml version="1.0" encoding="UTF-8"?>
<!--定时任务配置文件： -->
<quartz>
    <job>
        <job-detail>
            <name>videoServiceStatus-Job</name>
            <group>videoServiceStatusJobGroup</group>
            <description>摄像机存储状态查询任务Job</description>
            <job-class>com.air.security.sodb.data.receive.job.VideoServiceStatusJob</job-class>
        </job-detail>
        <trigger>
            <!-- JOB开关，on:开启 off:关闭 -->
            <switch>on</switch>
            <name>videoServiceStatus-Job-trigger</name>
            <group>videoServiceStatusJobGroup</group>
            <cron-expression>0 0/10 * * * ?</cron-expression>
        </trigger>
    </job>

    <job>
        <job-detail>
            <name>videoStatusMonitor-Job</name>
            <group>videoStatusMonitorJobGroup</group>
            <description>流媒体摄像机在线状态查询采集任务Job</description>
            <job-class>com.air.security.sodb.data.receive.job.VideoStatusReqJob</job-class>
        </job-detail>
        <trigger>
            <!-- JOB开关，on:开启 off:关闭 -->
            <switch>on</switch>
            <name>videoStatusMonitor-Job-trigger</name>
            <group>videoStatusMonitorJobGroup</group>
            <!-- 每日凌晨4点执行 -->
            <!-- <cron-expression>0 0 4 * * ?</cron-expression> -->
            <!-- 2020年9月24日 17:42:34执行一次 -->
            <!-- <cron-expression>34 42 17 24 9 ? 2020</cron-expression> -->
            <cron-expression>0 0/30 * * * ?</cron-expression>
        </trigger>
    </job>

</quartz>