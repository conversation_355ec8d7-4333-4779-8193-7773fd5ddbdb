package com.air.security.sodb.data;

import org.slf4j.Logger;
import org.slf4j.LoggerFactory;

import com.air.security.sodb.data.core.constant.MsgIdConstant;
import com.air.security.sodb.data.core.quartz.SchedulerManager;
import com.air.security.sodb.data.core.spring.SpringUtil;
import com.air.security.sodb.data.core.util.HaLog;
import com.air.security.sodb.data.core.util.PropertyUtil;
import com.air.security.sodb.data.core.util.ThreadUtil;
import com.air.security.sodb.data.receive.listener.TlqMessageListener;
import com.air.security.sodb.data.receive.listener.VideoStatusRabbitMqConsumer;
import com.air.security.sodb.data.release.listener.KafkaDataSyncMsgListener;

/**
 * @Description: 前端数据交互启动-启动所有
 * @author: zhangc
 * @Date: 2018年12月18日
 */
public class RunStartAll {

    private static final Logger log = LoggerFactory.getLogger(RunStartAll.class);

    private static boolean videoService = Boolean.parseBoolean(PropertyUtil.getProperty("video.status.service"));

    private static boolean tlqService = Boolean.parseBoolean(PropertyUtil.getProperty("tlq.service"));

    private static boolean dataSyncService = Boolean.parseBoolean(PropertyUtil.getProperty("oSodbDataSync.listener.service"));

    public static void main(String[] args) throws Exception {
        start();
    }

    private static void start() {
        // 初始化spring
        SpringUtil.initSpring();

        if (videoService) {
            VideoStatusRabbitMqConsumer videoStatusRabbitMqConsumer = new VideoStatusRabbitMqConsumer();
            videoStatusRabbitMqConsumer.receiveData();
        }

        if (tlqService) {
            TlqMessageListener listener = new TlqMessageListener();
            ThreadUtil.runWithNewThread(listener);
        }

        if (dataSyncService) {
            ThreadUtil.runWithNewThread(new KafkaDataSyncMsgListener());
        }

        runQuartzJob();
    }

    /**
     * 启动定时任务
     */
    private static void runQuartzJob() {
        HaLog.info(log, MsgIdConstant.MS_INF_0001, "quartz定时任务");
        SchedulerManager manager = new SchedulerManager();
        manager.runJob("quartzJob.xml");
        HaLog.info(log, MsgIdConstant.MS_INF_0002, "quartz定时任务");
    }

    /**
     * 停止定时任务
     */
    private static void shutQuartzJob() {
        HaLog.info(log, MsgIdConstant.MS_INF_0001, "quartz定时任务");
        SchedulerManager manager = new SchedulerManager();
        manager.shutJob();
        HaLog.info(log, MsgIdConstant.MS_INF_0002, "quartz定时任务");
    }

}
