package com.air.security.sodb.data.core.jolt.transform;

import java.util.List;
import java.util.Map;

import javax.inject.Inject;

import com.air.security.sodb.data.core.constant.ExceptionConstant;
import com.bazaarvoice.jolt.SpecDriven;
import com.bazaarvoice.jolt.Transform;
import com.bazaarvoice.jolt.exception.SpecException;

/**
 * 字段扩展的transform
 *
 * <AUTHOR>
 */
public class ColumnExtendTransform implements SpecDriven, Transform {

    /**
     * 转换规则配置
     * <p>
     * 如："equTypeCode": ["equType"]
     */
    private final Object spec;

    @Inject
    public ColumnExtendTransform(Object spec) {
        this.spec = spec;
    }

    /**
     * 转换
     * <p>
     * example:
     * input -> "equTypeCode": "EC0101"
     * output -> "equTypeCode": "EC0101", "equType": "EC0101"
     */
    @Override
    public Object transform(Object input) {

        if (null == input) {
            return null;
        }

        // spec 配置不合规范
        if (!(spec instanceof Map)) {
            throw new SpecException(ExceptionConstant.SPEC_CONF_IS_NOT_MAPJSON_EXCEPTION);
        }

        // 输入的源字符串不是标准json
        if (!(input instanceof Map)) {
            throw new SpecException(ExceptionConstant.INPUT_IS_NOT_MAPJOSN_EXCEPTION);
        }

        // spec配置转map
        Map<String, Object> specMap = (Map<String, Object>) spec;

        // input输入转map
        Map<String, Object> inputMap = null;

        inputMap = (Map<String, Object>) input;

        for (Map.Entry<String, Object> specEntry : specMap.entrySet()) {

            // 配置文件key，需要转换的数据项名称
            String specKey = specEntry.getKey();

            List<String> specValue = (List<String>) specEntry.getValue();

            if (null == inputMap.get(specKey)) {
                continue;
            }

            if (null == specValue || specValue.isEmpty()) {
                continue;
            }

            // 需要转换的数据字典值
            String outputValue = inputMap.get(specKey).toString();

            for (String outputKey : specValue) {
                inputMap.put(outputKey, outputValue);
            }

        }

        return inputMap;
    }

}
