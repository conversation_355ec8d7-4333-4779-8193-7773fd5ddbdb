package com.air.security.sodb.data.core.constant;

/**
 * @Description: Ems相关异常编码
 * @author: zhangc
 * @Date: 2018年11月26日
 */
public enum ALarmSysDataCode {

    /**
     * 报警级别-1级
     */
    ALARM_LEVEL_1("1", "1", "一级"),

    /**
     * 报警级别-2级
     */
    ALARM_LEVEL_2("2", "2", "二级"),

    /**
     * 报警级别-3级
     */
    ALARM_LEVEL_3("3", "3", "三级"),

    /**
     * 报警分类-摄像头报警
     */
    ALARM_CLASSIFY_VIDEO(null, "AC01", "摄像机报警"),

    /**
     * 报警分类-门禁报警
     */
    ALARM_CLASSIFY_ACS(null, "AC02", "门禁报警"),

    ;

    /**
     * 输入代码
     */
    private final String inputCode;

    /**
     * 输出代码
     */
    private final String outputCode;

    /**
     * 代码名称
     */
    private final String name;

    ALarmSysDataCode(String inputCode, String outputCode, String name) {
        this.inputCode = inputCode;
        this.outputCode = outputCode;
        this.name = name;
    }

    /**
     * 根据inputCode获取去输出信息
     *
     * @param code
     * @return
     */
    public static String[] getValueByCode(String inputCode) {
        for (ALarmSysDataCode sysDataCode : ALarmSysDataCode.values()) {
            if (inputCode.equals(sysDataCode.getInputCode())) {
                return new String[]{sysDataCode.getOutputCode(), sysDataCode.getName()};
            }
        }
        return null;
    }

    /**
     * @return 输入代码
     */
    public String getInputCode() {
        return inputCode;
    }

    /**
     * @return 输出代码
     */
    public String getOutputCode() {
        return outputCode;
    }

    /**
     * @return 代码名称
     */
    public String getName() {
        return name;
    }

}
