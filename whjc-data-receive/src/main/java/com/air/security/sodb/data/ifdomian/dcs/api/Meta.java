package com.air.security.sodb.data.ifdomian.dcs.api;

import com.thoughtworks.xstream.annotations.XStreamAlias;

/**
 * @Description:
 * @author: zhangc
 * @Date: 2019年3月10日
 */
@XStreamAlias("META")
public class Meta {
    /**
     * 消息发送者
     */
    @XStreamAlias("SNDR")
    private String sndr = new String();

    /**
     * 发送时间
     */
    @XStreamAlias("DTTM")
    private String dttm = new String();

    /**
     * 消息类别
     */
    @XStreamAlias("TYPE")
    private String type = new String();

    /**
     * 消息类型
     */
    @XStreamAlias("STYP")
    private String styp = new String();

    /**
     * @param 消息发送者
     */
    public void setSndr(String sndr) {
        this.sndr = sndr;
    }

    /**
     * @return 消息发送者
     */
    public String getSndr() {
        return sndr;
    }

    /**
     * @param 发送时间
     */
    public void setDttm(String dttm) {
        this.dttm = dttm;
    }

    /**
     * @return 发送时间
     */
    public String getDttm() {
        return dttm;
    }

    /**
     * @param 消息类别
     */
    public void setType(String type) {
        this.type = type;
    }

    /**
     * @return 消息类别
     */
    public String getType() {
        return type;
    }

    /**
     * @param 消息类型
     */
    public void setStyp(String styp) {
        this.styp = styp;
    }

    /**
     * @return 消息类型
     */
    public String getStyp() {
        return styp;
    }

}
