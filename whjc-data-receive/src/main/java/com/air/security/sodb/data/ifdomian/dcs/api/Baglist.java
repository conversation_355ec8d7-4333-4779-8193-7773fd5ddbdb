package com.air.security.sodb.data.ifdomian.dcs.api;

import java.util.ArrayList;

import com.thoughtworks.xstream.annotations.XStreamAlias;
import com.thoughtworks.xstream.annotations.XStreamImplicit;

/**
 * @Description: 行李列表
 * @author: zhangc
 * @Date: 2019年3月10日
 */
@XStreamAlias("BAGLIST")
public class Baglist {

    @XStreamAlias("BAGITEM")
    @XStreamImplicit(itemFieldName = "BAGITEM")
    private ArrayList<String> bagitem;

    /**
     * @return the bagitem
     */
    public ArrayList<String> getBagitem() {
        return bagitem;
    }

    /**
     * @param bagitem the bagitem to set
     */
    public void setBagitem(ArrayList<String> bagitem) {
        this.bagitem = bagitem;
    }

}
