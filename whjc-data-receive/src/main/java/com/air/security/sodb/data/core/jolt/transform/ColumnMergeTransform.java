package com.air.security.sodb.data.core.jolt.transform;

import java.util.List;
import java.util.Map;

import javax.inject.Inject;

import org.apache.commons.lang3.StringUtils;

import com.air.security.sodb.data.core.constant.ExceptionConstant;
import com.bazaarvoice.jolt.SpecDriven;
import com.bazaarvoice.jolt.Transform;
import com.bazaarvoice.jolt.exception.SpecException;

/**
 * @Description: 根据spec配置将将多个字段合并为一个
 * @author: zhangc
 * @Date: 2018年12月13日
 */
public class ColumnMergeTransform implements SpecDriven, Transform {

    /**
     * 转换规则配置
     * 如："flightIdentity":
     * {
     * "sourceCol": ["airlineIataCode", "flightNo"],
     * "separator": ""
     * }
     */
    private final Object spec;

    @Inject
    public ColumnMergeTransform(Object spec) {
        this.spec = spec;
    }

    /**
     * 转换
     * <p>
     * example:
     * input -> "airlineIataCode": "CZ", "flightNo": "6710"
     * output -> "flightIdentity":"CZ6710"
     */
    @SuppressWarnings("unchecked")
    @Override
    public Object transform(Object input) {

        // spec 配置不合规范
        if (!(spec instanceof Map)) {
            throw new SpecException(ExceptionConstant.SPEC_CONF_IS_NOT_MAPJSON_EXCEPTION);
        }

        // 输入的源字符串不是标准json
        if (!(input instanceof Map)) {
            throw new SpecException(ExceptionConstant.INPUT_IS_NOT_MAPJOSN_EXCEPTION);
        }

        // spec配置转map
        Map<String, Object> specMap = (Map<String, Object>) spec;

        // input输入转map
        Map<String, Object> inputMap = (Map<String, Object>) input;

        // 读取spec配置，获取需要转换的key
        for (Map.Entry<String, Object> specEntry : specMap.entrySet()) {
            // 合并之后的key
            String mergeKey = specEntry.getKey();

            Map<String, Object> specValue = (Map<String, Object>) specEntry.getValue();

            // 待合并的列
            List<String> sourceCols = (List<String>) specValue.get("sourceCol");

            // 分隔符
            String separator = (String) specValue.get("separator");

            StringBuilder mergeValueSb = new StringBuilder();
            for (String sourceCol : sourceCols) {
                String value = String.valueOf(inputMap.get(sourceCol));
                if (StringUtils.isNotBlank(value)) {
                    mergeValueSb.append(value);
                    mergeValueSb.append(separator);
                }
            }
            String mergeValue = mergeValueSb.substring(0, mergeValueSb.length() - separator.length());
            inputMap.put(mergeKey, mergeValue);
        }

        return inputMap;
    }

}
