package com.air.security.sodb.data.core.constant;

/**
 * @Description: 全局常量类
 * @author: zhangc
 * @Date: 2018年12月10日
 */
public class GlobalCodeConstant {

    public static final Integer BASE_ERROR_CODE = Integer.valueOf(10000);
    public static final String BASE_ERROR_CODE_NAME = "错误";

    public static final Integer LOGIN_ERROR_CODE = Integer.valueOf(11000);
    public static final String LOGIN_ERROR_CODE_NAME = "登录错误";

    public static final Integer SAVE_ERROR_CODE = Integer.valueOf(12001);
    public static final String SAVE_ERROR_CODE_NAME = "添加错误";

    public static final Integer UPDATE_ERROR_CODE = Integer.valueOf(12002);
    public static final String UPDATE_ERROR_CODE_NAME = "编辑错误";

    public static final Integer DELETE_ERROR_CODE = Integer.valueOf(12003);
    public static final String DELETE_ERROR_CODE_NAME = "删除错误";

    public static final Integer SEARCH_ERROR_CODE = Integer.valueOf(12004);
    public static final String SEARCH_ERROR_CODE_NAME = "查询错误";

    public static final Integer PARAMS_ERROR_CODE = Integer.valueOf(12005);
    public static final String PARAMS_ERROR_CODE_NAME = "参数错误";

    public static final Integer FILE_ERROR_CODE = Integer.valueOf(12006);
    public static final String FILE_ERROR_CODE_NAME = "文件错误";

    public static final Integer AUTH_ERROR_CODE = Integer.valueOf(13000);
    public static final String AUTH_ERROR_CODE_NAME = "权限错误";

    public static final Integer BASE_SUCCESS_CODE = Integer.valueOf(200);
    public static final String BASE_SUCCESS_CODE_NAME = "成功";
    public static final Integer PORT_ERROR_CODE = Integer.valueOf(-1);
    public static final String PORT_ERROR_CODE_NAME = "失败";

    /**
     * spec配置字符串分隔符
     */
    public static final String SPEC_STRING_SPLIT_CHAR = "@@";

    /**
     * 文件编码
     */
    public static final String FILE_ENCODE = "utf-8";

    /**
     * 数据格式-json
     */
    public static final String DATA_FORMAT_JSON = "json";

    /**
     * 数据格式-xml
     */
    public static final String DATA_FORMAT_XML = "xml";

    /**
     * 数据格式-string
     */
    public static final String DATA_FORMAT_STRING = "string";

    /**
     * 日志类型，0：前端接收日志
     */
    public static final Character LOG_TYPE_FRONT_RECV = '0';

    /**
     * 日志类型，1：交互系统接收日志
     */
    public static final Character LOG_TYPE_RECV = '1';

    /**
     * 日志类型，2：交互系统发送日志
     */
    public static final Character LOG_TYPE_SEND = '2';

    /**
     * 日志类型，2：交互系统保存日志
     */
    public static final Character LOG_TYPE_SAVE = '3';

    /**
     * 服务类型，1：接收数据日志
     */
    public static final Character LOG_SERVICE_TYPE_RECV = '1';

    /**
     * 服务类型，0：服务数据日志
     */
    public static final Character LOG_SERVICE_TYPE_RELS = '0';

    /**
     * kafka生产者配置文件路径
     */
    public static final String KAFKA_PRODUCER_CONFIG_PATH_KEY = "kafka.producer.config.path";

    /**
     * kafka消费者配置文件路径
     */
    public static final String KAFKA_CONSUMER_CONFIG_PATH_KEY = "kafka.consumer.config.path";

    /**
     * 格式化处理主配置文件路径
     */
    public static final String FORMAT_HANDLE_RECV_CONFIG_PATH_KEY = "format.handle.recv.config.path";

    /**
     * 保存处理主配置文件路径
     */
    public static final String FORMAT_HANDLE_SAVE_CONFIG_PATH_KEY = "format.handle.save.config.path";

    /**
     * kafka主题监听的配置文件路径
     */
    public static final String KAFKA_LISTENER_CONFIG_PATH_KEY = "kafka.listener.config.path";

    /**
     * 数据类型-STRING
     */
    public static final String PROP_TYPE_STRING = "STRING";

    /**
     * json数据key标识-json数组
     */
    public static final String JSON_DATA_LIST_KEY = "dataList";

    /**
     * 操作类型-新增
     */
    public static final String OPERATION_TYPE_ADD = "0";

    /**
     * 操作类型-删除
     */
    public static final String OPERATION_TYPE_DEL = "1";

    /**
     * 操作类型-修改
     */
    public static final String OPERATION_TYPE_MODIFY = "2";

    /**
     * 安保报警系统-SAEMS
     */
    public static final String SYS_CODE_SAEMS = "SAEMS";

    /**
     * 安全运行系统-SOMS
     */
    public static final String SYS_CODE_SOMS = "SOMS";

    /**
     * 围界-PIDS
     */
    public static final String SYS_CODE_PIDS = "PIDS";

    /**
     * 零
     */
    public static final String ZERO = "0";

    /**
     * 一
     */
    public static final String ONE = "1";

    /**
     * 二
     */
    public static final String TWO = "2";

    /**
     * 三
     */
    public static final String THREE = "3";

    /**
     * 是
     */
    public static final String TRUE = "true";

    /**
     * 否
     */
    public static final String  FALSE = "false";

    /**
     * 成功
     */
    public static final int  SUCCESS = 200;

}
