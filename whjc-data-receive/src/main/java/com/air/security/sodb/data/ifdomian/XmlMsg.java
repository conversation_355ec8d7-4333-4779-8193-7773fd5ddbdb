package com.air.security.sodb.data.ifdomian;

import java.util.ArrayList;

import com.thoughtworks.xstream.annotations.XStreamAlias;
import com.thoughtworks.xstream.annotations.XStreamImplicit;

/**
 * @Description: xml的数据信息类
 * @author: zhangc
 * @Date: 2019年1月4日
 */
@XStreamAlias("Data")
public class XmlMsg {

    /**
     * 数据项列表
     */
    @XStreamImplicit
    private ArrayList<XmlData> property = new ArrayList<XmlData>();

    @XStreamAlias("dataList")
    private ArrayList<XmlArrayNode> dataList;

    /**
     * @return 数据项列表
     */
    public ArrayList<XmlData> getProperty() {
        return property;
    }

    /**
     * @param 数据项列表
     */
    public void setProperty(ArrayList<XmlData> property) {
        this.property = property;
    }

    /**
     * @return the dataList
     */
    public ArrayList<XmlArrayNode> getDataList() {
        return dataList;
    }

    /**
     * @param dataList the dataList to set
     */
    public void setDataList(ArrayList<XmlArrayNode> dataList) {
        this.dataList = dataList;
    }

}
