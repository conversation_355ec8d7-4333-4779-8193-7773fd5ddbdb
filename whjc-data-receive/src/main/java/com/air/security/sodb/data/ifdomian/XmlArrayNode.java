package com.air.security.sodb.data.ifdomian;

import java.util.ArrayList;

import com.thoughtworks.xstream.annotations.XStreamAlias;
import com.thoughtworks.xstream.annotations.XStreamImplicit;

/**
 * @Description: xml数组单个数据项
 * @author: zhangc
 * @Date: 2019年1月4日
 */
@XStreamAlias("data")
public class XmlArrayNode {

    @XStreamImplicit
    private ArrayList<XmlData> property = new ArrayList<XmlData>();

    /**
     * @return the property
     */
    public ArrayList<XmlData> getProperty() {
        return property;
    }

    /**
     * @param property the property to set
     */
    public void setProperty(ArrayList<XmlData> property) {
        this.property = property;
    }

}
