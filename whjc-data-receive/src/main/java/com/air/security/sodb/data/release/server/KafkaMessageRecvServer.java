package com.air.security.sodb.data.release.server;

import com.air.security.sodb.data.core.exception.SystemBaseException;
import com.air.security.sodb.data.receive.ifdomain.Meta;
import com.alibaba.fastjson.JSONObject;

/**
 * @Description: 消息数据的分发处理接口
 * @author: zhangc
 * @Date: 2018年12月5日
 */
public interface KafkaMessageRecvServer {

    /**
     * 消息分发处理
     *
     * @param meta
     * @param messageJson
     * @throws SystemBaseException
     */
    void handle(Meta meta, JSONObject messageJson) throws SystemBaseException;
}
