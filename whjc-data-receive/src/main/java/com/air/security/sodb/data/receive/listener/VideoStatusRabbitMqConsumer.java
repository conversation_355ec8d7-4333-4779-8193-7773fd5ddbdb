package com.air.security.sodb.data.receive.listener;

import com.air.security.sodb.data.core.constant.MsgIdConstant;
import com.air.security.sodb.data.core.util.DateTimeUtil;
import com.air.security.sodb.data.core.util.HaLog;
import com.air.security.sodb.data.core.util.PropertyUtil;
import com.air.security.sodb.data.core.util.UuidUtil;
import com.air.security.sodb.data.receive.ifdomain.Meta;
import com.air.security.sodb.data.receive.server.MqMessageRecvServer;
import com.air.security.sodb.data.receive.server.MqMessageRecvServerImpl;
import com.alibaba.fastjson.JSONArray;
import com.alibaba.fastjson.JSONObject;
import com.rabbitmq.client.*;
import org.apache.commons.lang3.StringUtils;
import org.slf4j.Logger;
import org.slf4j.LoggerFactory;

import java.io.IOException;
/**
 * 流媒体服务状态通知采集
 * <AUTHOR>
public class VideoStatusRabbitMqConsumer {
    private static final Logger log = LoggerFactory.getLogger(VideoStatusRabbitMqConsumer.class);

//	private static String queneName = PropertyUtil.getProperty("video.rabbit.mq.queue");

    private static String exchangeName = PropertyUtil.getProperty("video.rabiit.mq.exchange");

    private static String uri = PropertyUtil.getProperty("video.rabbit.mq.uri");

    private static String routeKey = PropertyUtil.getProperty("video.rabiit.mq.routeKey");

    private static String videoStatusOnlineCode = PropertyUtil.getProperty("video.equ.status.online.service.code");

    private static String videoStatusOfflineCode = PropertyUtil.getProperty("video.equ.status.offline.service.code");

    private MqMessageRecvServer server = new MqMessageRecvServerImpl();

    private static String videoMediaMonitor = PropertyUtil.getProperty("video.equ.status.monitor.service.code");
    public void receiveData() {
        HaLog.info(log, MsgIdConstant.MS_INF_0001, "流媒体服务状态通知采集任务");
        ConnectionFactory factory = new ConnectionFactory();
        try {
            factory.setUri(uri);
            Connection connection = factory.newConnection();
            Channel channel = connection.createChannel();

            // 声明交换机类型
            channel.exchangeDeclare(exchangeName, "direct", true);
            String queueName = channel.queueDeclare().getQueue();
            channel.queueBind(queueName, exchangeName, routeKey);
            Consumer consumer = new DefaultConsumer(channel) {
                @Override
                public void handleDelivery(String consumerTag, Envelope envelope, AMQP.BasicProperties properties,
                                           byte[] body) throws IOException {

                    // 获取到的数据
                    String message = new String(body, "UTF-8");
                    JSONObject msgJson = JSONObject.parseObject(message);

                    HaLog.info(log, MsgIdConstant.MS_INF_0009, "流媒体服务状态变更信息", message);
                    // 数据类型
                    String cmdtype = msgJson.getString("cmdtype");
                    String str = "10039";
                    if (StringUtils.equals(str, cmdtype)) {
                        Meta meta = new Meta();
                        meta.setEventType(videoStatusOnlineCode);
                        meta.setRecvSequence(UuidUtil.getUuid32());
                        JSONArray equStatusArray = msgJson.getJSONArray("status");
                        for (Object object : equStatusArray) {
                            JSONObject jsonObj = (JSONObject) object;
                            jsonObj.put("time", DateTimeUtil.getCurrentTimestampStr("yyyy-MM-dd HH:mm:ss"));
                            HaLog.info(log, MsgIdConstant.MS_INF_0009, "摄像机上线信息", message);
                            server.handle(meta, JSONObject.toJSONString(jsonObj));
                        }
                    }
                    String strs = "10040";
                    if (StringUtils.equals(strs, cmdtype)) {
                        Meta meta = new Meta();
                        meta.setEventType(videoStatusOfflineCode);
                        meta.setRecvSequence(UuidUtil.getUuid32());
                        HaLog.info(log, MsgIdConstant.MS_INF_0009, "摄像机离线信息", message);
                        server.handle(meta, JSONObject.toJSONString(msgJson));
                    }
                  //4.15视频流中断告警通知
			        if (StringUtils.equals("20001", cmdtype)) {
			        	Meta meta = new Meta();
						meta.setEventType(videoMediaMonitor);
						meta.setRecvSequence(UuidUtil.getUuid32());
						HaLog.info(log, MsgIdConstant.MS_INF_0009, "视频流中断告警", message);
						server.handle(meta, JSONObject.toJSONString(msgJson));
			        }
			        //4.16Restful接口访问失败告警
			        if (StringUtils.equals("20055", cmdtype)) {
			        	Meta meta = new Meta();
						meta.setEventType(videoMediaMonitor);
						meta.setRecvSequence(UuidUtil.getUuid32());
						HaLog.info(log, MsgIdConstant.MS_INF_0009, "接口访问信息", message);
						server.handle(meta, JSONObject.toJSONString(msgJson));
			        }
			        //4.17 存储磁盘不可读写告警 
			        if (StringUtils.equals("20056", cmdtype)) {
			        	Meta meta = new Meta();
						meta.setEventType(videoMediaMonitor);
						meta.setRecvSequence(UuidUtil.getUuid32());
						HaLog.info(log, MsgIdConstant.MS_INF_0009, "视频流中断信息", message);
						server.handle(meta, JSONObject.toJSONString(msgJson));
			        }
			        //4.18 存储/接入服务离线告警 
			        if (StringUtils.equals("20057", cmdtype)) {
			        	Meta meta = new Meta();
						meta.setEventType(videoMediaMonitor);
						meta.setRecvSequence(UuidUtil.getUuid32());
						HaLog.info(log, MsgIdConstant.MS_INF_0009, "数据存储接入服务信息", message);
						server.handle(meta, JSONObject.toJSONString(msgJson));
			        }
			        //4.19 存储服务无新录像文件告警 
			        if (StringUtils.equals("20058", cmdtype)) {
			        	Meta meta = new Meta();
						meta.setEventType(videoMediaMonitor);
						meta.setRecvSequence(UuidUtil.getUuid32());
						HaLog.info(log, MsgIdConstant.MS_INF_0009, "存储服务新录像文件信息", message);
						server.handle(meta, JSONObject.toJSONString(msgJson));
			        }
			    }
            };

            // channel绑定队列、消费者，autoAck为true表示一旦收到消息则自动回复确认消息
            channel.basicConsume(queueName, true, consumer);
        } catch (Exception e) {
            HaLog.error(log, e, MsgIdConstant.MS_ERR_0001, "");
        } finally {
            HaLog.info(log, MsgIdConstant.MS_INF_0002, "流媒体服务状态通知采集任务");
        }
    }
}
