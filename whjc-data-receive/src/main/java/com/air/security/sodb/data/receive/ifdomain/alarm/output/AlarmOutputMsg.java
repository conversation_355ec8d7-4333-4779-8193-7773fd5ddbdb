package com.air.security.sodb.data.receive.ifdomain.alarm.output;

import com.air.security.sodb.data.receive.ifdomain.Meta;
import com.air.security.sodb.data.receive.ifdomain.OutputMsg;

/**
 * @Description: 门禁事件输出信息
 * @author: zhangc
 * @Date: 2018年12月10日
 */
public class AlarmOutputMsg extends OutputMsg {

    /**
     * 消息头
     */
    private Meta meta;

    /**
     * 消息体
     */
    private AlarmBodyMsg body;

    /**
     * @return 消息头
     */
    @Override
    public Meta getMeta() {
        return meta;
    }

    /**
     * @param 消息头
     */
    @Override
    public void setMeta(Meta meta) {
        this.meta = meta;
    }

    /**
     * @return 消息体
     */
    public AlarmBodyMsg getBody() {
        return body;
    }

    /**
     * @param 消息体
     */
    public void setBody(AlarmBodyMsg body) {
        this.body = body;
    }

}
