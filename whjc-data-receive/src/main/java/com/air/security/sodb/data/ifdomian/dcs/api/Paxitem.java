package com.air.security.sodb.data.ifdomian.dcs.api;

import com.thoughtworks.xstream.annotations.XStreamAlias;

/**
 * @Description:
 * @author: zhangc
 * @Date: 2019年3月10日
 */
@XStreamAlias("PAXITEM")
public class Paxitem {

    @XStreamAlias("DEPTCD")
    private String deptcd;

    @XStreamAlias("ARRVCD")
    private String arrvcd;

    @XStreamAlias("DEPTTM")
    private String depttm;

    @XStreamAlias("ARRVTM")
    private String arrvtm;

    @XStreamAlias("PSRNAME")
    private String psrname;

    @XStreamAlias("PSRCHNNAME")
    private String psrchnname;

    @XStreamAlias("PSRSTATUS")
    private String psrstatus;

    @XStreamAlias("PSRGENDER")
    private String psrgender;

    @XStreamAlias("CERTTYPE")
    private String certtype;

    @XStreamAlias("CERTNO")
    private String certno;

    @XStreamAlias("PSRBRDNO")
    private String psrbrdno;

    @XStreamAlias("PSRGROUP")
    private String psrgroup;

    @XStreamAlias("PSRICS")
    private String psrics;

    @XStreamAlias("PSRCRS")
    private String psrcrs;

    @XStreamAlias("PSRSEATNBR")
    private String psrseatnbr;

    @XStreamAlias("PSRFFFR")
    private String psrfffr;

    @XStreamAlias("PSREFMN")
    private String psrefmn;

    @XStreamAlias("PSRCLASS")
    private String psrclass;

    @XStreamAlias("PSRCKIPID")
    private String psrckipid;

    @XStreamAlias("PSROFFICE")
    private String psroffice;

    @XStreamAlias("PSRAGENT")
    private String psragent;

    @XStreamAlias("PSRCKITIME")
    private String psrckitime;

    @XStreamAlias("EDI")
    private String edi;

    @XStreamAlias("PSRINF")
    private String psrinf;

    @XStreamAlias("BAGGAGE")
    private Baggage baggage;

    /**
     * @return the deptcd
     */
    public String getDeptcd() {
        return deptcd;
    }

    /**
     * @param deptcd the deptcd to set
     */
    public void setDeptcd(String deptcd) {
        this.deptcd = deptcd;
    }

    /**
     * @return the arrvcd
     */
    public String getArrvcd() {
        return arrvcd;
    }

    /**
     * @param arrvcd the arrvcd to set
     */
    public void setArrvcd(String arrvcd) {
        this.arrvcd = arrvcd;
    }

    /**
     * @return the depttm
     */
    public String getDepttm() {
        return depttm;
    }

    /**
     * @param depttm the depttm to set
     */
    public void setDepttm(String depttm) {
        this.depttm = depttm;
    }

    /**
     * @return the arrvtm
     */
    public String getArrvtm() {
        return arrvtm;
    }

    /**
     * @param arrvtm the arrvtm to set
     */
    public void setArrvtm(String arrvtm) {
        this.arrvtm = arrvtm;
    }

    /**
     * @return the psrname
     */
    public String getPsrname() {
        return psrname;
    }

    /**
     * @param psrname the psrname to set
     */
    public void setPsrname(String psrname) {
        this.psrname = psrname;
    }

    /**
     * @return the psrchnname
     */
    public String getPsrchnname() {
        return psrchnname;
    }

    /**
     * @param psrchnname the psrchnname to set
     */
    public void setPsrchnname(String psrchnname) {
        this.psrchnname = psrchnname;
    }

    /**
     * @return the psrstatus
     */
    public String getPsrstatus() {
        return psrstatus;
    }

    /**
     * @param psrstatus the psrstatus to set
     */
    public void setPsrstatus(String psrstatus) {
        this.psrstatus = psrstatus;
    }

    /**
     * @return the psrgender
     */
    public String getPsrgender() {
        return psrgender;
    }

    /**
     * @param psrgender the psrgender to set
     */
    public void setPsrgender(String psrgender) {
        this.psrgender = psrgender;
    }

    /**
     * @return the certtype
     */
    public String getCerttype() {
        return certtype;
    }

    /**
     * @param certtype the certtype to set
     */
    public void setCerttype(String certtype) {
        this.certtype = certtype;
    }

    /**
     * @return the certno
     */
    public String getCertno() {
        return certno;
    }

    /**
     * @param certno the certno to set
     */
    public void setCertno(String certno) {
        this.certno = certno;
    }

    /**
     * @return the psrbrdno
     */
    public String getPsrbrdno() {
        return psrbrdno;
    }

    /**
     * @param psrbrdno the psrbrdno to set
     */
    public void setPsrbrdno(String psrbrdno) {
        this.psrbrdno = psrbrdno;
    }

    /**
     * @return the psrgroup
     */
    public String getPsrgroup() {
        return psrgroup;
    }

    /**
     * @param psrgroup the psrgroup to set
     */
    public void setPsrgroup(String psrgroup) {
        this.psrgroup = psrgroup;
    }

    /**
     * @return the psrics
     */
    public String getPsrics() {
        return psrics;
    }

    /**
     * @param psrics the psrics to set
     */
    public void setPsrics(String psrics) {
        this.psrics = psrics;
    }

    /**
     * @return the psrcrs
     */
    public String getPsrcrs() {
        return psrcrs;
    }

    /**
     * @param psrcrs the psrcrs to set
     */
    public void setPsrcrs(String psrcrs) {
        this.psrcrs = psrcrs;
    }

    /**
     * @return the psrseatnbr
     */
    public String getPsrseatnbr() {
        return psrseatnbr;
    }

    /**
     * @param psrseatnbr the psrseatnbr to set
     */
    public void setPsrseatnbr(String psrseatnbr) {
        this.psrseatnbr = psrseatnbr;
    }

    /**
     * @return the psrfffr
     */
    public String getPsrfffr() {
        return psrfffr;
    }

    /**
     * @param psrfffr the psrfffr to set
     */
    public void setPsrfffr(String psrfffr) {
        this.psrfffr = psrfffr;
    }

    /**
     * @return the psrefmn
     */
    public String getPsrefmn() {
        return psrefmn;
    }

    /**
     * @param psrefmn the psrefmn to set
     */
    public void setPsrefmn(String psrefmn) {
        this.psrefmn = psrefmn;
    }

    /**
     * @return the psrclass
     */
    public String getPsrclass() {
        return psrclass;
    }

    /**
     * @param psrclass the psrclass to set
     */
    public void setPsrclass(String psrclass) {
        this.psrclass = psrclass;
    }

    /**
     * @return the psrckipid
     */
    public String getPsrckipid() {
        return psrckipid;
    }

    /**
     * @param psrckipid the psrckipid to set
     */
    public void setPsrckipid(String psrckipid) {
        this.psrckipid = psrckipid;
    }

    /**
     * @return the psroffice
     */
    public String getPsroffice() {
        return psroffice;
    }

    /**
     * @param psroffice the psroffice to set
     */
    public void setPsroffice(String psroffice) {
        this.psroffice = psroffice;
    }

    /**
     * @return the psragent
     */
    public String getPsragent() {
        return psragent;
    }

    /**
     * @param psragent the psragent to set
     */
    public void setPsragent(String psragent) {
        this.psragent = psragent;
    }

    /**
     * @return the psrckitime
     */
    public String getPsrckitime() {
        return psrckitime;
    }

    /**
     * @param psrckitime the psrckitime to set
     */
    public void setPsrckitime(String psrckitime) {
        this.psrckitime = psrckitime;
    }

    /**
     * @return the edi
     */
    public String getEdi() {
        return edi;
    }

    /**
     * @param edi the edi to set
     */
    public void setEdi(String edi) {
        this.edi = edi;
    }

    /**
     * @return the psrinf
     */
    public String getPsrinf() {
        return psrinf;
    }

    /**
     * @param psrinf the psrinf to set
     */
    public void setPsrinf(String psrinf) {
        this.psrinf = psrinf;
    }

    /**
     * @return the baggage
     */
    public Baggage getBaggage() {
        return baggage;
    }

    /**
     * @param baggage the baggage to set
     */
    public void setBaggage(Baggage baggage) {
        this.baggage = baggage;
    }


}
