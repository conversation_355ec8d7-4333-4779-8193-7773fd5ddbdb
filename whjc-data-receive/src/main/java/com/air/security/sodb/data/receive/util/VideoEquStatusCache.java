package com.air.security.sodb.data.receive.util;

import com.air.security.sodb.data.core.util.HttpRequestUtil;
import com.air.security.sodb.data.core.util.PropertyUtil;
import com.alibaba.fastjson.JSONArray;
import com.alibaba.fastjson.JSONObject;

import java.util.HashMap;
import java.util.List;
import java.util.Map;

/**
 * 同步获取设备状态实现类
 *
 * <AUTHOR>
public class VideoEquStatusCache {

    private static Map<String, String> map = new HashMap<>();

    private static String equStatusRequUrl = PropertyUtil.getProperty("sodb.ec01.equ.status.req.url");

    public static Map<String, String> getCacheMap() {
        if (map.size() == 0 || map.isEmpty()) {
            String result = HttpRequestUtil.sendPost(equStatusRequUrl, "");
            JSONObject resJson = JSONObject.parseObject(result);
            JSONArray cacheArray = resJson.getJSONArray("datalist");
            List<VideoEquStatus> cacheList = JSONArray.parseArray(cacheArray.toJSONString(), VideoEquStatus.class);
            for (VideoEquStatus ysEquInfo : cacheList) {
                map.put(ysEquInfo.getEquCode(), ysEquInfo.getTimeStateId());
            }
            System.out.println(map.size());

        }
        return map;
    }

}
