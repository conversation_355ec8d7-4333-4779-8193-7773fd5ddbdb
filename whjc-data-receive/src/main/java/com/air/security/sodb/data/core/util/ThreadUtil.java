package com.air.security.sodb.data.core.util;

import org.slf4j.Logger;
import org.slf4j.LoggerFactory;

import java.util.concurrent.*;

/**
 * 线程池Util
 *
 * <AUTHOR>
 */
@SuppressWarnings({"ALL", "AlibabaThreadShouldSetName"})
public class ThreadUtil {

    private static final Logger log = LoggerFactory.getLogger(ThreadUtil.class);

    /**
     * 创建新线程并执行
     *
     * @param target
     * @return
     */
    public static Thread runWithNewThread(Runnable target) {
        Thread t = new Thread(target);
        t.start();
        return t;
    }

    /**
     * 线程睡眠
     *
     * @param ms
     * @return
     */
    public static boolean sleepThread(long ms) {
        try {
            Thread.sleep(ms);
            return true;
        } catch (Exception e) {
            HaLog.error(log, "线程[" + Thread.currentThread().getName() + "]睡眠失败: ", e);
            return false;
        }
    }

    /**
     * 线程睡眠10毫秒
     *
     * @return
     */
    public static boolean sleepThreadTenMs() {
        return sleepThread(10L);
    }

    /**
     * 线程睡眠一个单位
     *
     * @return
     */
    public static boolean sleepThreadUnit() {
        return sleepThread(20L);
    }

    /**
     * pool
     **/

    private static ThreadLocal<ExecutorService> threadLocal = new ThreadLocal<>();
    private static ExecutorService mainExecutor;

    private static int nThreads = 0;

    static {
        String count = PropertyUtil.getProperty("threadpool.count");

        try {
            nThreads = Integer.parseInt(count);
        } catch (Exception e) {
            HaLog.error(log, "", e);
        }
        int len = 5;
        int lens = 100;
        if (nThreads < len || nThreads > lens) {
            nThreads = 10;
        }
        mainExecutor = new ThreadPoolExecutor(nThreads, nThreads,
                0L, TimeUnit.MILLISECONDS,
                new LinkedBlockingQueue<Runnable>());
    }

    private static ExecutorService getExecutor() {
        ExecutorService executor = threadLocal.get();
        if (executor == null) {
            executor = new ThreadPoolExecutor(nThreads, nThreads,
                    0L, TimeUnit.MILLISECONDS,
                    new LinkedBlockingQueue<Runnable>());
            threadLocal.set(executor);
            return executor;
        }
        return executor;
    }

    /**
     * 从线程池中获取一个线程运行
     *
     * <pre>
     * 该线程池共享一个, 所有调用此方法的线程公用一个线程池
     * </pre>
     *
     * @param runnable
     */
    public static void runWithThreadPool(Runnable runnable) {

        mainExecutor.submit(runnable);
    }

    /**
     * 从线程池中获取一个线程运行
     *
     * <pre>
     * 该线程池非共享, 调用此方法的线程创建使用自己的一个线程池
     * </pre>
     *
     * @param runnable
     */
    public static void runWithSelfThreadPool(Runnable runnable) {

        getExecutor().submit(runnable);
    }

    /**
     * 关闭线程池
     */
    public static void shutdown() {
        HaLog.info(log, "关闭线程池 start...");
        if (null != mainExecutor) {
            mainExecutor.shutdown();
        }
        HaLog.info(log, "关闭线程池 end...");
    }

    public static void run(Runnable[] runs) {
        // mainExecutor.submit(task)
    }

}
