package com.air.security.sodb.data.core.util;

import java.io.File;
import java.util.HashMap;
import java.util.Map;

import org.apache.commons.io.FileUtils;
import org.slf4j.Logger;
import org.slf4j.LoggerFactory;

import com.air.security.sodb.data.core.config.FormatHandleRecvConfig;
import com.air.security.sodb.data.core.constant.GlobalCodeConstant;
import com.air.security.sodb.data.core.constant.MsgIdConstant;
import com.alibaba.fastjson.JSONObject;
import com.bazaarvoice.jolt.Chainr;

/**
 * @Description: spec转换规则配置环境工具类
 * @author: zhangc
 * @Date: 2018年12月18日
 */
public class ChainrParamContextUtil {

    private static final Logger log = LoggerFactory.getLogger(ChainrParamContextUtil.class);

    /**
     * spec配置存储map
     */
    private static Map<String, Chainr> chainrContext = null;

    /**
     * 加载spec配置
     */
    public static void loadConf() {

        HaLog.info(log, MsgIdConstant.MS_INF_0001, "初始化spec转换规则配置");

        Map<String, FormatHandleRecvConfig> map = FormatHandleRecvConfigContextUtil.getContextParam();
        chainrContext = new HashMap<String, Chainr>(map.size());
        for (Map.Entry<String, FormatHandleRecvConfig> entry : map.entrySet()) {
            HaLog.info(log, MsgIdConstant.MS_INF_0001, "加载spec转换规则：" + entry.getKey());
            FormatHandleRecvConfig config = entry.getValue();

            // spec配置文件路径
            String specFilePath = config.getSpecPath();

            // spec json配置文件
            File specJsonFile = new File(specFilePath);

            if (!specJsonFile.exists()) {
                HaLog.info(log, MsgIdConstant.MS_INF_0009, config.getEventTypeName() + "spec转换配置加载", "未找到配置文件");
                continue;
            }

            try {
                String specString = FileUtils.readFileToString(specJsonFile, GlobalCodeConstant.FILE_ENCODE);

                // spec转换对象
                Chainr chainr = Chainr.fromSpec(JSONObject.parseArray(specString));
                chainrContext.put(config.getEventType(), chainr);
            } catch (Exception e) {
                HaLog.error(log, MsgIdConstant.MS_ERR_0008, e, config.getEventTypeName());
            } finally {
                HaLog.info(log, MsgIdConstant.MS_INF_0002, "加载spec转换规则：" + entry.getKey());
            }

        }

        HaLog.info(log, MsgIdConstant.MS_INF_0002, "初始化spec转换规则配置");
    }

    /**
     * 获取spec配置存储map
     *
     * @return
     */
    public static Map<String, Chainr> getContextParam() {
        if (chainrContext == null || chainrContext.size() == 0) {
            loadConf();
        }
        return chainrContext;
    }

    /**
     * 获取spec转换对象
     *
     * @param key
     * @return
     */
    public static Chainr getchainrContext(String key) {
        if (chainrContext == null || chainrContext.size() == 0) {
            loadConf();
        }
        return chainrContext.get(key);
    }

}
