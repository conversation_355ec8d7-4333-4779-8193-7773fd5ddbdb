package com.air.security.sodb.data.receive.job;

import com.air.security.sodb.data.core.constant.GlobalCodeConstant;
import com.air.security.sodb.data.core.constant.MsgIdConstant;
import com.air.security.sodb.data.core.quartz.AbstractJob;
import com.air.security.sodb.data.core.spring.SpringUtil;
import com.air.security.sodb.data.core.util.*;
import com.air.security.sodb.data.receive.ifdomain.Meta;
import com.air.security.sodb.data.receive.server.MqMessageRecvServer;
import com.air.security.sodb.data.receive.server.MqMessageRecvServerImpl;
import com.air.security.sodb.data.receive.util.VideoEquStatusCache;
import com.alibaba.fastjson.JSONArray;
import com.alibaba.fastjson.JSONObject;
import com.rabbitmq.client.AMQP;
import com.rabbitmq.client.Channel;
import com.rabbitmq.client.DefaultConsumer;
import com.rabbitmq.client.Envelope;
import org.apache.commons.io.FileUtils;
import org.apache.commons.lang.StringUtils;
import org.quartz.JobDataMap;
import org.slf4j.Logger;
import org.slf4j.LoggerFactory;

import java.io.File;
import java.io.IOException;
import java.util.ArrayList;
import java.util.List;
import java.util.Map;

public class VideoStatusReqJob extends AbstractJob {

    private static final Logger log = LoggerFactory.getLogger(VideoStatusReqJob.class);

    // private static String uri =
    // PropertyUtil.getProperty("sodb.ec01.equ.status.req.url");

    private static String exchangeName = "cctv.gateway";

    private static String routeKey = PropertyUtil.getProperty("cctv.gateway.key");

    private static String requestPath = PropertyUtil.getProperty("camera.service.status.request.message.path");


//	private static String storageUri = PropertyUtil.getProperty("sodb.storage.status.req.url");

    private static Channel channel = null;

    private MqMessageRecvServer server = new MqMessageRecvServerImpl();

    @Override
    public void executeJob(JobDataMap paramMap) {
        HaLog.info(log, MsgIdConstant.MS_INF_0001, "摄像机状态查询");
        try {

            // 查询sodb摄像机数据
            Map<String, String> cacheMap = VideoEquStatusCache.getCacheMap();

            // 向流媒体发送摄像机状态同步请求
            List<VideoServiceStatusRequestBean> cacheList = VideoServiceStatusRequestBeanUtil.getCacheList();
            for (VideoServiceStatusRequestBean bean : cacheList) {
                bean.setSn(UuidUtil.getUuid32());
                publishMsg(bean, cacheMap);
            }
        } catch (Exception e) {
            HaLog.error(log, e, MsgIdConstant.MS_ERR_0001, "");
        } finally {
            HaLog.info(log, MsgIdConstant.MS_INF_0002, "摄像机状态查询");
        }
    }

    private void publishMsg(VideoServiceStatusRequestBean bean, Map<String, String> cacheMap) throws Exception {
        HaLog.debug(log, MsgIdConstant.MS_INF_0003, "摄像机状态查询请求", JSONObject.toJSONString(bean));
        channel = RabbitMqConsumerUtil.getInstance().getChannel();

        // 声明交换机类型
        channel.exchangeDeclare(exchangeName, "direct", true);
        String queueName = channel.queueDeclare().getQueue();
        channel.queueBind(queueName, exchangeName, routeKey);

        String sequence = UuidUtil.getUuid32();
        AMQP.BasicProperties props = new AMQP.BasicProperties.Builder().correlationId(sequence).replyTo(queueName)
                .build();

        channel.basicPublish(exchangeName, routeKey, props, JSONObject.toJSONString(bean).getBytes("UTF-8"));

        channel.basicConsume(queueName, true, new DefaultConsumer(channel) {
            @Override
            public void handleDelivery(String consumerTag, Envelope envelope, AMQP.BasicProperties properties,
                                       byte[] body) throws IOException {
                // 检查它的correlationId是否是我们所要找的那个
                // 如果是，则响应BlockingQueue
                // response.offer(new String(body, "UTF-8"));
                String result = new String(body, "UTF-8");
                ThreadUtil.runWithThreadPool(new Runnable() {

                    @Override
                    public void run() {
                        HaLog.debug(log, MsgIdConstant.MS_INF_0003, "摄像机状态查询", result);

                        JSONObject json = JSONObject.parseObject(result);
                        String servicetype = String.valueOf(json.get("servicetype"));

                        String cmdtype = String.valueOf(json.get("cmdtype"));
                        if (!"10038".equals(cmdtype)) {
                            return;
                        }

                        if (!"0".equals(servicetype)) {
                            return;
                        }

                        String serviceId = bean.getServiceid();
                        if (StringUtils.isBlank(serviceId)) {
                            return;
                        }
                        JSONArray statusArray = json.getJSONArray("status");
                        JSONArray inputBody = new JSONArray();

                        if (statusArray != null && !statusArray.isEmpty()) {
                            for (Object obj : statusArray) {
                                JSONObject jsonObj = (JSONObject) obj;

                                String channelCode = String.valueOf(jsonObj.get("deviceid"));
                                String resStatus = StringUtils.trim(String.valueOf(jsonObj.get("online")));
                                //获取摄像机的状态
                                String timeStateId = cacheMap.get(channelCode);
                                if (timeStateId == null) {
                                    continue;
                                }
                                String timeState = formatStatus(resStatus);
                                if (timeState.equals(timeStateId)) {
                                    continue;
                                }

                                VideoEquStatusCache.getCacheMap().remove(channelCode);
                                VideoEquStatusCache.getCacheMap().put(channelCode, resStatus);
                                JSONObject statusJson = new JSONObject();
                                statusJson.put("cameraid", channelCode);
                                statusJson.put("timeStateId", timeState);
                                statusJson.put("time", DateTimeUtil.getCurrentTimestampStr("yyyy-MM-dd HH:mm:ss"));
                                inputBody.add(statusJson);

                            }
                            HaLog.info(log, MsgIdConstant.MS_INF_0001, "1111>>>>>>" + inputBody.toString() + "   size" + inputBody.size());

                            Meta meta = new Meta();
                            meta.setEventType("SvmsEquStatusUe");
                            meta.setRecvSequence(UuidUtil.getUuid32());
                            server.handle(meta, inputBody.toString());
                        }

                    }
                });

            }
        });
        Thread.sleep(50L);
    }

    private String formatStatus(String onlineStatus) {
        if (GlobalCodeConstant.TRUE.equalsIgnoreCase(onlineStatus)) {
            onlineStatus = "ES01";
        } else {
            onlineStatus = "ES02";
        }
        return onlineStatus;
    }


    public static void main(String[] args) {

        // 初始化spring
        SpringUtil.initSpring();

        VideoStatusReqJob job = new VideoStatusReqJob();
        job.executeJob(null);
    }


    static class VideoServiceStatusRequestBeanUtil {

        private static List<VideoServiceStatusRequestBean> cacheList;

        public static void init() {
            if (null != cacheList) {
                return;
            }
            cacheList = new ArrayList<>();
            try {
                String message = FileUtils.readFileToString(new File(requestPath));

                List<JSONObject> tempContext = (List<JSONObject>) JSONArray.parse(message);
                for (JSONObject json : tempContext) {
                    VideoServiceStatusRequestBean bean = JSONObject.parseObject(json.toJSONString(),
                            VideoServiceStatusRequestBean.class);
                    cacheList.add(bean);
                }
            } catch (Exception e) {
                HaLog.error(log, e, MsgIdConstant.MS_ERR_0001, "");
            }
        }

        public static List<VideoServiceStatusRequestBean> getCacheList() {
            if (null == cacheList) {
                init();
            }
            return cacheList;
        }

    }

    static class VideoServiceStatusRequestBean {

        private String version;

        private String messagetype;

        private Integer cmdtype;

        private String sn;

        private String region;

        private String serviceid;

        private String serviceip;

        private Integer serviceport;

        public String getVersion() {
            return version;
        }

        public void setVersion(String version) {
            this.version = version;
        }

        public String getMessagetype() {
            return messagetype;
        }

        public void setMessagetype(String messagetype) {
            this.messagetype = messagetype;
        }

        public Integer getCmdtype() {
            return cmdtype;
        }

        public void setCmdtype(Integer cmdtype) {
            this.cmdtype = cmdtype;
        }

        public String getSn() {
            return sn;
        }

        public void setSn(String sn) {
            this.sn = sn;
        }

        public String getRegion() {
            return region;
        }

        public void setRegion(String region) {
            this.region = region;
        }

        public String getServiceid() {
            return serviceid;
        }

        public void setServiceid(String serviceid) {
            this.serviceid = serviceid;
        }

        public String getServiceip() {
            return serviceip;
        }

        public void setServiceip(String serviceip) {
            this.serviceip = serviceip;
        }

        public Integer getServiceport() {
            return serviceport;
        }

        public void setServiceport(Integer serviceport) {
            this.serviceport = serviceport;
        }

    }

}
