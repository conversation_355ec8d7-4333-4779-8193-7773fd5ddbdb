package com.air.security.sodb.data.core.quartz;

import com.air.security.sodb.data.core.constant.MsgIdConstant;
import com.air.security.sodb.data.core.util.HaLog;
import org.quartz.*;
import org.quartz.impl.StdSchedulerFactory;
import org.slf4j.Logger;
import org.slf4j.LoggerFactory;

import java.util.Date;
import java.util.List;

import static org.quartz.CronScheduleBuilder.cronSchedule;
import static org.quartz.JobBuilder.newJob;
import static org.quartz.TriggerBuilder.newTrigger;

/**
 * quartz的Job定时类，根据配置文件加载任务
 * <AUTHOR>
public class SchedulerManager {

    private static final Logger log = LoggerFactory.getLogger(SchedulerManager.class);

    /**
     * 启动定时任务
     *
     * @param fileName 配置文件
     */
    public void runJob(String fileName) {

        // 解析配置文件,获取所有任务配置
        List<Config> configLs = ConfigParser.parseXml(fileName);
        for (Config config : configLs) {
            if (config.getSwitchTrigger() == null || !("on").equals(config.getSwitchTrigger())) {
                continue;
            }
            // 启动定时器
            runJob(config);
        }
    }

    /**
     * 启动定时任务
     */
    public void runJob() {

        // 解析配置文件,获取所有任务配置
        List<Config> configLs = ConfigParser.parseXml(null);
        for (Config config : configLs) {
            if (config.getSwitchTrigger() == null || !("on").equals(config.getSwitchTrigger())) {
                continue;
            }
            // 启动定时器
            runJob(config);
        }
    }

    /**
     * 读取配置文件，执行定时任务
     * @param config
     */
    @SuppressWarnings("unchecked")
    public void runJob(Config config) {
        HaLog.info(log, MsgIdConstant.MS_INF_0001, "quartz定时任务");
        SchedulerFactory sf = new StdSchedulerFactory();
        Scheduler sched = null;
        try {
            sched = sf.getScheduler();

            Date runTime = DateBuilder.nextGivenSecondDate(null, 10);

            Class<? extends Job> jobClass = (Class<? extends Job>) Class.forName(config.getJobClass());
            JobDetail jobDatil = newJob(jobClass).withIdentity(config.getJobName(), config.getJobGroupName()).build();

            CronTrigger trigger = newTrigger().withIdentity(config.getTriggerName(), config.getTriggerGroup())
                    .withSchedule(cronSchedule(config.getTriggerCron())).startAt(runTime).build();

            sched.scheduleJob(jobDatil, trigger);
            HaLog.info(log, jobDatil.getKey() + " will run at: " + runTime);

            sched.start();
            HaLog.info(log, MsgIdConstant.MS_INF_0002, "quartz定时任务");

        } catch (SchedulerException e) {
            HaLog.error(log, e, MsgIdConstant.MS_ERR_0001, "quartz加载Job任务失败");
            try {
                sched.shutdown();
            } catch (SchedulerException e1) {
                HaLog.error(log, e, MsgIdConstant.MS_ERR_0001, "sched.shutdown()失败");
            }
        } catch (ClassNotFoundException e) {
            HaLog.error(log, e, MsgIdConstant.MS_ERR_0001, "根据配置文件映射job实现类出现错误");
        }
    }

    /**
     * 关闭定时任务
     */
    public void shutJob() {

        // 解析配置文件,获取所有任务配置
        List<Config> configLs = ConfigParser.parseXml(null);
        for (Config config : configLs) {
            if (config.getSwitchTrigger() == null || !("on").equals(config.getSwitchTrigger())) {
                continue;
            }
            // 启动定时器
            shutJob(config);
        }
    }

    /**
     * 读取配置文件，停止定时任务
     * @param config
     */
    public void shutJob(Config config) {
        HaLog.info(log, MsgIdConstant.MS_INF_0002, "quartz定时任务");
        SchedulerFactory sf = new StdSchedulerFactory();
        Scheduler sched = null;
        try {
            sched = sf.getScheduler();
            sched.shutdown();
            HaLog.info(log, MsgIdConstant.MS_INF_0002, "quartz定时任务成功");

        } catch (SchedulerException e) {
            HaLog.error(log, MsgIdConstant.MS_ERR_0001, e, "quartz加载Job任务失败");
            try {
                sched.shutdown();
            } catch (SchedulerException e1) {
                HaLog.error(log, MsgIdConstant.MS_ERR_0001, e, "sched.shutdown()失败");
            }
        }
    }

}
