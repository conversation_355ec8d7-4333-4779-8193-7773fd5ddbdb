package com.air.security.sodb.data.core.spring;

import org.slf4j.Logger;
import org.slf4j.LoggerFactory;
import org.springframework.context.ApplicationContext;
import org.springframework.context.support.ClassPathXmlApplicationContext;

import com.air.security.sodb.data.core.constant.MsgIdConstant;
import com.air.security.sodb.data.core.util.HaLog;

/**
 * @Description: spring工具类
 * @author: zhangc
 * @Date: 2018年12月6日
 */
public class SpringUtil {

    private static final Logger log = LoggerFactory.getLogger(SpringUtil.class);

    /**
     * 初始化spring容器
     */
    @SuppressWarnings({"resource", "unused"})
    public static void initSpring() {
        HaLog.info(log, MsgIdConstant.MS_INF_0001, "初始化Spring容器");

        ApplicationContext context = new ClassPathXmlApplicationContext("classpath*:/spring/spring-*.xml");

        HaLog.info(log, MsgIdConstant.MS_INF_0002, "初始化Spring容器");
    }
}
