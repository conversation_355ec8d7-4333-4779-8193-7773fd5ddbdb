package com.air.security.sodb.data.release.service.video;

import com.air.security.sodb.data.core.constant.GlobalCodeConstant;
import com.air.security.sodb.data.core.constant.MsgIdConstant;
import com.air.security.sodb.data.core.exception.SystemBaseException;
import com.air.security.sodb.data.core.util.HaLog;
import com.air.security.sodb.data.core.util.PropertyUtil;
import com.alibaba.fastjson.JSONObject;
import org.apache.commons.io.FileUtils;
import org.slf4j.Logger;
import org.slf4j.LoggerFactory;

import java.io.File;
import java.io.IOException;
import java.util.HashMap;
import java.util.Map;

/**
 * 机场三字码-名称对照配置
 *
 * <AUTHOR>
 */
public class VideoDataCodeMapConfigContext {

    private static final Logger log = LoggerFactory.getLogger(VideoDataCodeMapConfigContext.class);

    private static Map<String, String> videoDataCodeMap = null;

    @SuppressWarnings("unchecked")
    public static void loadConf() {
        HaLog.info(log, MsgIdConstant.MS_INF_0001, "流媒体接口code-与sodb共享数据类型对应映射配置");

        videoDataCodeMap = new HashMap<String, String>(1);
        String configPath = PropertyUtil.getProperty("video.data.code.map.path");
        File configFile = new File(configPath);

        if (!configFile.exists()) {
            HaLog.info(log, MsgIdConstant.MS_INF_0009, "流媒体接口code-与sodb共享数据类型对应映射配置加载", "未找到配置文件");
            throw new SystemBaseException("流媒体接口code-与sodb共享数据类型对应映射配置, 未找到配置文件");
        }

        try {
            String configString = FileUtils.readFileToString(configFile, GlobalCodeConstant.FILE_ENCODE);

            videoDataCodeMap = (Map<String, String>) JSONObject.parse(configString);

        } catch (IOException e) {
            HaLog.error(log, e, MsgIdConstant.MS_ERR_0001);
        }

        HaLog.info(log, MsgIdConstant.MS_INF_0002, "流媒体接口code-与sodb共享数据类型对应映射配置");
    }

    /**
     * 获取流媒体接口code-与sodb共享数据类型对应映射配置map
     *
     * @return
     * @throws IOException
     */
    public static Map<String, String> getContextParam() {
        if (videoDataCodeMap == null || videoDataCodeMap.size() == 0) {
            loadConf();
        }
        return videoDataCodeMap;
    }

}
