package com.air.security.sodb.data.release.server;

import org.apache.commons.lang.StringUtils;
import org.slf4j.Logger;
import org.slf4j.LoggerFactory;

import com.air.security.sodb.data.core.constant.GlobalCodeConstant;
import com.air.security.sodb.data.core.constant.MsgIdConstant;
import com.air.security.sodb.data.core.exception.SystemBaseException;
import com.air.security.sodb.data.core.spring.ApplicationContextUtil;
import com.air.security.sodb.data.core.util.DataTransferLogRecordUtil;
import com.air.security.sodb.data.core.util.DateTimeUtil;
import com.air.security.sodb.data.core.util.HaLog;
import com.air.security.sodb.data.core.util.PropertyUtil;
import com.air.security.sodb.data.receive.ifdomain.Meta;
import com.air.security.sodb.data.release.service.MessageReleaseService;
import com.alibaba.fastjson.JSONObject;

/**
 * @Description: 消息数据的分发处理类
 * @author: zhangc
 * @Date: 2018年12月5日
 */
public class KafkaMessageRecvServerImpl implements KafkaMessageRecvServer {

    private static final Logger log = LoggerFactory.getLogger(KafkaMessageRecvServerImpl.class);

    /**
     * 消息分发处理
     */
    @Override
    public void handle(Meta meta, JSONObject messageJson) {
        HaLog.info(log, MsgIdConstant.MS_INF_0001, meta.getEventType());

        String prop = null;

        try {

            String eventType = meta.getEventType();
            prop = PropertyUtil.getProperty(eventType);

            if (StringUtils.isBlank(prop)) {
                HaLog.warn(log, MsgIdConstant.MS_WAR_0002, "消息类型配置文件的配置");
                return;
            }

            MessageReleaseService service = (MessageReleaseService) ApplicationContextUtil.getBean(prop);

            service.execute(meta, messageJson);

            Thread.sleep(10L);

        } catch (SystemBaseException e1) {
            HaLog.error(log, MsgIdConstant.MS_ERR_0001, e1, meta.getEventType());
            throw e1;
        } catch (Exception e2) {
            HaLog.error(log, MsgIdConstant.MS_ERR_0001, e2, meta.getEventType());
            throw new SystemBaseException(e2);
        } finally {
            HaLog.info(log, MsgIdConstant.MS_INF_0002, meta.getEventType());
        }
    }

}
