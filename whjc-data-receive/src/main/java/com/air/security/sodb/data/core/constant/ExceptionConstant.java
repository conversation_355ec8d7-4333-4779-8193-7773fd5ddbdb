package com.air.security.sodb.data.core.constant;

/**
 * @Description: 异常参数常量类
 * @author: zhangc
 * @Date: 2018年12月17日
 */
public class ExceptionConstant {

    /**
     * spec配置不合规则，无法转为map
     */
    public static final String SPEC_CONF_IS_NOT_MAPJSON_EXCEPTION = "spec配置不合规则，无法转为map";

    /**
     * input输入不合规则，无法转为map
     */
    public static final String INPUT_IS_NOT_MAPJOSN_EXCEPTION = "input输入不合规则，无法转为map";

    /**
     * spec数据字典转换配置异常
     */
    public static final String SPEC_DATA_CODE_CONF_EXCEPTION = "spec数据字典转换配置异常";

}
