package com.air.security.sodb.data.receive.service.video;

import com.air.security.sodb.data.core.constant.MsgIdConstant;
import com.air.security.sodb.data.core.exception.SystemBaseException;
import com.air.security.sodb.data.core.util.HaLog;
import com.air.security.sodb.data.receive.ifdomain.Meta;
import com.air.security.sodb.data.receive.service.AbstractMessageRecvService;
import com.alibaba.fastjson.JSONArray;
import com.alibaba.fastjson.JSONObject;
import org.apache.commons.lang3.StringUtils;
import org.slf4j.Logger;
import org.slf4j.LoggerFactory;
import org.springframework.stereotype.Service;

import java.util.List;
/**
 * 摄像机设备 实现类
 * <AUTHOR>
@Service("VideoEquStatusRecvFormatServiceImpl")
public class VideoEquStatusRecvFormatServiceImpl extends AbstractMessageRecvService {

    private static final Logger log = LoggerFactory.getLogger(VideoEquStatusRecvFormatServiceImpl.class);

    @Override
    public void execute(Meta meta, String messageBody) throws SystemBaseException {
        HaLog.info(log, MsgIdConstant.MS_INF_0001, meta.getEventType());

        // 获取到的json业务数据
        JSONArray inputJson = JSONArray.parseArray(messageBody);

        JSONArray inputBody = new JSONArray();

        List<VideoEquStatus> cacheList = VideoEquStatusCache.getCacheList();

        // 遍历资源状态在线数据输入列表，获取离线变在线设备变更信息
        for (Object object : inputJson) {
            JSONObject jsonObj = (JSONObject) object;

            removeKey(jsonObj);

            String cameraid = jsonObj.getString("cameraid");
            VideoEquStatus status = new VideoEquStatus();
            status.setEquCode(cameraid);
            int index = cacheList.indexOf(status);
            if (index == -1) {
                status.setTimeStateId("ES01");
                VideoEquStatusCache.getCacheList().add(status);
                inputBody.add(status);
            } else {

                // 摄像机原始状态为离线则更新为在线，并将变更信息封装到输入json中
                if (StringUtils.equals("ES02", VideoEquStatusCache.getCacheList().get(index).getTimeStateId())) {
                    status.setTimeStateId("ES01");
                    VideoEquStatusCache.getCacheList().get(index).setTimeStateId("ES01");
                    inputBody.add(status);
                }
            }
        }

        // 遍历资源状态缓存列表，获取在线变离线设备变更信息
        for (int i = 0, l = cacheList.size(); i < l; i++) {
            VideoEquStatus status = cacheList.get(i);
            JSONObject json = new JSONObject();
            json.put("cameraid", status.getEquCode());

            // 原始状态为在线，且获取的在线设计列表中不包含该设备，则更新为离线
            if (!inputJson.contains(json) && StringUtils.equals("ES01", status.getTimeStateId())) {

                VideoEquStatusCache.getCacheList().get(i).setTimeStateId("ES02");
                inputBody.add(status);
            }
        }

        // 响应json消息
        JSONObject outputMsg = new JSONObject();

        // 响应消息头
        outputMsg.put("meta", meta);

        // 响应消息体
        outputMsg.put("body", inputBody);

        // 转换为JSON格式，并发送消息到指定主题
        super.putSendMessage(outputMsg);

        HaLog.info(log, MsgIdConstant.MS_INF_0002, meta.getEventType());
    }

    private static void removeKey(JSONObject jsonObj) {
        jsonObj.remove("channelnum");
        jsonObj.remove("clientip");
        jsonObj.remove("clientport");
        jsonObj.remove("cmdtype");
        jsonObj.remove("createtime");
        jsonObj.remove("deviceusername");
        jsonObj.remove("deviceuserpasswd");
        jsonObj.remove("ipaddress");
        jsonObj.remove("mainstreamname");
        jsonObj.remove("mport");
        jsonObj.remove("platformtype");
        jsonObj.remove("port");
        jsonObj.remove("serviceid");
        jsonObj.remove("serviceip");
        jsonObj.remove("serviceport");
        jsonObj.remove("startwayofstorage");
        jsonObj.remove("statustype");
        jsonObj.remove("transmitway");
        jsonObj.remove("uri");
        jsonObj.remove("userid");
        jsonObj.remove("username");
        jsonObj.remove("uuid");
        jsonObj.remove("uuid2");
    }

}
