package com.air.security.sodb.data.receive.listener;

import com.air.security.sodb.data.core.constant.MsgIdConstant;
import com.air.security.sodb.data.core.util.HaLog;
import com.air.security.sodb.data.receive.ifdomain.Meta;
import com.air.security.sodb.data.receive.server.MqMessageRecvServer;
import com.air.security.sodb.data.receive.server.MqMessageRecvServerImpl;
import com.airport.dwh.tlq.api.TlqGetMsgApi;
import com.tongtech.tlq.base.TlqException;
import org.dom4j.Document;
import org.dom4j.DocumentException;
import org.dom4j.DocumentHelper;
import org.dom4j.Node;
import org.slf4j.Logger;
import org.slf4j.LoggerFactory;

/**
 * @Description: Tlq消息监听
 * <AUTHOR>
public class TlqMessageListener implements Runnable {

    private static final Logger log = LoggerFactory.getLogger(TlqMessageListener.class);

    /**
     * 消息接收处理器
     */
    private static MqMessageRecvServer server = new MqMessageRecvServerImpl();

    private static final String XML_META_PATH = "MSG/META/STYP";


    @Override
    public void run() {
        HaLog.info(log, MsgIdConstant.MS_INF_0001, "tlq监听");
        //接收Tlq消息
        TlqGetMsgApi getMsgApi = TlqGetClientInitialize.getInstance().getGetClient();
        while (true) {

            //获取消息
            try {
                String msg = getMsgApi.getMsg();
                if (null == msg) {
                    continue;
                }

                HaLog.info(log, MsgIdConstant.MS_INF_0003, "Tlq监听", msg);

                //解析消息
                Document document = DocumentHelper.parseText(msg);

                //获取节点消息
                Node node = document.selectSingleNode(XML_META_PATH);

                //获取消息类型
                String styp = node.getStringValue();

                //消息头标签
                Meta meta = new Meta();

                //设置消息类型
                meta.setEventType(styp);

                //消息数据的分发处理
                server.handle(meta, msg);

                Thread.sleep(10L);
            } catch (TlqException e) {
                HaLog.error(log, e, MsgIdConstant.MS_ERR_0010, "Tlq消息异常");
            } catch (InterruptedException e) {
                HaLog.error(log, e, MsgIdConstant.MS_ERR_0001);
            } catch (DocumentException e) {
                HaLog.error(log, e, MsgIdConstant.MS_WAR_0002, "获取节点信息异常");
            }
        }
    }
}
