package com.air.security.sodb.data.release.service.video;

import com.air.security.sodb.data.core.config.FormatHandleRecvConfig;
import com.air.security.sodb.data.core.constant.MsgIdConstant;
import com.air.security.sodb.data.core.exception.SystemBaseException;
import com.air.security.sodb.data.core.util.*;
import com.air.security.sodb.data.receive.ifdomain.Meta;
import com.air.security.sodb.data.release.service.AbstractMessageReleaseService;
import com.alibaba.fastjson.JSONObject;
import com.bazaarvoice.jolt.Chainr;
import org.apache.commons.lang3.StringUtils;
import org.slf4j.Logger;
import org.slf4j.LoggerFactory;
import org.springframework.stereotype.Service;

import java.util.HashMap;
import java.util.Map;
/**
 * 摄像机设备 实现类
 * <AUTHOR>
@Service("VideoInfoMessageReleaseServiceImpl")
public class VideoInfoMessageReleaseServiceImpl extends AbstractMessageReleaseService {

    private static final Logger log = LoggerFactory.getLogger(VideoInfoMessageReleaseServiceImpl.class);

    private static String account = PropertyUtil.getProperty("video.login.account");
    private static String url = PropertyUtil.getProperty("video.rest.url");

    private static String PLATFORM_INFO = "PLATFORM_INFO";

    @Override
    public void execute(Meta meta, JSONObject messageJson) throws SystemBaseException {
        HaLog.info(log, MsgIdConstant.MS_INF_0001, meta.getEventType() + "消息发送");
        try {
            String eventType = meta.getEventType();
            // 获取消息格式化处理的配置
            FormatHandleRecvConfig config = FormatHandleRecvConfigContextUtil.getConfig(eventType);

            // spec转换配置
            Chainr chainr = ChainrParamContextUtil.getchainrContext(config.getEventType());

            // 获取到的json业务数据
            Object inputObj = messageJson.get("body");

            // 获取转换结果
            Object transformJson = chainr.transform(inputObj);

            JSONObject inputJson = JSONObject.parseObject(JSONObject.toJSONString(transformJson));

            String operationType = inputJson.getString("operationType");
            String key = eventType + "_" + operationType;
            String code = VideoDataCodeMapConfigContext.getContextParam().get(key);

            JSONObject paramJson = new JSONObject();
            paramJson.put("code", code);
            paramJson.put("params", transformJson);

            JSONObject authorJson = new JSONObject();
            authorJson.put("loginAccount", account);
            System.out.println(paramJson.toJSONString());
            Map<String, String> params = new HashMap<String, String>(2);
            params.put("authorJson", authorJson.toJSONString());
            params.put("parmJson", paramJson.toJSONString());

            HaLog.info(log, MsgIdConstant.MS_INF_0009, "接口请求参数", JSONObject.toJSONString(params));
            String result = HttpClientUtil.post(url, params);
            HaLog.info(log, MsgIdConstant.MS_INF_0009, "消息发送", result);

            // 平台信息，提取平台信息中的用户信息推送到流媒体
            if (StringUtils.equals(PLATFORM_INFO, meta.getEventType())) {
                String userEvent = "USER_INFO";

                // 获取消息格式化处理的配置
                FormatHandleRecvConfig userConfig = FormatHandleRecvConfigContextUtil.getConfig(userEvent);

                // spec转换配置
                Chainr userChainr = ChainrParamContextUtil.getchainrContext(userConfig.getEventType());

                // 获取转换结果
                Object userTransformJson = userChainr.transform(inputObj);

                String userKey = userEvent + "_" + operationType;
                String userCode = VideoDataCodeMapConfigContext.getContextParam().get(userKey);

                paramJson.put("code", userCode);
                paramJson.put("params", JSONObject.toJSONString(userTransformJson));
                params.put("parmJson", paramJson.toJSONString());
                result = HttpClientUtil.post(url, params);

                HaLog.info(log, MsgIdConstant.MS_INF_0009, "用户消息发送", result);
            }

            HaLog.info(log, MsgIdConstant.MS_INF_0002, meta.getEventType() + "消息发送");
        } catch (Exception e) {

            HaLog.error(log, e, MsgIdConstant.MS_ERR_0001, "");

        }

    }

}
