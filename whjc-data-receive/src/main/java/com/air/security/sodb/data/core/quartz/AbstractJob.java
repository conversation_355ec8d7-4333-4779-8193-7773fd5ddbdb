package com.air.security.sodb.data.core.quartz;

import org.quartz.Job;
import org.quartz.JobDataMap;
import org.quartz.JobExecutionContext;
import org.quartz.JobExecutionException;
import org.slf4j.Logger;
import org.slf4j.LoggerFactory;

import com.air.security.sodb.data.core.constant.MsgIdConstant;
import com.air.security.sodb.data.core.util.HaLog;

/**
 * 定时任务抽象类
 * <AUTHOR>
public abstract class AbstractJob implements Job {

    private static final Logger log = LoggerFactory.getLogger(AbstractJob.class);

    @Override
    public void execute(JobExecutionContext context) throws JobExecutionException {

        // 获取工作任务数据Map集合
        JobDataMap paramMap = context.getJobDetail().getJobDataMap();

        try {
            executeJob(paramMap);
        } catch (Exception e) {
            HaLog.error(log, MsgIdConstant.MS_ERR_0001, e, "调度任务出错！");
        }
    }

    /**
     * 定时任务执行方法
     *
     * @param paramMap 相关参数
     */
    public abstract void executeJob(JobDataMap paramMap);

}
