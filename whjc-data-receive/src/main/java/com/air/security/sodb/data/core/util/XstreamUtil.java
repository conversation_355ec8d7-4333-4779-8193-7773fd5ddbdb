package com.air.security.sodb.data.core.util;

import com.thoughtworks.xstream.XStream;

/**
 * @Description:
 * @Author: xus
 * @Date: 2018/5/26 15:27
 */
public class XstreamUtil {
    /**
     * 将事件消息转为xml字符串
     *
     * @param clazz
     * @param sendObject
     * @return
     */
    public static String toXml(Class<?> clazz, Object sendObject) {
        XStream xStream = new XStream();
        xStream.processAnnotations(clazz);
        String msg = xStream.toXML(sendObject);
        return msg;
    }

    /**
     * xml字符串转object
     *
     * @param clazz
     * @param xmlMsg
     * @return
     */
    public static Object fromXml(Class<?> clazz, String xmlMsg) {
        XStream xStream = new XStream();
        xStream.processAnnotations(clazz);
        Object object = xStream.fromXML(xmlMsg);
        return object;
    }
}
