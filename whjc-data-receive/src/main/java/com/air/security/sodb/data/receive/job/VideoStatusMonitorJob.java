package com.air.security.sodb.data.receive.job;

import com.air.security.sodb.data.core.constant.MsgIdConstant;
import com.air.security.sodb.data.core.quartz.AbstractJob;
import com.air.security.sodb.data.core.util.*;
import com.air.security.sodb.data.receive.ifdomain.Meta;
import com.air.security.sodb.data.receive.server.MqMessageRecvServer;
import com.air.security.sodb.data.receive.server.MqMessageRecvServerImpl;
import com.alibaba.fastjson.JSONArray;
import com.alibaba.fastjson.JSONObject;
import org.apache.commons.lang3.StringUtils;
import org.quartz.JobDataMap;
import org.slf4j.Logger;
import org.slf4j.LoggerFactory;

import java.net.InetAddress;
/**
 * 流媒体服务状态通知采集任务 定时任务
 * <AUTHOR>
public class VideoStatusMonitorJob extends AbstractJob {

    private static final Logger log = LoggerFactory.getLogger(VideoStatusMonitorJob.class);

    private static String uri = PropertyUtil.getProperty("sodb.ec01.equ.status.req.url");
    private static int timeOut = Integer.parseInt(PropertyUtil.getProperty("ping.timeout"));
    private static int pingInterval = Integer.parseInt(PropertyUtil.getProperty("ping.interval"));

    private MqMessageRecvServer server = new MqMessageRecvServerImpl();

    @Override
    public void executeJob(JobDataMap paramMap) {
        HaLog.info(log, MsgIdConstant.MS_INF_0001, "流媒体服务状态通知采集任务");
        try {
            String result = HttpRequestUtil.sendPost(uri, "");
            JSONObject resJson = JSONObject.parseObject(result);
            JSONArray statusArray = resJson.getJSONArray("datalist");
            for (Object object : statusArray) {
                JSONObject statusJson = (JSONObject) object;
                ThreadUtil.runWithThreadPool(new Runnable() {
                    @Override
                    public void run() {
                        try {
                            String equIp = statusJson.getString("equIp");
                            String timeStateId = statusJson.getString("timeStateId");
                            boolean isConnected = InetAddress.getByName(equIp).isReachable(timeOut);

                            if (!isConnected) {
                                isConnected = InetAddress.getByName(equIp).isReachable(timeOut);
                            }

                            // 网络连通，当前状态非在线
                            if (isConnected && !StringUtils.equals(timeStateId, "ES01")) {
                                statusJson.put("timeStateId", "ES01");
                                statusJson.put("cameraid", statusJson.getString("equCode"));
                                statusJson.put("time", DateTimeUtil.getCurrentTimestampStr("yyyy-MM-dd HH:mm:ss"));
                                Meta meta = new Meta();
                                meta.setEventType("SvmsEquStatusUe");
                                meta.setRecvSequence(UuidUtil.getUuid32());
                                server.handle(meta, statusJson.toJSONString());
                            } else if (!isConnected && !StringUtils.equals(timeStateId, "ES02")) {
                                // 网络不通，当前状态非离线
                                statusJson.put("timeStateId", "ES02");
                                statusJson.put("time", DateTimeUtil.getCurrentTimestampStr("yyyy-MM-dd HH:mm:ss"));
                                statusJson.put("cameraid", statusJson.getString("equCode"));
                                Meta meta = new Meta();
                                meta.setEventType("SvmsEquStatusUe");
                                meta.setRecvSequence(UuidUtil.getUuid32());
                                server.handle(meta, statusJson.toJSONString());
                            }
                            ThreadUtil.sleepThread(pingInterval);
                        } catch (Exception e) {
                            HaLog.error(log, e, MsgIdConstant.MS_ERR_0001, "");
                        }
                    }
                });
            }
        } catch (Exception e) {
            HaLog.error(log, e, MsgIdConstant.MS_ERR_0001, "");
        } finally {
            HaLog.info(log, MsgIdConstant.MS_INF_0002, "流媒体服务状态通知采集任务");
        }
    }

}
