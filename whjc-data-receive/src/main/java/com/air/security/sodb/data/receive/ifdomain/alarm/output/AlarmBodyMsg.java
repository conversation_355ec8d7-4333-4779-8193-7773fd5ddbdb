package com.air.security.sodb.data.receive.ifdomain.alarm.output;

import org.springframework.format.annotation.DateTimeFormat;

import com.alibaba.fastjson.annotation.JSONField;

/**
 * @Description: 报警事件输出的内容体信息
 * @author: zhangc
 * @Date: 2018年12月6日
 */
public class AlarmBodyMsg {

    /**
     * 报警名称
     */
    private String alarmName;

    /**
     * 报警分类编码
     */
    private String alarmClassCode;

    /**
     * 报警分类名称
     */
    private String alarmClassName;

    /**
     * 报警类型编码
     */
    private String alarmTypeCode;

    /**
     * 报警类型名称
     */
    private String alarmTypeName;

    /**
     * 报警级别编码
     */
    private String alarmLevelCode;

    /**
     * 报警级别名称
     */
    private String alarmLevelName;

    /**
     * 报警时间
     */
    @JSONField(format = "yyyy-MM-dd HH:mm:ss")
    @DateTimeFormat(pattern = "yyyy-MM-dd HH:mm:ss")
    private String alarmTime;

    /**
     * 报警设备名称
     */
    private String alarmEquName;

    /**
     * 报警设备编码
     */
    private String alarmEquCode;

    /**
     * @return 报警名称
     */
    public String getAlarmName() {
        return alarmName;
    }

    /**
     * @param 报警名称
     */
    public void setAlarmName(String alarmName) {
        this.alarmName = alarmName;
    }

    /**
     * @return 报警分类编码
     */
    public String getAlarmClassCode() {
        return alarmClassCode;
    }

    /**
     * @param 报警分类编码
     */
    public void setAlarmClassCode(String alarmClassCode) {
        this.alarmClassCode = alarmClassCode;
    }

    /**
     * @return 报警分类名称
     */
    public String getAlarmClassName() {
        return alarmClassName;
    }

    /**
     * @param 报警分类名称
     */
    public void setAlarmClassName(String alarmClassName) {
        this.alarmClassName = alarmClassName;
    }

    /**
     * @return 报警类型编码
     */
    public String getAlarmTypeCode() {
        return alarmTypeCode;
    }

    /**
     * @param 报警类型编码
     */
    public void setAlarmTypeCode(String alarmTypeCode) {
        this.alarmTypeCode = alarmTypeCode;
    }

    /**
     * @return 报警类型名称
     */
    public String getAlarmTypeName() {
        return alarmTypeName;
    }

    /**
     * @param 报警类型名称
     */
    public void setAlarmTypeName(String alarmTypeName) {
        this.alarmTypeName = alarmTypeName;
    }

    /**
     * @return 报警级别编码
     */
    public String getAlarmLevelCode() {
        return alarmLevelCode;
    }

    /**
     * @param 报警级别编码
     */
    public void setAlarmLevelCode(String alarmLevelCode) {
        this.alarmLevelCode = alarmLevelCode;
    }

    /**
     * @return 报警级别名称
     */
    public String getAlarmLevelName() {
        return alarmLevelName;
    }

    /**
     * @param 报警级别名称
     */
    public void setAlarmLevelName(String alarmLevelName) {
        this.alarmLevelName = alarmLevelName;
    }

    /**
     * @return 报警时间
     */
    public String getAlarmTime() {
        return alarmTime;
    }

    /**
     * @param 报警时间
     */
    public void setAlarmTime(String alarmTime) {
        this.alarmTime = alarmTime;
    }

    /**
     * @return 设备名称
     */
    public String getAlarmEquName() {
        return alarmEquName;
    }

    /**
     * @param 设备名称
     */
    public void setAlarmEquName(String alarmEquName) {
        this.alarmEquName = alarmEquName;
    }

    /**
     * @return 设备编码
     */
    public String getAlarmEquCode() {
        return alarmEquCode;
    }

    /**
     * @param 设备编码
     */
    public void setAlarmEquCode(String alarmEquCode) {
        this.alarmEquCode = alarmEquCode;
    }


    @Override
    public String toString() {
        return "AlarmBodyMsg [alarmName=" + alarmName + ", alarmClassCode=" + alarmClassCode + ", alarmClassName="
                + alarmClassName + ", alarmTypeCode=" + alarmTypeCode + ", alarmTypeName=" + alarmTypeName
                + ", alarmLevelCode=" + alarmLevelCode + ", alarmLevelName=" + alarmLevelName + ", alarmTime="
                + alarmTime + ", alarmEquName=" + alarmEquName + ", alarmEquCode=" + alarmEquCode + "]";
    }

}
