package com.air.security.sodb.data.receive.server;

import com.air.security.sodb.data.core.exception.SystemBaseException;
import com.air.security.sodb.data.receive.ifdomain.RecvHeader;

/**
 * @Description: 同步服务数据的分发处理接口
 * @author: zhangc
 * @Date: 2018年12月5日
 */
public interface SyncSrvcMessageRecvServer {

    /**
     * 消息分发处理
     *
     * @param meta
     * @param messageBody
     * @throws SystemBaseException
     */
    void handle(RecvHeader meta, String messageBody) throws SystemBaseException;
}
