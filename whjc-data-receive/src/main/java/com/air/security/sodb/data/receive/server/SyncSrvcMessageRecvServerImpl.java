package com.air.security.sodb.data.receive.server;

import com.air.security.sodb.data.core.constant.GlobalCodeConstant;
import com.air.security.sodb.data.core.constant.MsgIdConstant;
import com.air.security.sodb.data.core.exception.SystemBaseException;
import com.air.security.sodb.data.core.spring.ApplicationContextUtil;
import com.air.security.sodb.data.core.util.*;
import com.air.security.sodb.data.receive.ifdomain.Meta;
import com.air.security.sodb.data.receive.ifdomain.RecvHeader;
import com.air.security.sodb.data.receive.service.MessageRecvService;
import com.alibaba.fastjson.JSONArray;
import com.alibaba.fastjson.JSONObject;
import org.apache.commons.lang.StringUtils;
import org.slf4j.Logger;
import org.slf4j.LoggerFactory;

/**
 * @Description:
 * @author: zhangc
 * @Date: 2019年4月9日
 */
public class SyncSrvcMessageRecvServerImpl implements SyncSrvcMessageRecvServer {

    private static final Logger log = LoggerFactory.getLogger(SyncSrvcMessageRecvServerImpl.class);

    /**
     * 服务实现
     * @param recvHeader
     * @param messageBody
     * @throws SystemBaseException
     */
    @Override
    public void handle(RecvHeader recvHeader, String messageBody) throws SystemBaseException {
        HaLog.info(log, MsgIdConstant.MS_INF_0001, recvHeader.getSvcServiceCode());

        HaLog.infoJson(log, JSONObject.toJSONString(recvHeader));
        HaLog.infoJson(log, messageBody);

        String[] typeProp = null;
        try {

            String eventType = PropertyUtil.getProperty(recvHeader.getSvcServiceCode());

            if (StringUtils.isBlank(eventType)) {
                HaLog.warn(log, MsgIdConstant.MS_WAR_0002, recvHeader.getSvcServiceCode() + "消息类型配置文件的配置");
                return;
            }

            typeProp = eventType.split(",");
            int len = 4;
            if (typeProp == null || typeProp.length < len) {
                HaLog.warn(log, MsgIdConstant.MS_WAR_0003, "消息类型配置文件的配置");
                return;
            }

            MessageRecvService service = (MessageRecvService) ApplicationContextUtil.getBean(typeProp[0]);

            Meta meta = new Meta();
            meta.setSequence(UuidUtil.getUuid32());

            // 编辑消息头
            String[] topics = typeProp[1].split("\\|");
            String[] eventTypes = typeProp[2].split("\\|");
            String[] msgTypes = typeProp[3].split("\\|");
            for (int i = 0, l = topics.length; i < l; i++) {

                EditMetaUtil.editMeta(eventTypes[i], msgTypes[i], DateTimeUtil.getCurrentTimestampStr("yyyyMMddHHmmss"),
                        meta);

                service.execute(meta, messageBody);

                // 发送消息到对应的kafka主题
                JSONObject sendKafkaMsg = service.pollSendMessage();
                Object body = sendKafkaMsg.get("body");
                if (body instanceof JSONArray) {
                    JSONArray bodyArray = (JSONArray) body;
                    for (Object object : bodyArray) {
                        sendKafkaMsg.put("body", object);
                        KafkaProducerUtil.send(topics[i], JSONObject.toJSONString(sendKafkaMsg));
                        DataTransferLogRecordUtil.record(meta, sendKafkaMsg, GlobalCodeConstant.LOG_TYPE_FRONT_RECV,
                                GlobalCodeConstant.LOG_SERVICE_TYPE_RECV);
                    }
                } else {
                    KafkaProducerUtil.send(topics[i], JSONObject.toJSONString(sendKafkaMsg));

                    // 记录数据交互日志
                    DataTransferLogRecordUtil.record(meta, sendKafkaMsg, GlobalCodeConstant.LOG_TYPE_FRONT_RECV,
                            GlobalCodeConstant.LOG_SERVICE_TYPE_RECV);
                }

                Thread.sleep(100L);
            }

            Thread.sleep(10L);

        } catch (SystemBaseException e1) {
            HaLog.error(log, MsgIdConstant.MS_ERR_0001, e1, typeProp.toString());
            throw e1;
        } catch (Exception e2) {
            HaLog.error(log, MsgIdConstant.MS_ERR_0001, e2, typeProp.toString());
            throw new SystemBaseException(e2);
        } finally {
            HaLog.info(log, MsgIdConstant.MS_INF_0002, recvHeader.getSvcServiceCode());
        }
    }

}
