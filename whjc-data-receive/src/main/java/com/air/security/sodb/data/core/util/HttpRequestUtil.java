package com.air.security.sodb.data.core.util;

import com.air.security.sodb.data.core.constant.GlobalCodeConstant;
import com.air.security.sodb.data.core.constant.MsgIdConstant;
import com.alibaba.fastjson.JSONObject;
import org.apache.commons.io.IOUtils;
import org.apache.commons.lang.StringUtils;
import org.apache.http.HttpEntity;
import org.apache.http.HttpResponse;
import org.apache.http.NameValuePair;
import org.apache.http.StatusLine;
import org.apache.http.client.entity.UrlEncodedFormEntity;
import org.apache.http.client.methods.CloseableHttpResponse;
import org.apache.http.client.methods.HttpGet;
import org.apache.http.client.methods.HttpPost;
import org.apache.http.entity.StringEntity;
import org.apache.http.impl.client.CloseableHttpClient;
import org.apache.http.impl.client.HttpClients;
import org.apache.http.message.BasicHeader;
import org.apache.http.message.BasicNameValuePair;
import org.apache.http.protocol.HTTP;
import org.apache.http.util.EntityUtils;
import org.slf4j.Logger;
import org.slf4j.LoggerFactory;

import java.io.*;
import java.net.HttpURLConnection;
import java.net.URL;
import java.net.URLConnection;
import java.util.*;
/**
 * HttpClient工具类
 * <AUTHOR>
public class HttpRequestUtil {

    private static final Logger log = LoggerFactory.getLogger(HttpRequestUtil.class);

    public static void main(String[] args) throws UnsupportedEncodingException {
        Map<String, String> map = new HashMap<String, String>(2);
        JSONObject authorJson = new JSONObject();
        authorJson.put("loginAccount", "admin");
        JSONObject paramJson = new JSONObject();
        paramJson.put("code", "TongYongChaXunSQLYuJu");
        paramJson.put("params", " { searchsql:\"select * from cs_equ_channel limit 10\" }");
        map.put("authorJson", authorJson.toJSONString());
        map.put("parmJson", paramJson.toJSONString());
        String result = HttpRequestUtil.sendPost("http://localhost:8080/aic/restfull/operationRestfullApi/excuteSqlByCode", map);
        System.out.println(result);
    }

    /**
     * 向指定URL发送GET方法的请求
     *
     * @param url   发送请求的URL
     * @param param 请求参数，请求参数应该是 name1=value1&name2=value2 的形式。
     * @return URL 所代表远程资源的响应结果
     */
    public static String sendGet(String url, String param) {
        StringBuilder result = new StringBuilder();
        BufferedReader in = null;
        try {
            String urlNameString = url + "?" + param;
            URL realUrl = new URL(urlNameString);
            // 打开和URL之间的连接
            URLConnection connection = realUrl.openConnection();
            // 设置通用的请求属性
            connection.setRequestProperty("accept", "*/*");
            connection.setRequestProperty("connection", "Keep-Alive");
            connection.setRequestProperty("Content-Type", "application/json");
            connection.setRequestProperty("user-agent", "Mozilla/4.0 (compatible; MSIE 6.0; Windows NT 5.1;SV1)");

            // 建立实际的连接
            connection.connect();

            // 获取所有响应头字段
            Map<String, List<String>> map = connection.getHeaderFields();

            // 遍历所有的响应头字段
            // 定义 BufferedReader输入流来读取URL的响应
            in = new BufferedReader(new InputStreamReader(connection.getInputStream()));
            String line;
            while ((line = in.readLine()) != null) {
                result.append(line);
            }
        } catch (Exception e) {
            e.printStackTrace();
        } finally {
            // 使用finally块来关闭输入流
            IOUtils.closeQuietly(in);
        }
        return result.toString();
    }

    /**
     * 向指定 URL 发送POST方法的请求
     *
     * @param url   发送请求的 URL
     * @param param 请求参数，请求参数应该是 name1=value1&name2=value2 的形式。
     * @return 所代表远程资源的响应结果
     */
    public static String sendPostStringParam(String url, String param) {
        PrintWriter out = null;
        BufferedReader in = null;
        StringBuilder result = new StringBuilder();
        try {
            URL realUrl = new URL(url);
            // 打开和URL之间的连接
            URLConnection conn = realUrl.openConnection();
            // 设置通用的请求属性
            conn.setRequestProperty("accept", "*/*");
            conn.setRequestProperty("connection", "Keep-Alive");
            conn.setRequestProperty("user-agent", "Mozilla/4.0 (compatible; MSIE 6.0; Windows NT 5.1;SV1)");
            // 发送POST请求必须设置如下两行
            conn.setDoOutput(true);
            conn.setDoInput(true);
            // 获取URLConnection对象对应的输出流
            out = new PrintWriter(conn.getOutputStream());
            // 发送请求参数
            out.print(param);
            // flush输出流的缓冲
            out.flush();
            // 定义BufferedReader输入流来读取URL的响应
            in = new BufferedReader(new InputStreamReader(conn.getInputStream()));
            String line;
            while ((line = in.readLine()) != null) {
                result.append(line);
            }
        } catch (Exception e) {
            System.out.println("发送 POST 请求出现异常！" + e);
            e.printStackTrace();
        }
        // 使用finally块来关闭输出流、输入流
        finally {
            IOUtils.closeQuietly(out);
            IOUtils.closeQuietly(in);
        }
        return result.toString();
    }

    public static String sendPost(String restUrl, String param) {
        StringBuilder sb = new StringBuilder();
        OutputStream os = null;
        try {
            URL url = new URL(restUrl);
            HttpURLConnection connection = (HttpURLConnection) url.openConnection();
            connection.setRequestMethod("POST");
            connection.setRequestProperty("content-type", "application/json");
            connection.setRequestProperty("token", "");
            // 3.3设置输入输出，新创建的connection默认是没有读写权限的，
            connection.setDoInput(true);
            connection.setDoOutput(true);
            os = connection.getOutputStream();
            os.write(param.getBytes());
            int responseCode = connection.getResponseCode();
            if (GlobalCodeConstant.SUCCESS == responseCode) {
                InputStream is = connection.getInputStream();
                InputStreamReader isr = new InputStreamReader(is);
                BufferedReader br = new BufferedReader(isr);

                String temp = null;

                while (null != (temp = br.readLine())) {
                    sb.append(temp);
                }

                is.close();
                isr.close();
                br.close();
            }

        } catch (Exception e) {
            e.printStackTrace();
        } finally {
            IOUtils.closeQuietly(os);
        }
        return sb.toString();
    }

    /**
     * 发送GET请求
     *
     * @param url
     * @return
     */
    public static String sendGet(String url) {
        CloseableHttpClient client = HttpClients.createDefault();
        HttpGet httpGet = new HttpGet(url);
        try {
            CloseableHttpResponse resp = client.execute(httpGet);
            StatusLine status = resp.getStatusLine();
            int code = status.getStatusCode();
            if (code != GlobalCodeConstant.SUCCESS) {
                return null;
            }
            return EntityUtils.toString(resp.getEntity());

        } catch (Exception e) {
            e.printStackTrace();
        }
        return null;
    }

    /**
     * post请求(用于key-value格式的参数)
     *
     * @param url
     * @param params
     * @return
     */
    public static String sendPost(String url, Map<String, String> params) {

        try {
            // 定义HttpClient
            CloseableHttpClient client = HttpClients.createDefault();
            // 实例化HTTP方法
            HttpPost request = new HttpPost(url);

            // 设置参数
            List<NameValuePair> nvps = new ArrayList<NameValuePair>();
            for (Iterator<String> iter = params.keySet().iterator(); iter.hasNext(); ) {
                String name = (String) iter.next();
                String value = String.valueOf(params.get(name));
                nvps.add(new BasicNameValuePair(name, value));
            }
            request.setEntity(new UrlEncodedFormEntity(nvps, "utf-8"));

            HttpResponse response = client.execute(request);
            int code = response.getStatusLine().getStatusCode();
            if (code == GlobalCodeConstant.SUCCESS) {
                return EntityUtils.toString(response.getEntity());
            }
        } catch (Exception e) {
            e.printStackTrace();
        }
        return null;
    }

    public static String sendWsPost(String wsUrl, String param, String token, String appId, String appKey,
                                    String serviceCode) {
        StringBuilder sb = new StringBuilder();
        try {
            URL url = new URL(wsUrl);
            HttpURLConnection connection = (HttpURLConnection) url.openConnection();
            connection.setRequestMethod("POST");
            connection.setRequestProperty("content-type", "text/xml;charset=utf-8");
            connection.setRequestProperty("accept", "*/*");
            connection.setRequestProperty("connection", "Keep-Alive");
            connection.setRequestProperty("user-agent", "Mozilla/4.0 (compatible; MSIE 6.0; Windows NT 5.1;SV1)");
            connection.setRequestProperty("Svc_TimeStamp", DateTimeUtil.getCurrentTimestampStr("yyyyMMdd'T'HHmmss"));
            connection.setRequestProperty("Svc_AppKey", appKey);
            connection.setRequestProperty("Svc_ServiceCode", serviceCode);
            connection.setRequestProperty("Svc_MsgSeq",
                    UuidUtil.getUuid32() + DateTimeUtil.getCurrentTimestampStr("yyyyMMddHHmmss"));
            connection.setRequestProperty("Svc_AppID", appId);
            connection.setRequestProperty("Svc_Operation", "getGaugeData");
            connection.setRequestProperty("Svc_RequestType", "1");
            connection.setRequestProperty("Svc_Token", token);
            // 3.3设置输入输出，新创建的connection默认是没有读写权限的，
            connection.setDoInput(true);
            connection.setDoOutput(true);
            OutputStream os = connection.getOutputStream();
            os.write(param.getBytes());
            int responseCode = connection.getResponseCode();
            // 头反馈信息
            if (GlobalCodeConstant.SUCCESS == responseCode) {
                InputStream is = connection.getInputStream();
                InputStreamReader isr = new InputStreamReader(is);
                BufferedReader br = new BufferedReader(isr);

                String temp = null;

                while (null != (temp = br.readLine())) {
                    sb.append(temp);
                }

                is.close();
                isr.close();
                br.close();
            }
            os.close();
        } catch (Exception e) {
            e.printStackTrace();
        }
        return sb.toString();
    }

    public static String sendWsPost(String wsUrl, String param, String token, String appId, String appKey,
                                    String serviceCode, String contentType, String svcOperation) {
        StringBuilder sb = new StringBuilder();
        OutputStream os = null;
        try {
            URL url = new URL(wsUrl);
            HttpURLConnection connection = (HttpURLConnection) url.openConnection();
            connection.setRequestMethod("POST");
            connection.setRequestProperty("Content-Type", "contentType");
            connection.setRequestProperty("accept", "*/*");
            connection.setRequestProperty("connection", "Keep-Alive");
            connection.setRequestProperty("user-agent", "Mozilla/4.0 (compatible; MSIE 6.0; Windows NT 5.1;SV1)");
            connection.setRequestProperty("Svc_TimeStamp", DateTimeUtil.getCurrentTimestampStr("yyyyMMdd'T'HHmmss"));
            connection.setRequestProperty("Svc_AppKey", appKey);
            connection.setRequestProperty("Svc_ServiceCode", serviceCode);
            connection.setRequestProperty("Svc_MsgSeq",
                    UuidUtil.getUuid32() + DateTimeUtil.getCurrentTimestampStr("yyyyMMddHHmmss"));
            connection.setRequestProperty("Svc_AppID", appId);
            connection.setRequestProperty("Svc_Operation", svcOperation);
            connection.setRequestProperty("Svc_RequestType", "1");
            connection.setRequestProperty("Svc_Token", token);
            // 3.3设置输入输出，新创建的connection默认是没有读写权限的，
            connection.setDoInput(true);
            connection.setDoOutput(true);
            os = connection.getOutputStream();
            os.write(param.getBytes());
            int responseCode = connection.getResponseCode();
            // 头反馈信息
            if (GlobalCodeConstant.SUCCESS == responseCode) {
                InputStream is = connection.getInputStream();
                InputStreamReader isr = new InputStreamReader(is);
                BufferedReader br = new BufferedReader(isr);

                String temp = null;

                while (null != (temp = br.readLine())) {
                    sb.append(temp);
                }

                is.close();
                isr.close();
                br.close();
            }

        } catch (Exception e) {
            e.printStackTrace();
        } finally {
            IOUtils.closeQuietly(os);
        }
        return sb.toString();
    }

    /**
     * 向ESB发送post请求
     */
    public static String sendReq2Esb(String url, String param, String token, String appId, String appKey,
                                     String serviceCode, String contentType, String operation, String charSet) {
        OutputStreamWriter out = null;
        BufferedReader in = null;
        StringBuilder result = new StringBuilder();
        try {
            URL realUrl = new URL(url);
            HttpURLConnection conn = (HttpURLConnection) realUrl.openConnection();
            conn.setDoOutput(true);
            conn.setDoInput(true);
            conn.setRequestMethod(operation);
            // 设置通用的请求属性
            conn.setRequestProperty("Svc_TimeStamp", DateTimeUtil.getCurrentTimestampStr("yyyyMMdd'T'HHmmss"));
            conn.setRequestProperty("Svc_AppKey", appKey);
            conn.setRequestProperty("Svc_ServiceCode", serviceCode);
            conn.setRequestProperty("Svc_MsgSeq",
                    UuidUtil.getUuid32() + DateTimeUtil.getCurrentTimestampStr("yyyyMMddHHmmss"));
            conn.setRequestProperty("Svc_AppID", appId);
            conn.setRequestProperty("Svc_Operation", operation);
            conn.setRequestProperty("Svc_RequestType", "1");
            conn.setRequestProperty("Svc_Token", token);
            conn.setRequestProperty("Content-Type", contentType);
            conn.connect();
            // 获取URLConnection对象对应的输出流
            if (StringUtils.isNotBlank(param)) {
                out = new OutputStreamWriter(conn.getOutputStream(), "UTF-8");
                out.write(param);
                out.flush();
            }
            // 定义BufferedReader输入流来读取URL的响应
            in = new BufferedReader(new InputStreamReader(conn.getInputStream(), charSet));
            String line;
            while ((line = in.readLine()) != null) {
                result.append(line);
            }
            conn.disconnect();
        } catch (Exception e) {
            e.printStackTrace();
        } finally {
            IOUtils.closeQuietly(out);
        }
        return result.toString();
    }

    public static String sendPostWithHeader(String url, String param, String token, String appId, String appKey,
                                            String serviceCode, String contentType, String operation) {
        String resultStr = "";
        try {
            resultStr = "";
            CloseableHttpClient httpClient = HttpClients.createDefault();
            HttpPost httpPost = new HttpPost(url);
            httpPost.addHeader("Content-Type", contentType);
            httpPost.addHeader("Svc_ServiceCode ", serviceCode);
            httpPost.addHeader("Svc_Operation", operation);
            httpPost.addHeader("Svc_TimeStamp", DateTimeUtil.getCurrentTimestampStr("yyyyMMdd'T'HHmmss"));
            httpPost.addHeader("Svc_AppID", appId);
            httpPost.addHeader("Svc_AppKey", appKey);
            httpPost.addHeader("Svc_ToKen ", token);
            httpPost.addHeader("Svc_RequestType", "1");
            httpPost.addHeader("Svc_MsgSeq",
                    UuidUtil.getUuid32() + DateTimeUtil.getCurrentTimestampStr("yyyyMMddHHmmss"));

            StringEntity paramEntity = new StringEntity(param);
            paramEntity.setContentType("application/json");
            httpPost.setEntity(paramEntity);

            CloseableHttpResponse httpResponse = httpClient.execute(httpPost);
            HttpEntity httpEntity = httpResponse.getEntity();
            resultStr = EntityUtils.toString(httpEntity, "utf-8");
            httpClient.close();
        } catch (Exception e) {
            e.printStackTrace();
        }
        return resultStr;
    }

    public static String sendWsPost(String wsUrl, String param) {
        return sendWsPost(wsUrl, param, "text/xml;charset=utf-8");
    }

    public static String sendWsPost(String wsUrl, String param, String contentType) {
        StringBuilder sb = new StringBuilder();
        OutputStream os = null;
        try {
            URL url = new URL(wsUrl);
            HttpURLConnection connection = (HttpURLConnection) url.openConnection();
            connection.setRequestMethod("POST");
            connection.setRequestProperty("content-type", contentType);
            connection.setRequestProperty("token", "");
            // 3.3设置输入输出，新创建的connection默认是没有读写权限的，
            connection.setDoInput(true);
            connection.setDoOutput(true);
            os = connection.getOutputStream();
            os.write(param.getBytes());
            int responseCode = connection.getResponseCode();
            if (GlobalCodeConstant.SUCCESS == responseCode) {
                InputStream is = connection.getInputStream();
                InputStreamReader isr = new InputStreamReader(is);
                BufferedReader br = new BufferedReader(isr);

                String temp = null;

                while (null != (temp = br.readLine())) {
                    sb.append(temp);
                }

                is.close();
                isr.close();
                br.close();
            }

        } catch (Exception e) {
            e.printStackTrace();
        } finally {
            IOUtils.closeQuietly(os);
        }
        return sb.toString();
    }

    /**
     * @param url
     * @param jsonObject
     * @param encoding
     * @return
     * @throws IOException
     */
    public static String send(String url, JSONObject jsonObject, String encoding) throws IOException {
        String body = "";

        //创建httpclient对象
        CloseableHttpClient client = HttpClients.createDefault();
        //创建post方式请求对象
        HttpPost httpPost = new HttpPost(url);

        //装填参数
        StringEntity s = new StringEntity(jsonObject.toString(), "utf-8");
        s.setContentEncoding(new BasicHeader(HTTP.CONTENT_ENCODING, "UTF-8"));
        s.setContentType(new BasicHeader(HTTP.CONTENT_TYPE, "application/json"));
        //设置参数到请求对象中
        httpPost.setHeader(new BasicHeader(HTTP.CONTENT_TYPE, "application/json"));
        httpPost.setEntity(s);
        HaLog.info(log, MsgIdConstant.MS_INF_0009, "请求地址", url);
        HaLog.info(log, MsgIdConstant.MS_INF_0009, "请求参数", jsonObject.toJSONString());

        //设置header信息
        httpPost.setHeader("User-Agent", "Mozilla/4.0 (compatible; MSIE 5.0; Windows NT; DigExt)");

        //执行请求操作，并拿到结果（同步阻塞）
        CloseableHttpResponse response = null;
        try {
            //执行请求操作，并拿到结果（同步阻塞）
            response = client.execute(httpPost);
            //获取结果实体
            HttpEntity entity = response.getEntity();
            if (entity != null) {
                //按指定编码转换结果实体为String类型
                body = EntityUtils.toString(entity, encoding);
            }
            EntityUtils.consume(entity);
        } finally {
            //释放链接
            IOUtils.closeQuietly(response);
        }
        return body;
    }

}
