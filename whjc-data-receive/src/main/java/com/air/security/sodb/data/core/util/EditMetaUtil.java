package com.air.security.sodb.data.core.util;

import java.text.ParseException;

import com.air.security.sodb.data.receive.ifdomain.Meta;

/**
 * @Description: meta编辑的工具类
 * @author: zhangc
 * @Date: 2018年12月6日
 */
public class EditMetaUtil {

    /**
     * 编辑消息头
     *
     * @param recvHeader
     * @param meta
     * @throws ParseException
     */
    public static void editMeta(Meta meta) throws ParseException {

        // 消息序号
        meta.setSequence(UuidUtil.getUuid32());

    }

    /**
     * 编辑消息头
     *
     * @param eventType 事件类型
     * @param msgType   消息类型
     * @param recvTime  前端系统接收时间
     * @param meta      消息头
     */
    public static void editMeta(String eventType, String msgType, String recvTime, Meta meta) {
        // 设置事件类型
        meta.setEventType(eventType);

        // 设置消息类型
        meta.setMsgType(msgType);

        // sdk采集时间
        meta.setRecvTime(recvTime);

        // 消息序号
        meta.setSequence(UuidUtil.getUuid32());
    }

}
