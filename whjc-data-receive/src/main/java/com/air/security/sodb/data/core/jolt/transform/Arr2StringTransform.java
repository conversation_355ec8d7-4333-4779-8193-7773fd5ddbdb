package com.air.security.sodb.data.core.jolt.transform;

import java.util.List;
import java.util.Map;

import javax.inject.Inject;

import com.air.security.sodb.data.core.constant.ExceptionConstant;
import com.bazaarvoice.jolt.SpecDriven;
import com.bazaarvoice.jolt.Transform;
import com.bazaarvoice.jolt.exception.SpecException;

/**
 * @Description: 根据spec配置将数组转为String
 * @author: zhangc
 * @Date: 2018年12月13日
 */
public class Arr2StringTransform implements SpecDriven, Transform {

    /**
     * 转换规则配置
     * 如："alarmEquCode": ","
     */
    private final Object spec;

    @Inject
    public Arr2StringTransform(Object spec) {
        this.spec = spec;
    }

    /**
     * 转换
     * <p>
     * example:
     * input -> "alarmEquCode":['1110','1111','1112']
     * output -> "alarmEquCode":"1110,1111,1112"
     */
    @SuppressWarnings("unchecked")
    @Override
    public Object transform(Object input) {

        // spec 配置不合规范
        if (!(spec instanceof Map)) {
            throw new SpecException(ExceptionConstant.SPEC_CONF_IS_NOT_MAPJSON_EXCEPTION);
        }

        // 输入的源字符串不是标准json
        if (!(input instanceof Map)) {
            throw new SpecException(ExceptionConstant.INPUT_IS_NOT_MAPJOSN_EXCEPTION);
        }

        // spec配置转map
        Map<String, Object> specMap = (Map<String, Object>) spec;

        // input输入转map
        Map<String, Object> inputMap = (Map<String, Object>) input;

        // 读取spec配置，获取需要转换的key
        for (Map.Entry<String, Object> specEntry : specMap.entrySet()) {
            String specKey = specEntry.getKey();

            // 分隔符
            String splitChar = (String) specEntry.getValue();

            StringBuilder outputValue = new StringBuilder();

            if (!(inputMap.get(specKey) instanceof List)) {
                return inputMap;
            }

            List<Object> inputValueList = (List<Object>) inputMap.get(specKey);

            if (null != inputValueList && !inputValueList.isEmpty()) {
                for (int i = 0, l = inputValueList.size(); i < l; i++) {
                    String inputValue = String.valueOf(inputValueList.get(i));
                    outputValue.append(inputValue);
                    if (i < l - 1) {
                        outputValue.append(splitChar);
                    }
                }

            }

            inputMap.put(specKey, outputValue);
        }

        return inputMap;
    }

}
