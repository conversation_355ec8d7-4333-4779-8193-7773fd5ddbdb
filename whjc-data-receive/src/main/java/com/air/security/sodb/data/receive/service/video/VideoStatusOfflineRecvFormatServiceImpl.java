package com.air.security.sodb.data.receive.service.video;

import org.slf4j.Logger;
import org.slf4j.LoggerFactory;
import org.springframework.stereotype.Service;

import com.air.security.sodb.data.core.constant.MsgIdConstant;
import com.air.security.sodb.data.core.exception.SystemBaseException;
import com.air.security.sodb.data.core.util.HaLog;
import com.air.security.sodb.data.receive.ifdomain.Meta;
import com.air.security.sodb.data.receive.service.AbstractMessageRecvService;
import com.alibaba.fastjson.JSONObject;

/**
 * 推送设备状态变更的实现类
 * <AUTHOR>
@Service("VideoStatusOfflineRecvFormatServiceImpl")
public class VideoStatusOfflineRecvFormatServiceImpl extends AbstractMessageRecvService {

    private static final Logger log = LoggerFactory.getLogger(VideoStatusOfflineRecvFormatServiceImpl.class);

    @Override
    public void execute(Meta meta, String messageBody) throws SystemBaseException {
        HaLog.info(log, MsgIdConstant.MS_INF_0001, meta.getEventType());

        // 获取到的json业务数据
        JSONObject inputJson = JSONObject.parseObject(messageBody);

        inputJson.put("timeStateId", "ES02");

        // 响应json消息
        JSONObject outputMsg = new JSONObject();

        // 响应消息头
        outputMsg.put("meta", meta);

        // 响应消息体
        outputMsg.put("body", inputJson);

        // 转换为JSON格式，并发送消息到指定主题
        super.putSendMessage(outputMsg);

        HaLog.info(log, MsgIdConstant.MS_INF_0002, meta.getEventType());
    }

}
