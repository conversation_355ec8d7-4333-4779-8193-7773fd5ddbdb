package com.air.security.sodb.data.core.config;

/**
 * 数据标准化过滤数据映射配置
 *
 * <AUTHOR>
 */
public class FormatHandleRecvFilterConfig {

    /**
     * 过滤值
     */
    private String filterValue;

    /**
     * 滤出数据输入主题
     */
    private String filterTopic;

    public FormatHandleRecvFilterConfig() {
        super();
    }

    public FormatHandleRecvFilterConfig(String filterValue) {
        super();
        this.filterValue = filterValue;
    }

    /**
     * 过滤值
     */
    public String getFilterValue() {
        return filterValue;
    }

    /**
     * 过滤值
     */
    public void setFilterValue(String filterValue) {
        this.filterValue = filterValue;
    }

    /**
     * 滤出数据输入主题
     */
    public String getFilterTopic() {
        return filterTopic;
    }

    /**
     * 滤出数据输入主题
     */
    public void setFilterTopic(String filterTopic) {
        this.filterTopic = filterTopic;
    }

    @Override
    public int hashCode() {
        final int prime = 31;
        int result = 1;
        result = prime * result + ((filterValue == null) ? 0 : filterValue.hashCode());
        return result;
    }

    @Override
    public boolean equals(Object obj) {
        if (this == obj) {
            return true;
        }

        if (obj == null) {
            return false;
        }

        if (getClass() != obj.getClass()) {
            return false;
        }
        FormatHandleRecvFilterConfig other = (FormatHandleRecvFilterConfig) obj;
        if (filterValue == null) {
            if (other.filterValue != null) {
                return false;
            }
        } else if (!filterValue.equals(other.filterValue)) {
            return false;
        }
        return true;
    }

}
