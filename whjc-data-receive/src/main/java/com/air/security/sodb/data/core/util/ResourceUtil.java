package com.air.security.sodb.data.core.util;

import java.io.BufferedReader;
import java.io.FileNotFoundException;
import java.io.FileReader;
import java.io.IOException;
import java.text.MessageFormat;
import java.util.HashMap;
import java.util.Properties;

/**
 * <p>Title: ResourceUtil.java</p>
 * <p>Description: 配置文件操作工具类</p>
 *
 * <AUTHOR>
 * @version v1.0
 * @date 2018年5月21日
 */
public class ResourceUtil {

    private static final String PROPERTY_VALUE_PREFIX = "/";

    /**
     * 配置文件存储的map
     */
    private static HashMap<String, Properties> hashMap = new HashMap<String, Properties>();

    private static ResourceUtil resourceUtil = null;

    public ResourceUtil() {
        super();
    }

    synchronized public static ResourceUtil getInstance() {
        if (resourceUtil == null) {
            resourceUtil = new ResourceUtil();
        }
        return resourceUtil;
    }

    /**
     * 读取properties文件
     *
     * @param fileName
     * @return
     */
    synchronized public Properties getProperties(String fileName) {
        Properties properties = null;
        properties = (Properties) hashMap.get(fileName);
        if (properties == null) {
            properties = new Properties();
            if (fileName.startsWith(PROPERTY_VALUE_PREFIX)) {
                try {
                    properties.load(this.getClass().getResourceAsStream(fileName));
                } catch (IOException e) {
                }
            } else {
                try {
                    properties.load(this.getClass().getResourceAsStream(PROPERTY_VALUE_PREFIX + fileName));
                } catch (IOException e) {
                }
            }

            hashMap.put(fileName, properties);
        }
        return properties;
    }

    /**
     * 读取本地properties文件，整个系统中不要出现同名的配置文件
     *
     * @param filePath 附件中真实路径
     * @param fileName 附件名
     * @return
     */
    synchronized public Properties getLocalProperties(String filePath, String fileName) {
        Properties properties = null;
        properties = (Properties) hashMap.get(fileName);
        if (null == properties) {
            properties = new Properties();
            // 使用InPutStream流读取properties文件
            try {
                BufferedReader bufferedReader = new BufferedReader(new FileReader(filePath));
                properties.load(bufferedReader);
                hashMap.put(fileName, properties);
            } catch (FileNotFoundException e) {
            } catch (IOException e) {
            }
        }
        return properties;
    }

    /**
     * 用objectArray替换peoperties文件中带参数{0}的值
     *
     * @param properties  Properties文件
     * @param key         Properties中的key
     * @param objectArray Object对象数组
     * @return 替换后的字符串
     */
    public static String getParamProperties(Properties properties, String key, Object[] objectArray) {
        String paramProperties = (String) properties.get(key);
        //替换参数:odconv/{0}/page/{1}/density/{2}/quality/{3}/resize/{4}
        MessageFormat messageFormat = new MessageFormat(paramProperties);
        return messageFormat.format(objectArray);
    }

    public static void main(String[] args) {
        Properties properties = ResourceUtil.getInstance().getLocalProperties("/mqtest/imf_config.properties", "imf_config");
        System.out.println(properties.getProperty("log4j"));
    }
}
