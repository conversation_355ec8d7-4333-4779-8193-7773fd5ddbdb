package com.air.security.sodb.data.dto;

/**
 * 接收消息传输日志的Dto
 * <AUTHOR>
public class ReceiveMsgTransferLogDTO {

    /**
     * 主键
     */
    private String uuid;

    /**
     * 消息发送者
     */
    private String sender;

    /**
     * 消息接收者
     */
    private String receiver;

    /**
     * 消息序号
     */
    private String sequence;

    /**
     * 接收到的消息序号
     */
    private String recvSequence;

    /**
     * 发送时间
     */
    private String sendTime;

    /**
     * 前端采集时间
     */
    private String recvTime;

    /**
     * 转发时间
     */
    private String forwardTime;

    /**
     * 消息类型
     */
    private String msgType;

    /**
     * 事件类型
     */
    private String eventType;

    /**
     * 数据内容
     */
    private Object content;

    /**
     * 内容格式，xml,json,string
     */
    private String contentFormat;

    /**
     * 日志类型，0：前端接收日志，1：交互系统接收日志，2：交互系统发送日志
     */
    private Character logType;

    /**
     * 服务类型，0：服务数据日志, 1：接收数据日志
     */
    private Character serviceType;

    /**
     * @return 主键
     */
    public String getUuid() {
        return uuid;
    }

    /**
     * @param 主键
     */
    public void setUuid(String uuid) {
        this.uuid = uuid;
    }

    /**
     * @param 消息发送者
     */
    public void setSender(String sender) {
        this.sender = sender;
    }

    /**
     * @return 消息发送者
     */
    public String getSender() {
        return sender;
    }

    /**
     * @param 消息接收者
     */
    public void setReceiver(String receiver) {
        this.receiver = receiver;
    }

    /**
     * @return 消息接收者
     */
    public String getReceiver() {
        return receiver;
    }

    /**
     * @param 消息序号
     */
    public void setSequence(String sequence) {
        this.sequence = sequence;
    }

    /**
     * @return 消息序号
     */
    public String getSequence() {
        return sequence;
    }

    /**
     * @return 接收到的消息序号
     */
    public String getRecvSequence() {
        return recvSequence;
    }

    /**
     * @param 接收到的消息序号
     */
    public void setRecvSequence(String recvSequence) {
        this.recvSequence = recvSequence;
    }

    /**
     * @param 发送时间
     */
    public void setSendTime(String sendTime) {
        this.sendTime = sendTime;
    }

    /**
     * @return 发送时间
     */
    public String getSendTime() {
        return sendTime;
    }

    /**
     * @return 前端采集时间
     */
    public String getRecvTime() {
        return recvTime;
    }

    /**
     * @param 前端采集时间
     */
    public void setRecvTime(String recvTime) {
        this.recvTime = recvTime;
    }

    /**
     * @return 转发时间
     */
    public String getForwardTime() {
        return forwardTime;
    }

    /**
     * @param 转发时间
     */
    public void setForwardTime(String forwardTime) {
        this.forwardTime = forwardTime;
    }

    /**
     * @param 消息类型
     */
    public void setMsgType(String msgType) {
        this.msgType = msgType;
    }

    /**
     * @return 消息类型
     */
    public String getMsgType() {
        return msgType;
    }

    /**
     * @return 事件类型
     */
    public String getEventType() {
        return eventType;
    }

    /**
     * @param 事件类型
     */
    public void setEventType(String eventType) {
        this.eventType = eventType;
    }

    /**
     * @return 数据内容
     */
    public Object getContent() {
        return content;
    }

    /**
     * @param 数据内容
     */
    public void setContent(Object content) {
        this.content = content;
    }

    /**
     * @return 内容格式，xml,json,string
     */
    public String getContentFormat() {
        return contentFormat;
    }

    /**
     * @param 内容格式，xml,json,string
     */
    public void setContentFormat(String contentFormat) {
        this.contentFormat = contentFormat;
    }

    /**
     * @return 日志类型，0：前端接收日志，1：交互系统接收日志，2：交互系统发送日志
     */
    public Character getLogType() {
        return logType;
    }

    /**
     * @param 日志类型，0：前端接收日志，1：交互系统接收日志，2：交互系统发送日志
     */
    public void setLogType(Character logType) {
        this.logType = logType;
    }

    /**
     * 服务类型，1：接收数据日志，0：服务数据日志
     *
     * @return
     */
    public Character getServiceType() {
        return serviceType;
    }

    /**
     * 服务类型，1：接收数据日志，0：服务数据日志
     *
     * @param serviceType
     */
    public void setServiceType(Character serviceType) {
        this.serviceType = serviceType;
    }

    @Override
    public String toString() {
        return "ReceiveMsgTransferLogDTO{" +
                "uuid='" + uuid + '\'' +
                ", sender='" + sender + '\'' +
                ", receiver='" + receiver + '\'' +
                ", sequence='" + sequence + '\'' +
                ", recvSequence='" + recvSequence + '\'' +
                ", sendTime='" + sendTime + '\'' +
                ", recvTime='" + recvTime + '\'' +
                ", forwardTime='" + forwardTime + '\'' +
                ", msgType='" + msgType + '\'' +
                ", eventType='" + eventType + '\'' +
                ", content=" + content +
                ", contentFormat='" + contentFormat + '\'' +
                ", logType=" + logType +
                ", serviceType=" + serviceType +
                '}';
    }
}
