package com.air.security.sodb.data.core.util;

import java.text.MessageFormat;

import org.apache.commons.lang.StringUtils;
import org.slf4j.Logger;

/**
 * 日志工具类
 *
 * <AUTHOR>
 */
public class HaLog {

    public HaLog() {
        super();
    }

    /**
     * 输出Debug日志
     *
     * @param log    输出日志对象
     * @param msgId  日志编号
     * @param params 日志埋入字符
     */
    public static void debug(Logger log, String msgId, Object... params) {

        if (log.isDebugEnabled()) {
            String message = PropertyUtil.getProperty(msgId);
            if (params != null && params.length != 0) {
                message = MessageFormat.format(message, params);
            }
            log.debug(StringUtils.isNotBlank(message) ? message : msgId);
        }
    }

    /**
     * 输出warn日志
     *
     * @param log    输出日志对象
     * @param msgId  日志编号
     * @param params 日志埋入字符
     */
    public static void warn(Logger log, String msgId, Object... params) {
        if (log.isWarnEnabled()) {
            String message = PropertyUtil.getProperty(msgId);
            if (params != null && params.length != 0) {
                message = MessageFormat.format(message, params);
            }
            log.warn(StringUtils.isNotBlank(message) ? message : msgId);
        }
    }

    /**
     * 输出info日志
     *
     * @param log    输出日志对象
     * @param msgId  日志编号
     * @param params 日志埋入字符
     */
    public static void info(Logger log, String msgId, Object... params) {
        if (log.isInfoEnabled()) {
            String message = PropertyUtil.getProperty(msgId);
            if (params != null && params.length != 0) {
                message = MessageFormat.format(message, params);
            }
            log.info(StringUtils.isNotBlank(message) ? message : msgId);
        }
    }

    /**
     * 输出info日志
     *
     * @param log     输出日志对象
     * @param logInfo XML或Json格式的日志
     */
    public static void infoJson(Logger log, String logInfo) {
        if (log.isInfoEnabled()) {
            log.info(logInfo);
        }
    }

    /**
     * 输出error日志
     *
     * @param log    输出日志对象
     * @param msgId  日志编号
     * @param params 日志埋入字符
     */
    public static void error(Logger log, String msgId, Object... params) {
        if (log.isErrorEnabled()) {
            String message = PropertyUtil.getProperty(msgId);
            if (params != null && params.length != 0) {
                message = MessageFormat.format(message, params);
            }
            log.error(StringUtils.isNotBlank(message) ? message : msgId);
        }
    }

    /**
     * 输出error日志
     *
     * @param log   输出日志对象
     * @param msgId 日志编号
     * @param e     异常
     */
    public static void error(Logger log, String msgId, Exception e, Object... params) {
        if (log.isErrorEnabled()) {
            String message = PropertyUtil.getProperty(msgId);
            if (params != null && params.length != 0) {
                message = MessageFormat.format(message, params);
            }
            log.error(StringUtils.isNotBlank(message) ? message : msgId + e.getMessage(), e);
        }
    }

    /**
     * 输出error日志
     *
     * @param log
     * @param e
     * @param msgId
     * @param params
     */
    public static void error(Logger log, Exception e, String msgId, Object... params) {
        if (log.isErrorEnabled()) {
            String message = PropertyUtil.getProperty(msgId);
            if (params != null && params.length != 0) {
                message = MessageFormat.format(message, params);
            }
            log.error(StringUtils.isNotBlank(message) ? message : msgId + e.getMessage(), e);
        }
    }
}
