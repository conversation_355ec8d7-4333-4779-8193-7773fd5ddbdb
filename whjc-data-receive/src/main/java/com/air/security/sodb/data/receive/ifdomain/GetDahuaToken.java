package com.air.security.sodb.data.receive.ifdomain;

import com.air.security.sodb.data.core.constant.GlobalCodeConstant;
import com.air.security.sodb.data.core.exception.SystemBaseException;
import com.air.security.sodb.data.core.util.HttpRequestUtil;
import com.air.security.sodb.data.core.util.PropertyUtil;
import com.alibaba.fastjson.JSON;
import com.alibaba.fastjson.JSONObject;

import java.io.IOException;
import java.io.UnsupportedEncodingException;
import java.util.Base64;

/**
 * 获取大华token类
 * <AUTHOR>
public class GetDahuaToken {
    final static Base64.Encoder ENCODER = Base64.getEncoder();

    /**
     * 获取token
     *
     * @return
     * @throws IOException
     */
    public static String getToken() {

        //获取大华的登录key
        String keyUrl = PropertyUtil.getProperty("dahua.getToken");
        String passWord = PropertyUtil.getProperty("dahua.passWord");
        byte[] bt = null;
        try {
            bt = passWord.getBytes("UTF-8");
        } catch (UnsupportedEncodingException e) {
            e.printStackTrace();
        }
        JSONObject jsonObject = new JSONObject();
        jsonObject.put("loginName", PropertyUtil.getProperty("dahua.loginName"));
        jsonObject.put("loginPass", ENCODER.encodeToString(bt));
        try {
            String str = HttpRequestUtil.send(keyUrl, jsonObject, "utf-8");

            // 转换json对象
            JSONObject infoJson = JSON.parseObject(str);

            //验证调用成功
            String success = "success";
            if (!GlobalCodeConstant.TRUE.equals(infoJson.getString(success))) {
                throw new SystemBaseException(infoJson.getString("errMsg"));
            }
            return infoJson.getString("token");
        } catch (Exception e) {
            // TODO Auto-generated catch block
            if (e instanceof SystemBaseException) {
                throw (SystemBaseException) e;
            }
            throw new SystemBaseException("获取token失败", e);
        }


    }
}
