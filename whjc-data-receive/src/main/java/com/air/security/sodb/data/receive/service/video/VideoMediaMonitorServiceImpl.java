package com.air.security.sodb.data.receive.service.video;

import java.util.HashMap;

import org.apache.commons.lang3.StringUtils;
import org.slf4j.Logger;
import org.slf4j.LoggerFactory;
import org.springframework.stereotype.Service;

import com.air.security.sodb.data.core.constant.MsgIdConstant;
import com.air.security.sodb.data.core.exception.SystemBaseException;
import com.air.security.sodb.data.core.util.HaLog;
import com.air.security.sodb.data.receive.ifdomain.Meta;
import com.air.security.sodb.data.receive.service.AbstractMessageRecvService;
import com.alibaba.fastjson.JSONObject;
/**
 * <p>Description: 这个类用来处理流媒体监控告警数据</p>
 * <AUTHOR>
 * @date 2021年3月15日
 */
@Service("VideoMediaMonitorServiceImpl")
public class VideoMediaMonitorServiceImpl extends AbstractMessageRecvService {
	
	private static final Logger log = LoggerFactory.getLogger(VideoMediaMonitorServiceImpl.class);
	
	@Override
	public void execute(Meta meta, String messageBody) throws SystemBaseException {
		HaLog.info(log, MsgIdConstant.MS_INF_0001, meta.getEventType());
		
		// 获取到的json业务数据
		JSONObject inputJson = JSONObject.parseObject(messageBody);
		
		// 根据不同cmdtype
		String cmdtype = inputJson.getString("cmdtype");
		//20001代表摄像机资源，20056,20057,20058代表存储资源
		//查找对应资源类型
		if(StringUtils.equals("20001", cmdtype)){
			String uid = inputJson.getString("cameraid");
			HashMap<String, VideoMediaResource> camCacheMap = VideoMediaMonitorCache.getCamCacheMap();
//			String result = HttpRequestUtil.sendPost("http://192.168.112.163:2201/api/bs/equ/info/open/cameraList", uid);
//			JSONObject resJson = JSONObject.parseObject(result);
//			JSONArray statusArray = resJson.getJSONArray("datalist");
			VideoMediaResource videoMediaResource = camCacheMap.get(uid);
			if (null == videoMediaResource) {
				camCacheMap = VideoMediaMonitorCache.getUnpdatedCamCacheMap();
				videoMediaResource = camCacheMap.get(uid);
			}
			inputJson.put("alarmResourceId", videoMediaResource.getEquCode());
			inputJson.put("alarmResourceName", videoMediaResource.getEquName());
			inputJson.put("ipAdress", videoMediaResource.getEquIp());
		} else if (StringUtils.equals("20056", cmdtype)
				|| StringUtils.equals("20057", cmdtype) 
				|| StringUtils.equals("20058", cmdtype)) {
			String uid = inputJson.getString("serviceid");
			HashMap<String, VideoMediaResource> cacheMap = VideoMediaMonitorCache.getResCacheMap();
			VideoMediaResource videoMediaResource = cacheMap.get(uid);
			if (videoMediaResource == null) {
				cacheMap = VideoMediaMonitorCache.getUnpdatedResCacheMap();
				videoMediaResource = cacheMap.get(uid);
			} 
			inputJson.put("alarmResourceId", videoMediaResource.getUuid());
			inputJson.put("alarmResourceName", videoMediaResource.getServiceName());
			inputJson.put("ipAdress", videoMediaResource.getServiceIp());
		}
		// 响应json消息
		JSONObject outputMsg = new JSONObject();
		
		// 响应消息头
		outputMsg.put("meta", meta);
		
		// 响应消息体
		outputMsg.put("body", inputJson);
		
		// 转换为JSON格式，并发送消息到指定主题
		super.putSendMessage(outputMsg);
		
		HaLog.info(log, MsgIdConstant.MS_INF_0002, meta.getEventType());
	}

}
