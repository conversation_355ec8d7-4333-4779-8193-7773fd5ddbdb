package com.air.security.sodb.data.ifdomian.dcs.api;

import java.util.ArrayList;

import com.thoughtworks.xstream.annotations.XStreamAlias;

/**
 * @Description:
 * @author: zhangc
 * @Date: 2019年3月10日
 */
@XStreamAlias("PAYLOAD")
public class Payload {

    @XStreamAlias("AIRLCD")
    private String airlcd;

    @XStreamAlias("FLTNBR")
    private String fltnbr;

    @XStreamAlias("FLTSFX")
    private String fltsfx;

    @XStreamAlias("FLTDATE")
    private String fltdate;

    @XStreamAlias("DEPTCD")
    private String deptcd;

    @XStreamAlias("ARRVCD")
    private String arrvcd;

    @XStreamAlias("PAXLIST")
    private ArrayList<Paxitem> paxlist;

    /**
     * @return the airlcd
     */
    public String getAirlcd() {
        return airlcd;
    }

    /**
     * @param airlcd the airlcd to set
     */
    public void setAirlcd(String airlcd) {
        this.airlcd = airlcd;
    }

    /**
     * @return the fltnbr
     */
    public String getFltnbr() {
        return fltnbr;
    }

    /**
     * @param fltnbr the fltnbr to set
     */
    public void setFltnbr(String fltnbr) {
        this.fltnbr = fltnbr;
    }

    /**
     * @return the fltsfx
     */
    public String getFltsfx() {
        return fltsfx;
    }

    /**
     * @param fltsfx the fltsfx to set
     */
    public void setFltsfx(String fltsfx) {
        this.fltsfx = fltsfx;
    }

    /**
     * @return the fltdate
     */
    public String getFltdate() {
        return fltdate;
    }

    /**
     * @param fltdate the fltdate to set
     */
    public void setFltdate(String fltdate) {
        this.fltdate = fltdate;
    }

    /**
     * @return the deptcd
     */
    public String getDeptcd() {
        return deptcd;
    }

    /**
     * @param deptcd the deptcd to set
     */
    public void setDeptcd(String deptcd) {
        this.deptcd = deptcd;
    }

    /**
     * @return the arrvcd
     */
    public String getArrvcd() {
        return arrvcd;
    }

    /**
     * @param arrvcd the arrvcd to set
     */
    public void setArrvcd(String arrvcd) {
        this.arrvcd = arrvcd;
    }

    /**
     * @return the paxlist
     */
    public ArrayList<Paxitem> getPaxlist() {
        return paxlist;
    }

    /**
     * @param paxlist the paxlist to set
     */
    public void setPaxlist(ArrayList<Paxitem> paxlist) {
        this.paxlist = paxlist;
    }


}
