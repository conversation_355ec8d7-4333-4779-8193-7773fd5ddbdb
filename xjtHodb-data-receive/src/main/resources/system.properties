kafka.listener.config.path=/itms/deploy/xjthodb/config/kafkaListener.json
kafka.consumer.config.path=/itms/deploy/xjthodb/config/kafkaConsumer.properties
rocketMq.producer.config.path=/itms/deploy/xjthodb/config/rocketMQProducer.properties
rocketMq.consumer.config.path=/itms/deploy/xjthodb/config/rocketMQConsumer.properties
xjwjsodb.stop.port=20016
#\u7EBF\u7A0B\u6C60\u5927\u5C0F
threadpool.count=20

bucket=datarecv
endpoint=http://10.62.27.26:9000
access_key=miniominio
secret_key=hayc@123456
FLIGHT_FLAG=true
FLIGHT_SERVICE_TYPE=FLIGHT
TRAFFIC_FLAG=true
TRAFFIC_SERVICE_TYPE=ZHJT
TAVELLER_FLAG=true
TAVELLER_TYPE=TAVELLER

SODB_FLAG=true
SODB_TYPE=SODB

WEATHER_FLAG=true
WEATHER_TYPE=ACDM

# \u5B89\u4FDD\u89C6\u9891\u4EBA\u6570\u7EDF\u8BA1\u8BBE\u5907\u4FE1\u606F
VIDEO.PERSON.NUM.EQU_CODE=65019907001324180004,65019907001324192131