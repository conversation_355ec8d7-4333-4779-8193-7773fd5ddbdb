#4.1.1.6\u822A\u73ED\u5171\u4EAB\u53D8\u66F4\u4E8B\u4EF6\uFF08SFLG\uFF09
SFLG=DfltMessageRecvFormatServiceImpl,iHodbFlight,SFLG,FLIGHT_DATA
#4.1.1.7\u822A\u73ED\u822A\u7EBF\u53D8\u66F4\u4E8B\u4EF6\uFF08AIRL\uFF09
AIRL=DfltMessageRecvFormatServiceImpl,iHodbFlight,AIRL,FLIGHT_DATA
#4.1.1.8\u822A\u73ED\u53F7\u53D8\u66F4\u4E8B\u4EF6\uFF08HBTT\uFF09
HBTT=DfltMessageRecvFormatServiceImpl,iHodbFlight,HBTT,FLIGHT_DATA
#4.1.1.9\u822A\u73ED\u524D\u7AD9\u8D77\u98DE\u4E8B\u4EF6\uFF08ONRE\uFF09
ONRE=DfltMessageRecvFormatServiceImpl,iHodbFlight,ONRE,FLIGHT_DATA
#4.1.1.10\u822A\u73ED\u5230\u8FBE\u672C\u7AD9\u4E8B\u4EF6\uFF08ARRE\uFF09
ARRE=DfltMessageRecvFormatServiceImpl,iHodbFlight,ARRE,FLIGHT_DATA
#4.1.1.11\u822A\u73ED\u672C\u7AD9\u8D77\u98DE\u4E8B\u4EF6\uFF08DEPE\uFF09
DEPE=DfltMessageRecvFormatServiceImpl,iHodbFlight,DEPE,FLIGHT_DATA
#4.1.1.13\u822A\u73ED\u5F00\u59CB\u503C\u673A\u4E8B\u4EF6\uFF08CKIE\uFF09
CKIE=DfltMessageRecvFormatServiceImpl,iHodbFlight,CKIE,FLIGHT_DATA
#4.1.1.14\u822A\u73ED\u622A\u6B62\u503C\u673A\u4E8B\u4EF6\uFF08CKOE\uFF09
CKOE=DfltMessageRecvFormatServiceImpl,iHodbFlight,CKOE,FLIGHT_DATA
#4.1.1.15\u822A\u73ED\u5F00\u59CB\u767B\u673A\u4E8B\u4EF6\uFF08BORE\uFF09
BORE=DfltMessageRecvFormatServiceImpl,iHodbFlight,BORE,FLIGHT_DATA
#4.1.1.16\u822A\u73ED\u8FC7\u7AD9\u767B\u673A\u4E8B\u4EF6\uFF08TBRE\uFF09
TBRE=DfltMessageRecvFormatServiceImpl,iHodbFlight,TBRE,FLIGHT_DATA
#4.1.1.17\u822A\u73ED\u50AC\u4FC3\u767B\u673A\u4E8B\u4EF6\uFF08LBDE\uFF09
LBDE=DfltMessageRecvFormatServiceImpl,iHodbFlight,LBDE,FLIGHT_DATA
#4.1.1.18\u822A\u73ED\u7ED3\u675F\u767B\u673A\u4E8B\u4EF6\uFF08POKE\uFF09
POKE=DfltMessageRecvFormatServiceImpl,iHodbFlight,POKE,FLIGHT_DATA
#4.1.1.19\u822A\u73ED\u5EF6\u8BEF\u4E8B\u4EF6\uFF08DLYE\uFF09
DLYE=DfltMessageRecvFormatServiceImpl,iHodbFlight,DLYE,FLIGHT_DATA
#*******0\u822A\u73ED\u53D6\u6D88\u4E8B\u4EF6\uFF08CANE\uFF09
CANE=DfltMessageRecvFormatServiceImpl,iHodbFlight,CANE,FLIGHT_DATA
#********\u822A\u73ED\u8FD4\u822A\u4E8B\u4EF6\uFF08RTNE\uFF09
RTNE=DfltMessageRecvFormatServiceImpl,iHodbFlight,RTNE,FLIGHT_DATA
#********\u822A\u73ED\u6ED1\u56DE\u4E8B\u4EF6\uFF08BAKE\uFF09
BAKE=DfltMessageRecvFormatServiceImpl,iHodbFlight,BAKE,FLIGHT_DATA
#********\u822A\u73ED\u5907\u964D\u4E8B\u4EF6\uFF08ALTE\uFF09
ALTE=DfltMessageRecvFormatServiceImpl,iHodbFlight,ALTE,FLIGHT_DATA
#********\u822A\u73ED\u66F4\u6362\u98DE\u673A\u4E8B\u4EF6\uFF08CFCE\uFF09
CFCE=DfltMessageRecvFormatServiceImpl,iHodbFlight,CFCE,FLIGHT_DATA
#********\u822A\u73EDVIP\u4E8B\u4EF6\uFF08VIP\uFF09
VIP=DfltMessageRecvFormatServiceImpl,iHodbFlight,VIP,FLIGHT_DATA
#********\u822A\u73ED\u767B\u673A\u95E8\u52A8\u6001\u4FE1\u606F\u66F4\u65B0\u4E8B\u4EF6\uFF08GTLS\uFF09
GTLS=DfltMessageRecvFormatServiceImpl,iHodbFlight,GTLS,FLIGHT_DATA
#********\u822A\u73ED\u884C\u674E\u63D0\u53D6\u8F6C\u76D8\u52A8\u6001\u4FE1\u606F\u66F4\u65B0\u4E8B\u4EF6\uFF08BLLS\uFF09
BLLS=DfltMessageRecvFormatServiceImpl,iHodbFlight,BLLS,FLIGHT_DATA
#********\u822A\u73ED\u503C\u673A\u67DC\u53F0\u52A8\u6001\u4FE1\u606F\u66F4\u65B0\u4E8B\u4EF6\uFF08CKLS\uFF09
CKLS=DfltMessageRecvFormatServiceImpl,iHodbFlight,CKLS,FLIGHT_DATA
#********\u822A\u73ED\u673A\u4F4D\u52A8\u6001\u4FE1\u606F\u66F4\u65B0\u4E8B\u4EF6\uFF08STLS\uFF09
STLS=DfltMessageRecvFormatServiceImpl,iHodbFlight,STLS,FLIGHT_DATA
#********\u822A\u73ED\u8BA1\u5212\u65F6\u95F4\u4E8B\u4EF6\uFF08FPTT\uFF09
FPTT=DfltMessageRecvFormatServiceImpl,iHodbFlight,FPTT,FLIGHT_DATA
#********\u822A\u73ED\u9884\u8BA1\u65F6\u95F4\u4E8B\u4EF6\uFF08FETT\uFF09
FETT=DfltMessageRecvFormatServiceImpl,iHodbFlight,FETT,FLIGHT_DATA
#********\u822A\u73ED\u5B9E\u9645\u65F6\u95F4\u4E8B\u4EF6\uFF08FRTT\uFF09
FRTT=DfltMessageRecvFormatServiceImpl,iHodbFlight,FRTT,FLIGHT_DATA
#********\u822A\u73ED\u8DD1\u9053\u53D8\u66F4\u4E8B\u4EF6\uFF08RWAY\uFF09
RWAY=DfltMessageRecvFormatServiceImpl,iHodbFlight,RWAY,FLIGHT_DATA
#********\u822A\u73ED\u822A\u7AD9\u697C\u53D8\u66F4\u4E8B\u4EF6\uFF08TRML\uFF09
TRML=DfltMessageRecvFormatServiceImpl,iHodbFlight,TRML,FLIGHT_DATA
#4.1.1.37\u822A\u73ED\u5C5E\u6027\u53D8\u66F4\u4E8B\u4EF6\uFF08FATT\uFF09
FATT=DfltMessageRecvFormatServiceImpl,iHodbFlight,FATT,FLIGHT_DATA
#4.1.1.1\u822A\u73ED\u52A8\u6001\u589E\u52A0\u4E8B\u4EF6\u8BF4\u660E\uFF08DFIE\uFF09
DFIE=DfltMessageRecvFormatServiceImpl,iHodbFlight,DFIE,FLIGHT_DATA
#*******\u52A8\u6001\u5220\u9664\u4E8B\u4EF6\u8BF4\u660E\uFF08DFDE\uFF09
DFDE=DfltMessageRecvFormatServiceImpl,iHodbFlight,DFDE,FLIGHT_DATA
#*******\u52A8\u6001\u822A\u73ED\u6574\u8868\u540C\u6B65\u4E8B\u4EF6\uFF08DFDL\uFF09
DFDL=DfltMessageRecvFormatServiceImpl,iHodbFlight,DFDL,FLIGHT_DATA
#********\u822A\u73ED\u884C\u674E\u6ED1\u69FD\u53E3\u52A8\u6001\u4FE1\u606F\u66F4\u65B0\u4E8B\u4EF6\uFF08CHLS\uFF09
CHLS=DfltMessageRecvFormatServiceImpl,iHodbFlight,CHLS,FLIGHT_DATA
NXTE=DfltMessageRecvFormatServiceImpl,iHodbFlight,NXTE,FLIGHT_DATA

IHOE=MessageRecvFormatServiceImpl,cContainerHodbMember,VIP_RESERVATION_ADD,MEMBER
UHOE=MessageRecvFormatServiceImpl,cContainerHodbMember,VIP_RESERVATION_UPDATE,MEMBER
DHOE=MessageRecvFormatServiceImpl,cContainerHodbMember,VIP_RESERVATION_DELETE,MEMBER
BDPL=MessageRecvFormatServiceImpl,cContainerHodbParkBasic,PARK_BASIC,PARK
PA=MessageRecvFormatServiceImpl,cContainerHodbParkBasic,PARK_AREA,PARK
PLENR=MessageRecvFormatServiceImpl,cContainerHodbParkEntry,PARK_ENTRY,PARK
PLEXR=MessageRecvFormatServiceImpl,cContainerHodbParkExit,PARK_EIXT,PARK
PLTR=MessageRecvFormatServiceImpl,cContainerHodbParkCharge,PARK_CHARGE,PARK
PLF=MessageRecvFormatServiceImpl,cContainerHodbParkFlow,PARK_FLOW,PARK
PLAR=MessageRecvFormatServiceImpl,cContainerHodbParkBasic|cContainerHodbParkSpace,PARK_REAL|PARK_SPACE,PARK|PARK

#******* \u822A\u73ED\u8BA1\u5212\u6574\u8868\u540C\u6B65\u4E8B\u4EF6\uFF08FPDL\uFF09
FPDL = FpltMessageRecvFormatServiceImpl,iHodbFlight,FPDL,FLIGHT_DATA

#\u63A5\u5165\u5929\u6C14\u534A\u5C0F\u65F6\u4E8B\u4EF6
SASPH = MessageRecvFormatServiceImpl,cContainerWeather,WEATHER_INFO,WEATHER

# \u89C6\u9891\u5206\u6790\u4EBA\u6570\u63A8\u9001 T_VIDEO_PERSON_NUMBER
VIDEO_ANALYSE_DISTRIBUTE=AnalyseResultMsg,,topic-esbrcv-sodb
# \u89C6\u9891\u5206\u6790\u6392\u961F\u957F\u5EA6\u6570\u636E\u63A8\u9001
VIDEO_ANALYSE_QUEUELENGTH=,,topic-esbrcv-sodb

#\u535A\u897F\u5C3C\u505C\u8F66\u573A\u7CFB\u7EDF
TAXI_ENTRY=MessageRecvFormatServiceImpl,cContainerTaxiEntry,TAXI_ENTRY,TAXI_INFO
TAXI_EXIT=MessageRecvFormatServiceImpl,cContainerTaxiExit,TAXI_EXIT,TAXI_INFO
TAXI_BUILD=MessageRecvFormatServiceImpl,cContainerTaxiBuild,TAXI_BUILD,TAXI_INFO

