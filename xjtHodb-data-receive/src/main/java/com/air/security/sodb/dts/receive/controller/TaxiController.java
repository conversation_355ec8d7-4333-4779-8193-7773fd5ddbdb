package com.air.security.sodb.dts.receive.controller;

import com.air.security.sodb.data.core.base.Meta;
import com.air.security.sodb.data.core.mq.MyProducer;
import com.air.security.sodb.data.core.mq.impl.SyncRocketMQProducer;
import com.air.security.sodb.dts.receive.server.MqMessageRecvServer;
import com.air.security.sodb.dts.receive.server.MqMessageRecvServerImpl;
import com.air.security.sodb.dts.srvc.domain.dto.ResponseDto;
import org.slf4j.Logger;
import org.slf4j.LoggerFactory;
import org.springframework.web.bind.annotation.PostMapping;
import org.springframework.web.bind.annotation.RequestBody;
import org.springframework.web.bind.annotation.RequestMapping;
import org.springframework.web.bind.annotation.RestController;


/**
 * 出租车-博西尼停车场系统
 */
@RestController
@RequestMapping(path = "/api/taxi")
public class TaxiController {
    private static final Logger log = LoggerFactory.getLogger(TaxiController.class);

    private static MqMessageRecvServer buildMqMessageRecvServer() {
        MyProducer producer = new SyncRocketMQProducer();
        return new MqMessageRecvServerImpl(producer);
    }

    private static final MqMessageRecvServer server = buildMqMessageRecvServer();

    public ResponseDto processVerifyRecord(String paramString, String msgType, String eventType) {
        log.info("收到博西尼停车场系统数据{}：{}",eventType,paramString);
        try {
            Meta meta = new Meta();
            meta.setMsgType(msgType);
            meta.setEventType(eventType);
            server.handle(meta, paramString);
            return ResponseDto.success();
        } catch (Exception e) {
            log.error("处理 {} 时出错", eventType, e);
            return ResponseDto.error();
        } finally {
            log.info("{} 结束", eventType);
        }
    }

    @PostMapping("/receiveTaxiEntryInfo")
    public ResponseDto saveOnlineMessage(@RequestBody String paramString) {
        return processVerifyRecord(paramString, "TAXI_INFO","TAXI_ENTRY");
    }

    @PostMapping("/receiveTaxiExitInfo")
    public ResponseDto saveOfflineMessage(@RequestBody String paramString) {
        return processVerifyRecord(paramString, "TAXI_INFO","TAXI_EXIT");
    }

    @PostMapping("/receiveTaxiParkBuildInfo")
    public ResponseDto saveLocationMessage(@RequestBody String paramString) {
        return processVerifyRecord(paramString, "TAXI_INFO","TAXI_BUILD");
    }

}