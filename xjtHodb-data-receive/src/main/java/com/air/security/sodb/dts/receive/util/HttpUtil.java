package com.air.security.sodb.dts.receive.util;


import org.apache.commons.lang3.StringUtils;
import org.slf4j.Logger;
import org.slf4j.LoggerFactory;
import org.springframework.http.HttpEntity;
import org.springframework.http.HttpHeaders;
import org.springframework.web.context.request.RequestContextHolder;
import org.springframework.web.context.request.ServletRequestAttributes;

import javax.servlet.http.Cookie;
import javax.servlet.http.HttpServletRequest;
import java.net.InetAddress;


/**
 * 处理HTTP请求相关的工具类
 *
 * <AUTHOR>
 */
public class HttpUtil {

    private static final Logger log = LoggerFactory.getLogger(HttpUtil.class);

    /**
     * 封装http请求实体参数
     *
     * @param request 请求头
     * @param obj     请求参数对象
     * @return
     */
    public HttpEntity<Object> getHttpEntity(HttpServletRequest request, Object obj) {
        String cookieValue = getUserLoginCookieValue(request);
        HttpHeaders headers = new HttpHeaders();
        headers.set(HaConstant.HAYC_USER_LOGIN_KEY, cookieValue);
        headers.setContentType(HaConstant.APPLICATION_JSON_UTF8);
        return new HttpEntity(obj, headers);
    }

    /**
     * 获取组件框架用户登录cookie标识
     *
     * @param request
     * @return
     */
    public static String getUserLoginCookieValue(HttpServletRequest request) {
        if (null == request) {
            return null;
        }
        String cookieValue = null;
        //获取cookie信息
        Cookie[] cookies = request.getCookies();
        if (cookies != null && cookies.length > 0) {
            for (Cookie cookie : cookies) {
                if (HaConstant.HAYC_USER_LOGIN_KEY.equalsIgnoreCase(cookie.getName())) {
                    cookieValue = cookie.getValue();
                    break;
                }
            }
        }
        //如果cookies为空 尝试从header中获取用户登录标识
        if (StringUtils.isBlank(cookieValue)) {
            cookieValue = request.getHeader(HaConstant.HAYC_USER_LOGIN_KEY);
        }
        return cookieValue;
    }

    /**
     * 获取request
     *
     * @return
     */
    public HttpServletRequest getRequest() {
        return ((ServletRequestAttributes) RequestContextHolder.getRequestAttributes()).getRequest();
    }

    /**
     * 获取项目名ContextPath  ------houjl add
     *
     * @param request
     * @return
     */
    public static String getContextPath(HttpServletRequest request) {
        String contextPath = request.getContextPath();
        return contextPath.substring(1, contextPath.length());
    }

    /**
     * 返回主机IP地址
     *
     * @return
     */
    public static String getHostIp() {
        String ip = null;
        try {
            ip = InetAddress.getLocalHost().getHostAddress();
        } catch (Exception e) {
            if (log.isErrorEnabled()) {
                log.error("获取主机IP地址失败！", e);
            }
        }
        return ip;
    }

    public static HttpUtil getInstance() {
        return new HttpUtil();
    }

}
