package com.air.security.sodb.dts.srvc.restsrvc.platform.flight.dto;

import io.swagger.annotations.ApiModelProperty;

/**
 * <AUTHOR>
 */
public class FlightRqdfDto {

    /**
     * 机场四字码
     */
    @ApiModelProperty(value = "机场四字码")
    private String airportIcaoCode;

    /**
     * 请求类型
     */
    @ApiModelProperty(value = "请求类型：FLID 单条航班请求， DFTM 时间段航班请求，为空 表示整表请求")
    private String queryType;

    /**
     * 航班原id
     */
    @ApiModelProperty(value = "航班原id")
    private String sourceflightId;

    /**
     * 查询开始时间
     */
    @ApiModelProperty(value = "查询开始时间yyyy-MM-dd HH:mm:ss")
    private String startTime;

    /**
     * 查询结束时间
     */
    @ApiModelProperty(value = "查询结束时间yyyy-MM-dd HH:mm:ss")
    private String endTime;

    public String getAirportIcaoCode() {
        return airportIcaoCode;
    }

    public void setAirportIcaoCode(String airportIcaoCode) {
        this.airportIcaoCode = airportIcaoCode;
    }

    public String getQueryType() {
        return queryType;
    }

    public void setQueryType(String queryType) {
        this.queryType = queryType;
    }

    public String getSourceflightId() {
        return sourceflightId;
    }

    public void setSourceflightId(String sourceflightId) {
        this.sourceflightId = sourceflightId;
    }

    public String getStartTime() {
        return startTime;
    }

    public void setStartTime(String startTime) {
        this.startTime = startTime;
    }

    public String getEndTime() {
        return endTime;
    }

    public void setEndTime(String endTime) {
        this.endTime = endTime;
    }
}
