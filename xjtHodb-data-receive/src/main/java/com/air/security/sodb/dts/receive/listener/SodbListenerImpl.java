package com.air.security.sodb.dts.receive.listener;

import com.air.security.sodb.data.core.constant.MsgIdConstant;
import com.air.security.sodb.data.core.mq.MyProducer;
import com.air.security.sodb.data.core.mq.impl.OrderRocketMQProducer;
import com.air.security.sodb.data.core.util.HaLog;
import com.air.security.sodb.data.core.util.PropertyUtil;
import com.air.security.sodb.dts.receive.dto.Section2Meta;
import com.air.security.sodb.dts.receive.exception.IgnoreException;
import com.alibaba.fastjson.JSONObject;
import com.caacitc.rabbitmq.listener.AbstractMessageListener;
import org.apache.commons.lang3.ArrayUtils;
import org.apache.commons.lang3.StringUtils;
import org.slf4j.Logger;
import org.slf4j.LoggerFactory;

/**
 * <AUTHOR>
 * @date 2025/6/18
 */
public class SodbListenerImpl extends AbstractMessageListener {
    private static final Logger log = LoggerFactory.getLogger(SodbListenerImpl.class);
    private String serviceType;
    private final MyProducer producer = new OrderRocketMQProducer();

    public SodbListenerImpl(String serviceType, boolean autoAck) {
        super(serviceType, autoAck);
        this.serviceType = serviceType;
    }

    @Override
    public Boolean handleMessage(String message) {
        HaLog.info(log, MsgIdConstant.MS_INF_0001, "SODB事件消息监听");
        String topicName = null;

        try {
            HaLog.infoJson(log, message);
            JSONObject jsonObject = JSONObject.parseObject(message);
            Section2Meta dataMeta = jsonObject.getObject("meta", Section2Meta.class);
            if (dataMeta != null && !StringUtils.isBlank(dataMeta.getEventType())) {
                String stypeProp = PropertyUtil.getProperty(dataMeta.getEventType());
                if (StringUtils.isEmpty(stypeProp)) {
                    throw new IgnoreException("消息类型（" + dataMeta.getEventType() + "）对应配置信息为空");
                } else {
                    String[] prop = stypeProp.split(",");
                    if (prop.length < 3) {
                        HaLog.error(log, MsgIdConstant.MS_WAR_0003, "消息类型（" + dataMeta.getEventType() + "）配置文件");
                        return false;
                    } else {
                        String beanName = prop[0];
                        topicName = prop[2];
                        if(this.validData(dataMeta, jsonObject)) {
                            // 验证通过，转发数据
                            this.producer.sendMsg(topicName, message);
                        }

                        //            Meta meta = new Meta();
                        //            meta.setEventType(dataMeta.getEventType());
                        //            meta.setRecvSequence(UuidUtil.getUuid32());
                        //            // 消息分发处理器
                        //            MqMessageRecvServer server = new MqMessageRecvServerImpl(producer);
                        //            server.handle(meta, jsonObject.toString());
                        HaLog.info(log, MsgIdConstant.MS_INF_0002, "SODB事件消息监听");
                        return true;
                    }
                }
            } else {
                return false;
            }
        } catch (Exception e) {
            HaLog.error(log, e, MsgIdConstant.MS_ERR_0001);
            return e instanceof IgnoreException;
        }
    }

    /**
     * 视频分析人数推送 接入设备列表
     */
    private final String[] videoPersonNumEquCodes = PropertyUtil.getPropertyArrayValue("VIDEO.PERSON.NUM.EQU_CODE", ",");
    {
        if(ArrayUtils.isEmpty(videoPersonNumEquCodes)) {
            log.error("视频分析人数推送 接入设备列表信息为空。");
        }
    }

    private boolean validData(Section2Meta dataMeta, JSONObject jsonObject) {
        if("VIDEO_ANALYSE_DISTRIBUTE".equals(dataMeta.getEventType())) {
            // 视频分析人数推送
            JSONObject body = jsonObject.getJSONObject("body");
            if(body == null) {
                return false;
            }
            String equCode = body.getString("equCode"); // 获取设备编码
            for (String videoPersonNumEquCode : videoPersonNumEquCodes) {
                if(videoPersonNumEquCode.equals(equCode)) {
                    return true; // 只处理：交通中心三层入口门禁
                }
            }
            return false;
        }
        return true;
    }
}
