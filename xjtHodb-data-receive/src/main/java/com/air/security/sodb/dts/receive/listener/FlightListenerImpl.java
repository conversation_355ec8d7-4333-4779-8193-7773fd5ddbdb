package com.air.security.sodb.dts.receive.listener;

import com.air.security.sodb.data.core.base.Meta;
import com.air.security.sodb.data.core.constant.MsgIdConstant;
import com.air.security.sodb.data.core.mq.MyProducer;
import com.air.security.sodb.data.core.mq.impl.OrderRocketMQProducer;
import com.air.security.sodb.data.core.util.HaLog;
import com.air.security.sodb.data.core.util.UuidUtil;
import com.air.security.sodb.dts.receive.server.MqMessageRecvServer;
import com.air.security.sodb.dts.receive.server.MqMessageRecvServerImpl;
import com.caacitc.rabbitmq.listener.AbstractMessageListener;
import org.apache.commons.lang3.StringUtils;
import org.json.JSONArray;
import org.json.JSONObject;
import org.json.XML;
import org.json.XMLParserConfiguration;
import org.slf4j.Logger;
import org.slf4j.LoggerFactory;

public class FlightListenerImpl extends AbstractMessageListener {

    private static final Logger log = LoggerFactory.getLogger(FlightListenerImpl.class);
    private String serviceType;

    /**
     * RocketMQ生产者
     */
    private MyProducer producer = new OrderRocketMQProducer();

    public FlightListenerImpl(String serviceType, boolean autoAck) {
        super(serviceType, autoAck);
        this.serviceType = serviceType;
    }

    @Override
    public Boolean handleMessage(String message) {
        HaLog.info(log, MsgIdConstant.MS_INF_0001, "航班事件消息监听");
        try {
            // 接收消息日志
            HaLog.infoJson(log, message);
            JSONObject jsonObject = XML.toJSONObject(message);
            this.cleanNilValues(jsonObject);
            HaLog.infoJson(log, jsonObject.toString());
            String eventType = jsonObject.getJSONObject("MSG").getJSONObject("META").getString("STYP");

            if (StringUtils.isBlank(eventType)) {
                return false;
            }
            Meta meta = new Meta();
            meta.setEventType(eventType);
            meta.setRecvSequence(UuidUtil.getUuid32());
            // 消息分发处理器
            MqMessageRecvServer server = new MqMessageRecvServerImpl(producer);
            server.handle(meta, jsonObject.toString());
            HaLog.info(log, MsgIdConstant.MS_INF_0002, "航班事件消息监听");
            return true;
        } catch (Exception e) {
            e.printStackTrace();
            HaLog.error(log, MsgIdConstant.MS_ERR_0001, e, "消息处理异常");
            return false;
        }
    }
    private void cleanNilValues(Object obj) {
        if (obj instanceof JSONObject) {
            JSONObject json = (JSONObject) obj;
            for (String key : json.keySet()) {
                Object value = json.get(key);

                // 递归处理 JSONObject
                if (value instanceof JSONObject) {
                    JSONObject childObj = (JSONObject) value;
                    if (childObj.has("xsi:nil") || childObj.has("xmlns:xsi")) {
                        json.put(key, ""); // 替换为空字符串
                    } else {
                        cleanNilValues(childObj);
                    }
                }

                // 递归处理 JSONArray
                else if (value instanceof JSONArray) {
                    JSONArray array = (JSONArray) value;
                    for (int i = 0; i < array.length(); i++) {
                        cleanNilValues(array.get(i));
                    }
                }
            }
        }
    }
}
