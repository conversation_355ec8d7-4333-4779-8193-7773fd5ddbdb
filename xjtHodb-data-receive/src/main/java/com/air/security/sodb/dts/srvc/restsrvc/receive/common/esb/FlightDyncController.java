package com.air.security.sodb.dts.srvc.restsrvc.receive.common.esb;

import com.air.security.sodb.data.core.constant.MsgIdConstant;
import com.air.security.sodb.data.core.util.*;
import com.air.security.sodb.dts.srvc.restsrvc.platform.flight.dto.FlightRqdfDto;
import com.caacitc.esb.constants.EsbPropertyKeyConst;
import com.caacitc.esb.dto.ProducerSendResult;
import com.caacitc.rabbitmq.client.EsbClient;
import org.apache.commons.lang3.StringUtils;
import org.slf4j.Logger;
import org.slf4j.LoggerFactory;
import org.springframework.web.bind.annotation.*;

import java.text.ParseException;
import java.text.SimpleDateFormat;
import java.util.Date;
import java.util.Properties;
import java.util.UUID;

/**
 * <AUTHOR>
 * @date 2021/11/20
 */
@CrossOrigin
@RestController
@RequestMapping("/api/release/flight/flight-dync-event")
public class FlightDyncController {

    private static final Logger log = LoggerFactory.getLogger(FlightDyncController.class);
    /**
     * kafka配置文件
     */
    private static Properties properties = ResourceUtil.getInstance().getProperties("config/esb.properties");

    @RequestMapping(value = "/execute", method = RequestMethod.POST)
    public ResultVo execute(@RequestBody FlightRqdfDto flightRqdfDto) {
        HaLog.info(log, MsgIdConstant.MS_INF_0001, "航班全表同步消息请求监听");
        SimpleDateFormat dateFormat = new SimpleDateFormat("yyyyMMddHHmmssSSS");
        String nowTime = dateFormat.format(new Date());
        String startTime = "";
        String endTime = "";
        try {
            if (StringUtils.isNotBlank(flightRqdfDto.getStartTime())) {
                startTime = DateTimeUtil.parseDateStr(flightRqdfDto.getStartTime(),
                        "yyyy-MM-dd HH:mm:ss",
                        "yyyyMMddHHmmss");
            }
            if (StringUtils.isNotBlank(flightRqdfDto.getEndTime())) {
                endTime = DateTimeUtil.parseDateStr(flightRqdfDto.getEndTime(),
                        "yyyy-MM-dd HH:mm:ss",
                        "yyyyMMddHHmmss");
            }
        } catch (ParseException e) {
            ResultMessage resultMessage = new ResultMessage("查询时间转换失败，日期格式yyyy-MM-dd HH:mm:ss");
            return ResultVo.failure(resultMessage);
        }
        String msg = "<MSG>" +
                "    <META>" +
                "        <SNDR>" + properties.getProperty(EsbPropertyKeyConst.ESB_SDK_CLIENT_ENDPOINT) + "</SNDR>" +
                "        <RCVR></RCVR>" +
                "        <SEQN>1</SEQN>" +
                "        <DDTM>" + nowTime + "</DDTM>" +
                "        <TYPE>REQE</TYPE>" +
                "        <STYP>RQDF</STYP>" +
                "        <MGID>" + UUID.randomUUID().toString().replace("-", "") + "</MGID>" +
                "        <RMID/>" +
                "        <APOT>" + flightRqdfDto.getAirportIcaoCode() + "</APOT>" +
                "    </META>" +
                "    <RQTP>" +
                "        <RQTP>DFTM</RQTP>" +
                "        <STTM>" + startTime + "</STTM>" +
                "        <EDTM>" + endTime + "</EDTM>" +
                "    </RQTP>" +
                "</MSG>";

        try {
            ProducerSendResult result = EsbClient.getInstance.producer(msg, properties);
            String sendState = result.getSendState();
            System.out.println("发送结果： " + sendState + "###" + result.getSendDesc());
            if ("200".equalsIgnoreCase(sendState)) {
                return ResultVo.success();
            } else {
                String meg = "发送结果： " + sendState + "###" + result.getSendDesc();
                ResultMessage resultMessage = new ResultMessage(meg);
                HaLog.info(log, MsgIdConstant.MS_INF_0002, "航班全表同步消息请求监听");
                return ResultVo.failure(resultMessage);
            }
        } catch (Exception e) {
            return ResultVo.failure();
        }
    }
}
