package com.air.security.sodb.dts;

import com.air.security.sodb.data.core.constant.MsgIdConstant;
import com.air.security.sodb.data.core.quartz.SchedulerManager;
import com.air.security.sodb.data.core.util.HaLog;
import com.air.security.sodb.data.core.util.PropertyUtil;
import com.air.security.sodb.data.core.util.ThreadUtil;
import com.air.security.sodb.dts.receive.listener.KafkaCargoListener;
import com.air.security.sodb.dts.receive.listener.KafkaFlightMsgListener;
import com.air.security.sodb.dts.receive.listener.KafkaPsgrMsgListener;
import org.slf4j.Logger;
import org.slf4j.LoggerFactory;
import org.springframework.boot.ApplicationArguments;
import org.springframework.boot.ApplicationRunner;
import org.springframework.boot.SpringApplication;
import org.springframework.boot.autoconfigure.SpringBootApplication;
import org.springframework.context.annotation.ImportResource;

/**
 * @Description 于田数据接入服务
 * <AUTHOR>
@ImportResource("classpath:/applicationContext.xml")
@SpringBootApplication(scanBasePackages = "com.air.security.sodb")
public class YtRecvApplication implements ApplicationRunner {

    private static final Logger log = LoggerFactory.getLogger(YtRecvApplication.class);

    private static boolean psgrService = Boolean.parseBoolean(PropertyUtil.getProperty("psgr.service"));
    private static boolean flightService = Boolean.parseBoolean(PropertyUtil.getProperty("flight.service"));
    private static boolean cargoService = Boolean.parseBoolean(PropertyUtil.getProperty("cargo.service"));

    public static void main(String[] args) {
        SpringApplication.run(YtRecvApplication.class, args);

    }

    /**
     * 初始化
     * @param args
     * @throws Exception
     */
    @Override
    public void run(ApplicationArguments args) throws Exception {
        // 初始化spring
        if (psgrService) {
            ThreadUtil.runWithNewThread(new KafkaPsgrMsgListener());

        }
        if (flightService) {
            ThreadUtil.runWithNewThread(new KafkaFlightMsgListener());
        }

        if (cargoService) {
            ThreadUtil.runWithNewThread(new KafkaCargoListener());
        }

        // 启动定时任务
        runQuartzJob();

    }

    /**
     * 启动定时任务
     */
    private static void runQuartzJob() {
        HaLog.info(log, MsgIdConstant.MS_INF_0001, "quartz定时任务");
        SchedulerManager manager = new SchedulerManager();
        manager.runJob("quartzJob.xml");
        HaLog.info(log, MsgIdConstant.MS_INF_0002, "quartz定时任务");
    }

}
