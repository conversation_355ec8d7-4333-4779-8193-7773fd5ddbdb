package com.air.security.sodb.dts.receive.util;

import com.air.security.sodb.data.core.constant.MsgIdConstant;
import com.air.security.sodb.data.core.util.HaLog;
import com.air.security.sodb.data.core.util.PropertyUtil;
import com.hikvision.artemis.sdk.ArtemisHttpUtil;
import com.hikvision.artemis.sdk.config.ArtemisConfig;
import org.slf4j.Logger;
import org.slf4j.LoggerFactory;

import java.util.HashMap;
import java.util.Map;
/**
 * 海康服务统一认证数据
 * <AUTHOR>
public class CertificationInfoUtil {

    private static final Logger log = LoggerFactory.getLogger(CertificationInfoUtil.class);
    /**
     * 海康服务ip:port
     */
    public static final String HOST = PropertyUtil.getProperty("rest.hk.host");
    /**
     * 海康服务appkey
     */
    public static final String APPKEY = PropertyUtil.getProperty("rest.hk.appKey");
    /**
     * 海康服务appsecret
     */
    public static final String APPSECRET = PropertyUtil.getProperty("rest.hk.appSecret");

    /**
     * 消息分发处理
     */
    public static String send(String url, String body) {

        HaLog.info(log, MsgIdConstant.MS_INF_0001, "统一认证数据接入");

        /**
         * STEP1：设置平台参数，根据实际情况,设置host appkey appsecret 三个参数.
         */
        ArtemisConfig.host = HOST;
        ArtemisConfig.appKey = APPKEY;
        ArtemisConfig.appSecret = APPSECRET;
        /**
         * STEP2：设置OpenAPI接口的上下文
         */
        final String artemisPath = "/artemis";
        /**
         * STEP3：设置接口的URI地址
         */
        String previewUrlsApi = artemisPath + url;
        Map<String, String> path = new HashMap<String, String>(2) {
            {
                put("https://", previewUrlsApi);//根据现场环境部署确认是http还是https
            }
        };
        /**
         * STEP4：设置参数提交方式
         */
        String contentType = "application/json";
        /**
         * STEP6：调用接口
         */
        String result = ArtemisHttpUtil.doPostStringArtemis(path, body, null, null, contentType, null);
        HaLog.info(log, MsgIdConstant.MS_INF_0001, "统一认证数据接入");
        return result;
    }
}
