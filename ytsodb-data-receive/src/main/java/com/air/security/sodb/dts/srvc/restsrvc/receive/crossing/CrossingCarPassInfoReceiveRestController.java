package com.air.security.sodb.dts.srvc.restsrvc.receive.crossing;

import com.air.security.sodb.data.core.base.Meta;
import com.air.security.sodb.data.core.base.ReceiveErrorType;
import com.air.security.sodb.data.core.constant.MsgIdConstant;
import com.air.security.sodb.data.core.util.HaLog;
import com.air.security.sodb.data.core.util.PropertyUtil;
import com.air.security.sodb.data.core.util.RequestUtil;
import com.air.security.sodb.data.core.util.UuidUtil;
import com.air.security.sodb.dts.receive.server.MqMessageRecvServer;
import com.air.security.sodb.dts.receive.server.MqMessageRecvServerImpl;
import com.air.security.sodb.dts.receive.util.ResultDTO;
import com.alibaba.fastjson.JSONObject;
import org.slf4j.Logger;
import org.slf4j.LoggerFactory;
import org.springframework.web.bind.annotation.RequestMapping;
import org.springframework.web.bind.annotation.RequestMethod;
import org.springframework.web.bind.annotation.RestController;

import javax.servlet.http.HttpServletRequest;
import javax.servlet.http.HttpServletResponse;

/**
 * @description:道口过车数据接入解析
 * @author: mk
 * @date: 2019/11/27 10:53
 */
@RestController
@RequestMapping("/api/release/crossingCarPassReceive/execute")
public class CrossingCarPassInfoReceiveRestController {
	private static final Logger log = LoggerFactory.getLogger(CrossingCarPassInfoReceiveRestController.class);

	private static String crossingCarPass = PropertyUtil.getProperty("crossingCarPass.service.code");

	/**
	 *
	 */
	private MqMessageRecvServer server = new MqMessageRecvServerImpl();


	/**
	 * 接口
	 */
	@RequestMapping(value = "/upload", method = RequestMethod.POST)
	public Object execute(HttpServletRequest request, HttpServletResponse response) {
		JSONObject jsonObj = RequestUtil.requestGetJson(request);
		ResultDTO<JSONObject> responseDto = new ResultDTO<JSONObject>();
		HaLog.info(log, MsgIdConstant.MS_INF_0001, "道口过车数据接入接口");
		HaLog.info(log, MsgIdConstant.MS_INF_0001, "道口过车数据接入接口");
		HaLog.info(log, MsgIdConstant.MS_INF_0003, "道口过车数据接入接口",jsonObj.toString());
		try {
			// 执行业务处理
			if (null != jsonObj) {
				// 执行业务处理
				this.execute(jsonObj);
				HaLog.info(log, MsgIdConstant.MS_INF_0002, "道口过车数据接入接口");
				responseDto.setResultcode(0);
				responseDto.setResultmsg("OK");
			}
			return responseDto;
		} catch (Exception e) {
			HaLog.error(log, e, MsgIdConstant.MS_ERR_0001);
			responseDto.setResultcode(1);
			responseDto.setResultmsg(ReceiveErrorType.SYSTEM_ERROR.getMessage());
			return responseDto;
		}
	}

	/**
	 * 执行业务处理
	 *
	 * @param jsonObj
	 */
	private void execute(JSONObject jsonObj) {
		Meta meta = new Meta();
		meta.setEventType(crossingCarPass);
		meta.setRecvSequence(UuidUtil.getUuid32());
		server.handle(meta, jsonObj.toString());
	}

}
