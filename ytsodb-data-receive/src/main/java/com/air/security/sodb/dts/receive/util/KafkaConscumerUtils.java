package com.air.security.sodb.dts.receive.util;

import com.air.security.sodb.data.core.constant.GlobalCodeConstant;
import com.air.security.sodb.data.core.util.PropertyUtil;
import com.air.security.sodb.data.core.util.ResourceUtil;
import org.apache.kafka.clients.consumer.KafkaConsumer;

import java.util.Arrays;
import java.util.Properties;

/**
 * kafka消费者工具类
 *
 * <AUTHOR>
 */
public class KafkaConscumerUtils {

    /**
     * kafka配置文件
     */
    private static Properties properties = ResourceUtil.getInstance().getLocalProperties(
            PropertyUtil.getProperty(GlobalCodeConstant.KAFKA_CONSUMER_CONFIG_PATH_KEY), "kafkaConsumer");


    /**
     * 获取kafkaConsumer
     *
     * @param topic
     * @return
     */
    public static KafkaConsumer<String, String> getConsumer(String[] topic) {
        KafkaConsumer<String, String> consumer = new KafkaConsumer<>(properties);
        consumer.subscribe(Arrays.asList(topic));
        return consumer;
    }
}
