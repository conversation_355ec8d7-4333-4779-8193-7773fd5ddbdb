#\u7EBF\u7A0B\u6C60\u5927\u5C0F
threadpool.count=20
#kafkaConsumer kafkaæ¶è´¹èéç½®æä»¶è·¯å¾
#kafka.consumer.config.path=G:\\whjcDataRecv\\config\\kafkaConsumer.properties
kafka.consumer.config.path=/server/dtsServer/xjjcDataRecv/config/kafkaConsumer.properties
#kafkaProducer kafkaæ¶è´¹èéç½®æä»¶è·¯å¾
#kafka.producer.config.path=G:\\whjcDataRecv\\config\\kafkaProducer.properties
kafka.producer.config.path=/server/dtsServer/xjjcDataRecv/config/kafkaProducer.properties
#\u8BB0\u5F55\u6570\u636E\u4F20\u8F93\u65E5\u5FD7\u7684topic
kafka.msg.transfer.log.topic=msgTranLog
#\u5E94\u7528\u5173\u95ED\u76D1\u63A7\u7AEF\u53E3
ytsodb.stop.port=20028
#\u6D77\u5EB7\u670D\u52A1ip:port
rest.hk.host=***********:443
#\u6D77\u5EB7\u670D\u52A1appkey
rest.hk.appKey=27438127
#\u6D77\u5EB7\u670D\u52A1appsecret
rest.hk.appSecret=lujmsZbUXFp8OVF2Xe9N
#\u6D77\u5EB7\u624B\u52A8\u62A5\u8B66\u8D44\u6E90\u4FE1\u606Fcode
hk.handEquInfo.service.code=hkhandEquInfo
#\u6D77\u5EB7\u83B7\u53D6\u624B\u52A8\u62A5\u8B66\u8D44\u6E90\u4FE1\u606Furl
hk.handEquInfo.url=/api/irds/v1/deviceResource/resources
#\u6D77\u5EB7\u624B\u52A8\u62A5\u8B66\u8D44\u6E90\u53D8\u66F4\u4FE1\u606Fcode
hk.handEquState.service.code=hkhandEquState
#\u6D77\u5EB7\u83B7\u53D6\u624B\u52A8\u62A5\u8B66\u8D44\u6E90\u53D8\u66F4\u4FE1\u606Furl
hk.handEquState.url=/api/scpms/v1/defence/status
#\u6D77\u5EB7\u83B7\u53D6\u6309\u4E8B\u4EF6\u7C7B\u578B\u8BA2\u9605\u4E8B\u4EF6\u4FE1\u606Furl
hk.eventSubscription.url=/api/eventService/v1/eventSubscriptionByEventTypes
#\u6D77\u5EB7\u5165\u4FB5\u62A5\u8B66\u4E8B\u4EF6\uFF08\u624B\u52A8\u62A5\u8B66\u62A5\u8B66\uFF09code
hk.handAlarmMessage.service.code=handAlarmMessage
#é¨ç¦æ¥è­¦èµæºcode
hk.mjEquInfo.service.code=hkmjEquInfo
#é¨ç¦æ¥è­¦èµæºurl
hk.mjEquInfo.url=/api/irds/v1/deviceResource/resources
#é¨ç¦æ¥è­¦åæ´èµæºcode
hk.mjEquState.service.code=hkmjEquState
#é¨ç¦æ¥è­¦åæ´èµæºurl
hk.mjEquState.url=/api/acs/v1/door/states
#è·ååä¸ªå¡çä¿¡æ¯
hk.cardNoInfo.url=/api/irds/v1/card/cardInfo
#é¨ç¦æ¥è­¦code
hk.mjAlarmMessage.service.code=mjAlarmMessage
#éå£è¿è½¦code
crossingCarPass.service.code=crossingCarPass
#sodb\u624B\u52A8\u62A5\u8B66\u8D44\u6E90\u72B6\u6001\u8BF7\u6C42\u63A5\u53E3
sodb.ec03.equ.status.req.url=http://***********/api/bs/equ/info/open/getEquInfoByCode/EC03
#é¨ç¦æ¥è­¦èµæº
sodb.ec02.equ.status.req.url=http://***********/api/bs/equ/info/open/getEquInfoByCode/EC02
#sodb\u89C6\u9891\u8D44\u6E90\u72B6\u6001\u8BF7\u6C42\u63A5\u53E3
sodb.video.equ.status.req.url=http://***********/api/bs/equ/info/open/getEquInfoByCode/EC01
#éå£èµæº
sodb.cross.equ.status.req.url=http://***********/api/bs/equ/info/open/getEquInfoByCode/EC05

#ææ£æ°æ®æ¥å¥ä¸»é¢
psgr.service=true
kafka.psgr.msg.input.topics=relsTopic1
#è´§æ£åéæ¥å£
cargo.service=true
kafka.cargo.msg.output.topic=relsTopic2
#èªç­æ°æ®æ¥å¥ä¸»é¢
flight.service=true
kafka.flight.msg.input.topics=relsTopic3
#minioåæ°
endpoint=http://10.32.25.12:9000
access_key=miniominio
secret_key=hayc@123
bucket=datarecv

#æºåºä¸å­ç 
airport.iata=YTW
#éå£åæ´èµæºcode
crossEquState.service.code=crossEquState
#éå£åæ´èµæºurl
crossEquState.url=https://127.0.0.1:18899/api/status?
#ç¨æ·å
username=admin
#å¯ç 
password=admin