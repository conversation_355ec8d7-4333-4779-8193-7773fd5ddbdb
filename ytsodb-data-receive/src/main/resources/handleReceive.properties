#\u6D77\u5EB7\u624B\u52A8\u62A5\u8B66\u8D44\u6E90\u4FE1\u606F
hkhandEquInfo=MessageRecvFormatServiceImpl,cContainerEqu,HK_SD_EQU,EQU
hkhandEquState=MessageRecvFormatServiceImpl,cContainerEqu,HK_SD_EQU_STATUS_UE,EQU_STATUS
#\u6D77\u5EB7\u624B\u52A8\u62A5\u8B66\u8D44\u6E90\u4FE1\u606F
hkmjEquInfo=MessageRecvFormatServiceImpl,cContainerEqu,HK_MJ_EQU,EQU
#\u6D77\u5EB7\u624B\u52A8\u62A5\u8B66\u8D44\u6E90\u53D8\u66F4\u4FE1\u606F
hkmjEquState=MessageRecvFormatServiceImpl,cContainerEqu,HK_MJ_EQU_STATUS_UE,EQU_STATUS
#\u6D77\u5EB7\u5165\u4FB5\u62A5\u8B66\u4E8B\u4EF6\uFF08\u624B\u52A8\u62A5\u8B66\u62A5\u8B66\uFF09
handAlarmMessage=MessageRecvFormatServiceImpl,cContainerAlarm,HK_SD_ALARM,ALARM
#é¨ç¦æ¥è­¦åé¨ç¦å·å¡
mjAlarmMessage=MessageRecvFormatServiceImpl,cContainerAlarm|cContainerRecord,HK_MJ_ALARM|HK_MJ_RECORD_CARD,ALARM|RECORD_CARD
#ææ£æ°æ®
check=ScimsImgMessageRecvFormatServiceImpl,cContainerPassenger,PRSCJSON_SECURITY,PSGR_DATA
bag=ScimsImgMessageRecvFormatServiceImpl,cContainerPassenger,PSGR_OPEN_INFO,PSGR_DATA
flight=MessageRecvFormatServiceImpl,cContainerFlight,FLIGHT_DYN,FLIGHT_DATA
#è´§è¿å®æ£ç³»ç»-è¿ååºæ¬ä¿¡æ¯
WAY_BILL_LOG=MessageRecvFormatServiceImpl,cContainerCargo,WAY_BILL_LOG,CARGO
#è´§è¿å®æ£ç³»ç»-è¿åå¼åä¿¡æ¯
OPEN_LOG=CargoOpenMessageRecvFormatServiceImpl,cContainerCargo,OPEN_LOG,CARGO
#è´§è¿å®æ£ç³»ç»-è¿åå®æ£ä¿¡æ¯
SECURITY_LOG=MessageRecvFormatServiceImpl,cContainerCargo,SECURITY_LOG,CARGO
#è´§è¿å®æ£ç³»ç»-å®æ£ééçå¼æ¾ä¿¡æ¯
CHECKSTATUS_EQU=MessageRecvFormatServiceImpl,cContainerCargo,CHECKSTATUS_EQU,CARGO
#éå£è¿è½¦æ°æ®
crossingCarPass=MessageRecvFormatServiceImpl,cContainerCar,CROSSING_CAR_PASS_INFO,CAR_DATA
#éå£è®¾å¤åæ´
crossEquState=MessageRecvFormatServiceImpl,cContainerEqu,CROSSING_EQU_STATUS_UE,EQU_STATUS
