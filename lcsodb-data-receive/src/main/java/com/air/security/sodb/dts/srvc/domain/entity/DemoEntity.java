package com.air.security.sodb.dts.srvc.domain.entity;

import java.util.Date;

import javax.persistence.Column;
import javax.persistence.Id;
import javax.persistence.Table;

import com.air.security.sodb.data.core.base.BaseEntity;

/**
 * demo Entity
 *
 * <AUTHOR>
 */
@Table(name = "DTS_DEMO")
public class DemoEntity extends BaseEntity {
    @Id
    @Column(name = "UUID")
    private String uuid;
    @Column(name = "NAME")
    private String name;
    @Column(name = "PASSWORD")
    private String password;
    @Column(name = "BIRTH")
    private Date birth;
    @Column(name = "GENDER")
    private String gender;
    @Column(name = "NICK_NAME")
    private String nickName;
    @Column(name = "CREATE_TIME")
    private Date createTime;
    @Column(name = "CREATE_ID")
    private String createId;
    @Column(name = "IS_DELETE")
    private String isDelete;
    @Column(name = "DELETE_TIME")
    private Date deleteTime;

    public String getUuid() {
        return uuid;
    }

    public void setUuid(String uuid) {
        this.uuid = uuid;
    }

    public String getName() {
        return name;
    }

    public void setName(String name) {
        this.name = name;
    }

    public String getPassword() {
        return password;
    }

    public void setPassword(String password) {
        this.password = password;
    }

    public Date getBirth() {
        return birth;
    }

    public void setBirth(Date birth) {
        this.birth = birth;
    }

    public String getGender() {
        return gender;
    }

    public void setGender(String gender) {
        this.gender = gender;
    }

    public String getNickName() {
        return nickName;
    }

    public void setNickName(String nickName) {
        this.nickName = nickName;
    }

    public Date getCreateTime() {
        return createTime;
    }

    public void setCreateTime(Date createTime) {
        this.createTime = createTime;
    }

    public String getCreateId() {
        return createId;
    }

    public void setCreateId(String createId) {
        this.createId = createId;
    }

    public String getIsDelete() {
        return isDelete;
    }

    public void setIsDelete(String isDelete) {
        this.isDelete = isDelete;
    }

    public Date getDeleteTime() {
        return deleteTime;
    }

    public void setDeleteTime(Date deleteTime) {
        this.deleteTime = deleteTime;
    }

}
