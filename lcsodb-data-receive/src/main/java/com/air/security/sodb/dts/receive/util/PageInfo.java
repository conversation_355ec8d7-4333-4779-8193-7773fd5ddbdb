package com.air.security.sodb.dts.receive.util;

/**
 * <AUTHOR>
 */
public class PageInfo {

    private int currentPage;
    private int pageSize;
    private int totalPage;
    private int totalRows;

    public int getCurrentPage() {
        return currentPage;
    }

    public void setCurrentPage(int currentPage) {
        this.currentPage = currentPage;
    }

    public int getPageSize() {
        return pageSize;
    }

    public void setPageSize(int pageSize) {
        this.pageSize = pageSize;
    }

    public int getTotalPage() {
        return totalPage;
    }

    public void setTotalPage(int totalPage) {
        this.totalPage = totalPage;
    }

    public int getTotalRows() {
        return totalRows;
    }

    public void setTotalRows(int totalRows) {
        this.totalRows = totalRows;
    }

    /**
     * 判断是否存在下一页
     *
     * @return
     */
    public boolean hasNextPage() {
        return this.currentPage != -1 && this.currentPage < this.totalPage;
    }

}
