package com.air.security.sodb.dts.receive.listener;

import com.air.security.sodb.data.core.base.Meta;
import com.air.security.sodb.data.core.constant.MsgIdConstant;
import com.air.security.sodb.data.core.util.HaLog;
import com.air.security.sodb.data.core.util.PropertyUtil;
import com.air.security.sodb.dts.receive.server.MqMessageRecvServer;
import com.air.security.sodb.dts.receive.server.MqMessageRecvServerImpl;
import org.apache.activemq.ActiveMQConnectionFactory;
import org.slf4j.Logger;
import org.slf4j.LoggerFactory;

import javax.jms.*;

/**
 * 获取航班数据 监听类
 * <AUTHOR>
public class FlightMessageActiveMqListener implements MessageListener {
    private static final Logger log = LoggerFactory.getLogger(FlightMessageActiveMqListener.class);

    private static String mqUrl = PropertyUtil.getProperty("flight.active.mq.url");
    private static String mqUsername = PropertyUtil.getProperty("flight.active.mq.username");
    private static String mqPwd = PropertyUtil.getProperty("flight.active.mq.pwd");
    private static String flightTopic = PropertyUtil.getProperty("flight.active.mq.ajTopic");

    private static MqMessageRecvServer server = new MqMessageRecvServerImpl();

    public void receiveData(FlightMessageActiveMqListener lkajActiveMqListener) {
        try {
            //初始化连接工厂
            ActiveMQConnectionFactory connectionFactory = new ActiveMQConnectionFactory(mqUsername, mqPwd, mqUrl);
            connectionFactory.setTrustAllPackages(true);
            //建立连接
            Connection conn = connectionFactory.createConnection();
            //启动连接
            conn.start();
            //创建Session，此方法第一个参数表示会话是否在事务中执行，第二个参数设定会话的应答模式
            Session session = conn.createSession(false, Session.AUTO_ACKNOWLEDGE);
            //创建目标队列
            Queue queue = session.createQueue(flightTopic);
            //通过session创建消息的接收者
            MessageConsumer consumer = session.createConsumer(queue);
            HaLog.info(log, MsgIdConstant.MS_INF_0001, "航班数据监听");

            //给接收者添加监听对象
            consumer.setMessageListener(lkajActiveMqListener);

        } catch (JMSException e) {
            HaLog.error(log, e, MsgIdConstant.MS_ERR_0001);
        }
    }

    @Override
    public void onMessage(Message arg0) {
        HaLog.info(log, MsgIdConstant.MS_INF_0001, "航班数据监听");

        TextMessage textMsg = (TextMessage) arg0;
        try {
            String text = textMsg.getText();
            Meta meta = new Meta();
            meta.setEventType("flightInfo");
            server.handle(meta, text);
        } catch (Exception e) {
            HaLog.error(log, e, MsgIdConstant.MS_ERR_0001);
        }
    }
}
