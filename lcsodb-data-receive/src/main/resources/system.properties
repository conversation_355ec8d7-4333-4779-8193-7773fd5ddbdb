#çº¿ç¨æ± å¤§å°
threadpool.count=20
#kafkaConsumer kafkaæ¶è´¹èéç½®æä»¶è·¯å¾
#kafka.consumer.config.path=D:\\dts\\config\\kafkaConsumer.properties
kafka.consumer.config.path=/server/dtsServer/qzDataRecv/config/kafkaConsumer.properties
#kafkaProducer kafkaæ¶è´¹èéç½®æä»¶è·¯å¾
#kafka.producer.config.path=D:\\dts\\config\\kafkaProducer.properties
kafka.producer.config.path=/server/dtsServer/qzDataRecv/config/kafkaProducer.properties
#è®°å½æ°æ®ä¼ è¾æ¥å¿çtopic
kafka.msg.transfer.log.topic=msgTranLog
qzsodb.stop.port=20016
#æµ·åº·æå¡ip:port
rest.hk.host=*********:443
#æµ·åº·æå¡appkey
rest.hk.appKey=25454562
#æµ·åº·æå¡appsecret
rest.hk.appSecret=S7n8NL2Rkju3U5d5bbZm
#æµ·åº·è§é¢ç®å½æ code
hk.videoRegion.service.code=hkVideoRegion
#æµ·åº·è·åè§é¢ç®å½æ url
hk.videoRegion.url=/api/resource/v1/regions
#æµ·åº·è§é¢èµæºä¿¡æ¯code
hk.videoEquInfo.service.code=hkvideoEquInfo
#æµ·åº·è·åè§é¢èµæºä¿¡æ¯url
hk.videoEquInfo.url=/api/resource/v1/cameras
#æµ·åº·è§é¢èµæºåæ´ä¿¡æ¯code
hk.videoEquState.service.code=hkvideoEquState
#æµ·åº·è·åè§é¢èµæºåæ´ä¿¡æ¯url
hk.videoEquState.url=/api/nms/v1/online/camera/get
flight.service=true
#èªç­ä¿¡æ¯ç³»ç»activemqéç½®
flight.active.mq.url=failover://tcp://*************:8089
flight.active.mq.username=adminhx
flight.active.mq.pwd=hx_456yhgfDSAQ!@#
flight.active.mq.ajTopic=Aofis.Security