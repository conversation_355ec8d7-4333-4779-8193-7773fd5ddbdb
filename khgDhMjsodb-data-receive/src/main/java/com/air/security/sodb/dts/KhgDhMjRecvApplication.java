package com.air.security.sodb.dts;

import com.air.security.sodb.data.core.constant.MsgIdConstant;
import com.air.security.sodb.data.core.quartz.SchedulerManager;
import com.air.security.sodb.data.core.util.HaLog;
import org.slf4j.Logger;
import org.slf4j.LoggerFactory;
import org.springframework.boot.ApplicationArguments;
import org.springframework.boot.ApplicationRunner;
import org.springframework.boot.SpringApplication;
import org.springframework.boot.autoconfigure.SpringBootApplication;
import org.springframework.context.annotation.ImportResource;

/**
 * <AUTHOR>
 * @date 2019/12/26 14:34
 * @description 新疆巴音机场数据接入服务
 */
@ImportResource("classpath:/applicationContext.xml")
@SpringBootApplication(scanBasePackages = "com.air.security.sodb")
public class KhgDhMjRecvApplication implements ApplicationRunner {

    private static final Logger log = LoggerFactory.getLogger(KhgDhMjRecvApplication.class);

    public static void main(String[] args) {

        SpringApplication.run(KhgDhMjRecvApplication.class, args);
    }

    /**
     * 启动服务
     *
     * @param args
     * @throws Exception
     */
    @Override
    public void run(ApplicationArguments args) throws Exception {
        // 启动定时任务
        runQuartzJob();
    }

    /**
     * 启动定时任务
     */
    private static void runQuartzJob() {
        HaLog.info(log, MsgIdConstant.MS_INF_0001, "quartz定时任务");
        SchedulerManager manager = new SchedulerManager();
        manager.runJob("quartzJob.xml");
        HaLog.info(log, MsgIdConstant.MS_INF_0002, "quartz定时任务");
    }
}
