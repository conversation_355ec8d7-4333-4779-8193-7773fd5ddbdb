package com.air.security.sodb.dts.receive.server;

import org.apache.commons.lang.StringUtils;
import org.slf4j.Logger;
import org.slf4j.LoggerFactory;

import com.air.security.sodb.data.core.base.Meta;
import com.air.security.sodb.data.core.constant.GlobalCodeConstant;
import com.air.security.sodb.data.core.constant.MsgIdConstant;
import com.air.security.sodb.data.core.exception.SystemBaseException;
import com.air.security.sodb.data.core.spring.ApplicationContextUtil;
import com.air.security.sodb.data.core.util.DataTransferLogRecordUtil;
import com.air.security.sodb.data.core.util.DateTimeUtil;
import com.air.security.sodb.data.core.util.EditMetaUtil;
import com.air.security.sodb.data.core.util.HaLog;
import com.air.security.sodb.data.core.util.KafkaProducerUtil;
import com.air.security.sodb.data.core.util.PropertyUtil;
import com.air.security.sodb.data.core.util.UuidUtil;
import com.air.security.sodb.dts.receive.service.MessageRecvService;
import com.alibaba.fastjson.JSONArray;
import com.alibaba.fastjson.JSONObject;

/**
 * @Description: 消息数据的分发处理类
 * @author: zhangc
 * @Date: 2018年12月5日
 */
public class MqMessageRecvServerImpl implements MqMessageRecvServer {

    private static final Logger log = LoggerFactory.getLogger(MqMessageRecvServerImpl.class);

    /**
     * 消息分发处理
     */
    @Override
    public void handle(Meta meta, String messageBody) {
        HaLog.info(log, MsgIdConstant.MS_INF_0001, meta.getEventType());

        HaLog.infoJson(log, JSONObject.toJSONString(meta));
        HaLog.infoJson(log, messageBody);

        String[] typeProp = null;
        try {

            String eventType = PropertyUtil.getProperty(meta.getEventType());

            if (StringUtils.isBlank(eventType)) {
                HaLog.warn(log, MsgIdConstant.MS_WAR_0002, meta.getEventType() + "消息类型配置文件的配置");
                return;
            }

            typeProp = eventType.split(",");
            int len = 4;
            if (typeProp == null || typeProp.length < len) {
                HaLog.warn(log, MsgIdConstant.MS_WAR_0003, "消息类型配置文件的配置");
                return;
            }

            MessageRecvService service = (MessageRecvService) ApplicationContextUtil.getBean(typeProp[0]);
            // MessageRecvService service = new MessageRecvFormatServiceImpl();

            meta.setSequence(UuidUtil.getUuid32());

            // 编辑消息头
            String[] topics = typeProp[1].split("\\|");
            String[] eventTypes = typeProp[2].split("\\|");
            String[] msgTypes = typeProp[3].split("\\|");
            for (int i = 0, l = topics.length; i < l; i++) {

                EditMetaUtil.editMeta(eventTypes[i], msgTypes[i], DateTimeUtil.getCurrentTimestampStr("yyyyMMddHHmmss"),
                        meta);

                // 记录数据交互日志
                DataTransferLogRecordUtil.record(meta, messageBody, GlobalCodeConstant.LOG_TYPE_FRONT_RECV,
                        GlobalCodeConstant.LOG_SERVICE_TYPE_RECV);

                service.execute(meta, messageBody);

                // 发送消息到对应的kafka主题
                JSONObject sendKafkaMsg = service.pollSendMessage();

                Object body = sendKafkaMsg.get("body");
                if (body instanceof JSONArray) {
                    JSONArray bodyArray = (JSONArray) body;
                    for (Object object : bodyArray) {
                        meta.setSequence(UuidUtil.getUuid32());
                        sendKafkaMsg.put("body", object);
                        sendKafkaMsg.put("meta", meta);
                        KafkaProducerUtil.send(topics[i], JSONObject.toJSONString(sendKafkaMsg));
                    }
                } else {
                    KafkaProducerUtil.send(topics[i], JSONObject.toJSONString(sendKafkaMsg));

                }

                Thread.sleep(100L);
            }

            Thread.sleep(10L);

        } catch (SystemBaseException e1) {
            HaLog.error(log, MsgIdConstant.MS_ERR_0001, e1, typeProp.toString());
            throw e1;
        } catch (Exception e2) {
            HaLog.error(log, MsgIdConstant.MS_ERR_0001, e2, typeProp.toString());
            throw new SystemBaseException(e2);
        } finally {
            HaLog.info(log, MsgIdConstant.MS_INF_0002, meta.getEventType());
        }
    }

}
