#\u7EBF\u7A0B\u6C60\u5927\u5C0F
threadpool.count=20
rest.dh.pageSize=200
#kafkaConsumer kafka\u6D88\u8D39\u8005\u914D\u7F6E\u6587\u4EF6\u8DEF\u5F84
#kafka.consumer.config.path=D:\\config\\kafkaConsumer.properties
kafka.consumer.config.path=/server/dtsServer/xjjcDataRecv/config/kafkaConsumer.properties
#kafkaProducer kafka\u6D88\u8D39\u8005\u914D\u7F6E\u6587\u4EF6\u8DEF\u5F84
#kafka.producer.config.path=D:\\config\\kafkaProducer.properties
kafka.producer.config.path=/server/dtsServer/xjjcDataRecv/config/kafkaProducer.properties
#\u8BB0\u5F55\u6570\u636E\u4F20\u8F93\u65E5\u5FD7\u7684topic
kafka.msg.transfer.log.topic=msgTranLog
zssodb.stop.port=20032

#(\u95E8\u7981\uFF09\u95E8\u7981\u62A5\u8B66\u7C7B\u578B
doorAlarmEventType=43,46,52,53,1430,1420,1462
doorAlarmEventTypes=52,46,1420,42,43,45,51,48,49,53,56,57,1430,1461,1462
#\uFF08\u624B\u52A8\u62A5\u8B66\uFF09\u624B\u52A8\u62A5\u8B66\u7C7B\u578B
handAlarmEventTypes=81

dHMjalarm={\"param\":{\"unitType\":7, \"category\":8}}
dHHandalarm={\"param\":{\"unitType\":3, \"category\":3}}
# host
icc.sdk.host=***********
icc.sdk.username=system
icc.sdk.password=Admin123
url=http://***********:8314
