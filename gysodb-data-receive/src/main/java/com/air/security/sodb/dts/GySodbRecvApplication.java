package com.air.security.sodb.dts;

import com.air.security.sodb.data.core.constant.MsgIdConstant;
import com.air.security.sodb.data.core.quartz.SchedulerManager;
import com.air.security.sodb.data.core.util.HaLog;
import com.air.security.sodb.data.core.util.PropertyUtil;
import com.air.security.sodb.dts.receive.listener.MqFlightListener;
import com.air.security.sodb.dts.receive.listener.mqtt.MqttReceiveListener;
import com.caacitc.rabbitmq.client.EsbClient;
import org.slf4j.Logger;
import org.slf4j.LoggerFactory;
import org.springframework.beans.factory.annotation.Value;
import org.springframework.boot.ApplicationArguments;
import org.springframework.boot.ApplicationRunner;
import org.springframework.boot.SpringApplication;
import org.springframework.boot.autoconfigure.SpringBootApplication;
import org.springframework.context.annotation.ImportResource;

/**
 * <AUTHOR> @Description 贵阳sodb数据接入服务
 */
@ImportResource("classpath:/applicationContext.xml")
@SpringBootApplication(scanBasePackages = "com.air.security.sodb")
public class GySodbRecvApplication implements ApplicationRunner {

    private static final Logger log = LoggerFactory.getLogger(GySodbRecvApplication.class);

    private static boolean flightService = Boolean.parseBoolean(PropertyUtil.getProperty("flight.service"));

    @Value("${esb.accept.topic}")
    private String acceptTopic;

    @Value("${mqtt.service}")
    private boolean mqttService;

    public static void main(String[] args) {
        SpringApplication.run(GySodbRecvApplication.class, args);
    }

    /**
     * 初始化
     *
     * @param args
     * @throws Exception
     */
    @Override
    public void run(ApplicationArguments args) throws Exception {
        // 启动定时任务
        runQuartzJob();
        if (flightService) {
            MqFlightListener mqFlightListener = new MqFlightListener(acceptTopic, true);
            EsbClient.getInstance.consumer(mqFlightListener);
        }

        if (mqttService) {
            MqttReceiveListener amqpMessageListener = new MqttReceiveListener();
            amqpMessageListener.start();
        }

    }

    /**
     * 启动定时任务
     */
    private static void runQuartzJob() {
        HaLog.info(log, MsgIdConstant.MS_INF_0001, "quartz定时任务");
        SchedulerManager manager = new SchedulerManager();
        manager.runJob("quartzJob.xml");
        HaLog.info(log, MsgIdConstant.MS_INF_0002, "quartz定时任务");
    }

}
