package com.air.security.sodb.dts.receive.listener.mqtt;

import com.air.security.sodb.data.core.util.PropertyUtil;
import org.eclipse.paho.client.mqttv3.*;
import org.eclipse.paho.client.mqttv3.persist.MemoryPersistence;

public class MqttRecListener {

    public static final String HOST = PropertyUtil.getProperty("sip.rabbit.mq.url");

    private static final String clientid = "ACS";
    private MqttClient client;
    private MqttConnectOptions options;
    private String userName = PropertyUtil.getProperty("sip.rabbit.mq.username");
    private String passWord = PropertyUtil.getProperty("sip.rabbit.mq.password");

    // 连接服务器
    public  boolean connectRabbitMq(String message,String sendTopic) {
        try {

            // host为主机名，clientid即连接MQTT的客户端ID，一般以唯一标识符表示，MemoryPersistence设置clientid的保存形式，默认为以内存保存
            client = new MqttClient(HOST, clientid, new MemoryPersistence());

            // MQTT的连接设置
            options = new MqttConnectOptions();

            // 设置是否清空session,这里如果设置为false表示服务器会保留客户端的连接记录，这里设置为true表示每次连接到服务器都以新的身份连接
            options.setCleanSession(false);

            // 设置连接的用户名
            options.setUserName(userName);

            // 设置连接的密码
            options.setPassword(passWord.toCharArray());

            // 设置超时时间 单位为秒
            options.setConnectionTimeout(10);

            // 设置会话心跳时间 单位为秒 服务器会每隔1.5*20秒的时间向客户端发送个消息判断客户端是否在线，但这个方法并没有重连的机制
            options.setKeepAliveInterval(20);

			//设置自动断线重连
			options.setAutomaticReconnect(true);

            // 设置回调s
            client.setCallback(new PushCallback());

            client.connect(options);

            MqttTopic topic = client.getTopic(sendTopic);

            MqttMessage message1 = new MqttMessage();
            message1.setQos(1);
            message1.setRetained(true);
            message1.setPayload(message.getBytes());

            MqttDeliveryToken token = topic.publish(message1);
            token.waitForCompletion();
            boolean retained = message1.isRetained();
            return retained;
        } catch (Exception e) {
            e.printStackTrace();
            return false;
        }
    }

}
