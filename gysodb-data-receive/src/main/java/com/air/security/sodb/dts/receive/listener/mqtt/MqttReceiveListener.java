package com.air.security.sodb.dts.receive.listener.mqtt;


import com.air.security.sodb.data.core.constant.MsgIdConstant;
import com.air.security.sodb.data.core.util.HaLog;
import com.air.security.sodb.data.core.util.PropertyUtil;
import org.eclipse.paho.client.mqttv3.MqttClient;
import org.eclipse.paho.client.mqttv3.MqttConnectOptions;
import org.eclipse.paho.client.mqttv3.persist.MemoryPersistence;
import org.slf4j.Logger;
import org.slf4j.LoggerFactory;

import java.util.ArrayList;
import java.util.List;

/**
 * sip Amqp数据监听
 *
 * <AUTHOR>
public class MqttReceiveListener {

	private static final Logger log = LoggerFactory.getLogger(MqttReceiveListener.class);
	public static final String HOST = PropertyUtil.getProperty("sip.rabbit.mq.url");
	public static final String ACCEPT_TOPIC = PropertyUtil.getProperty("sip.rabbit.mq.accept.topic");
	private static final String clientid = "T2anbao";
	private MqttClient client;
	private MqttConnectOptions options;
	private String userName = PropertyUtil.getProperty("sip.rabbit.mq.username");
	private String passWord = PropertyUtil.getProperty("sip.rabbit.mq.password");


	// 连接服务器
	public void start() {
		HaLog.info(log, MsgIdConstant.MS_INF_0001, "mqtt事件消息监听");
		try {

			// host为主机名，clientid即连接MQTT的客户端ID，一般以唯一标识符表示，MemoryPersistence设置clientid的保存形式，默认为以内存保存
			client = new MqttClient(HOST, clientid, new MemoryPersistence());

			// MQTT的连接设置
			options = new MqttConnectOptions();

			// 设置是否清空session,这里如果设置为false表示服务器会保留客户端的连接记录，这里设置为true表示每次连接到服务器都以新的身份连接
			options.setCleanSession(false);

			// 设置连接的用户名
			options.setUserName(userName);

			// 设置连接的密码
			options.setPassword(passWord.toCharArray());

			// 设置超时时间 单位为秒
			options.setConnectionTimeout(10);

			// 设置会话心跳时间 单位为秒 服务器会每隔1.5*20秒的时间向客户端发送个消息判断客户端是否在线，但这个方法并没有重连的机制
			options.setKeepAliveInterval(20);

			//设置自动断线重连
			options.setAutomaticReconnect(true);

			// 设置回调
			client.setCallback(new PushCallback());

			//MqttTopic topic = client.getTopic(TOPIC);
			//setWill方法，如果项目中需要知道客户端是否掉线可以调用该方法。设置最终端口的通知消息
			// options.setWill(topic, "close".getBytes(), 2, true);

			client.connect(options);

			//订阅消息
			// QoS0 代表，Sender 发送的一条消息，Receiver 最多能收到一次，也就是说 Sender 尽力向 Receiver 发送消息，如果发送失败，也就算了；
			// QoS1 代表，Sender 发送的一条消息，Receiver 至少能收到一次，也就是说 Sender 向 Receiver 发送消息，如果发送失败，会继续重试，直到 Receiver
			// 收到消息为止，但是因为重传的原因，Receiver 有可能会收到重复的消息；
			// QoS2 代表，Sender 发送的一条消息，Receiver 确保能收到而且只收到一次，也就是说 Sender 尽力向 Receiver 发送消息，如果发送失败，会继续重试，直到 Receiver
			// 收到消息为止，同时保证 Receiver 不会因为消息重传而收到重复的消息。

			List<Integer> integers = new ArrayList<>();
			String[] topic1 = ACCEPT_TOPIC.trim().split(",");
			int[] Qos = new int[topic1.length];
			for (int i = 0; i < topic1.length; i++) {
				Qos[i] = 1;
			}
			HaLog.info(log, MsgIdConstant.MS_INF_0002, "主题数》》》" + topic1.length + "   qos数量》》》+" + Qos.length);
			client.subscribe(topic1, Qos);
			HaLog.info(log, MsgIdConstant.MS_INF_0002, "mqtt事件消息监听");
		} catch (Exception e) {
			e.printStackTrace();
		}
	}

}
