package com.air.security.sodb.dts.srvc.restsrvc.platform.mqtt.dto;

import io.swagger.annotations.ApiModelProperty;

/**
 * 配置mqtt传递参数dto
 */
public class MqttDto {

    /**
     * 数据请求类型(1:请求所有设备数据
     * 2:请求单条设备数据，需要传递device_code参数
     * 3:请求对应设备类型数据，需要传递device_type参数)
     */
    @ApiModelProperty(value = "数据请求类型")
    private String requestType;

    /**
     * 设备编号（请求单条设备数据，时传递）
     */
    @ApiModelProperty(value = "设备编号")
    private String deviceCode;

    /**
     * 设备类型（请求对应设备类型数据时传递）
     */
    @ApiModelProperty(value = "设备类型")
    private String deviceType;

    /**
     * 请求编码类型
     */
    @ApiModelProperty(value = "请求编码类型")
    private String serviceCode;

    /**
     * 发送主题
     */
    @ApiModelProperty(value = "发送主题")
    private String sendTopic;

    /**
     * 接受者
     */
    @ApiModelProperty(value = "接受者")
    private String receiverSys;
    /**
     * 接受者
     */
    @ApiModelProperty(value = "接受者")
    private String receiverPlatform;
    /**
     * 发送者
     */
    @ApiModelProperty(value = "发送者")
    private String senderPlatform;
    /**
     * 发送者
     */
    @ApiModelProperty(value = "发送者")
    private String senderSys;

    public String getReceiverPlatform() {
        return receiverPlatform;
    }

    public void setReceiverPlatform(String receiverPlatform) {
        this.receiverPlatform = receiverPlatform;
    }

    public String getSenderPlatform() {
        return senderPlatform;
    }

    public void setSenderPlatform(String senderPlatform) {
        this.senderPlatform = senderPlatform;
    }

    public String getSenderSys() {
        return senderSys;
    }

    public void setSenderSys(String senderSys) {
        this.senderSys = senderSys;
    }

    public String getReceiverSys() {
        return receiverSys;
    }

    public void setReceiverSys(String receiverSys) {
        this.receiverSys = receiverSys;
    }

    public String getSendTopic() {
        return sendTopic;
    }

    public void setSendTopic(String sendTopic) {
        this.sendTopic = sendTopic;
    }

    public String getRequestType() {
        return requestType;
    }

    public void setRequestType(String requestType) {
        this.requestType = requestType;
    }

    public String getDeviceCode() {
        return deviceCode;
    }

    public void setDeviceCode(String deviceCode) {
        this.deviceCode = deviceCode;
    }

    public String getDeviceType() {
        return deviceType;
    }

    public void setDeviceType(String deviceType) {
        this.deviceType = deviceType;
    }

    public String getServiceCode() {
        return serviceCode;
    }

    public void setServiceCode(String serviceCode) {
        this.serviceCode = serviceCode;
    }
}
