package com.air.security.sodb.dts.receive.listener;

import com.air.security.sodb.data.core.base.Meta;
import com.air.security.sodb.data.core.constant.MsgIdConstant;
import com.air.security.sodb.data.core.util.HaLog;
import com.air.security.sodb.data.core.util.UuidUtil;
import com.air.security.sodb.dts.receive.server.MqMessageRecvServer;
import com.air.security.sodb.dts.receive.server.MqMessageRecvServerImpl;
import com.caacitc.rabbitmq.listener.AbstractMessageListener;
import org.json.JSONObject;
import org.json.XML;
import org.slf4j.Logger;
import org.slf4j.LoggerFactory;

import java.io.UnsupportedEncodingException;

/**
 * @function:
 * @create: 2020/01/08
 * @author: ZhangDong
 * @since: jdk1.8.0_92
 */
public class MqFlightListener extends AbstractMessageListener {

    private static final Logger log = LoggerFactory.getLogger(MqFlightListener.class);

    /**
     * 消息分发处理器
     */
    private static MqMessageRecvServer server = new MqMessageRecvServerImpl();

    public MqFlightListener(String serviceType, boolean autoAck) {
        super(serviceType, autoAck);
    }

    /**
     * 仅当autoAck 为 false时，handleMessage的返回结果false 和 true有意义
     * autoAck为true时，无论 handleMessage返回任何结果，消息总会被消息，并不会挂起
     * @param message
     * @return
     * @throws UnsupportedEncodingException
     */
    @Override
    public Boolean handleMessage(String message)  {
        HaLog.info(log, MsgIdConstant.MS_INF_0001, "航班货检事件消息监听");
        try{
            // 接收消息日志
            HaLog.infoJson(log, message);
            JSONObject jsonObject = XML.toJSONObject(message);
            String eventype = jsonObject.getJSONObject("MSG").getJSONObject("META").getString("STYP");
            Meta meta = new Meta();
            meta.setEventType(eventype);
            meta.setRecvSequence(UuidUtil.getUuid32());
            server.handle(meta, jsonObject.toString());
            HaLog.info(log, MsgIdConstant.MS_INF_0002, "航班货检事件消息监听");
            return true;
        }catch(Exception e){
            e.printStackTrace();
            HaLog.error(log, MsgIdConstant.MS_ERR_0001, e, "消息处理异常");
            return false;
        }
    }
}
