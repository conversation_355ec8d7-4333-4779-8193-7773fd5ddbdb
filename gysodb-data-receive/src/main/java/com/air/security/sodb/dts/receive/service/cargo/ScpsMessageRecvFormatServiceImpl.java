package com.air.security.sodb.dts.receive.service.cargo;

import com.air.security.sodb.data.core.base.Meta;
import com.air.security.sodb.data.core.constant.MsgIdConstant;
import com.air.security.sodb.data.core.exception.SystemBaseException;
import com.air.security.sodb.data.core.util.DateTimeUtil;
import com.air.security.sodb.data.core.util.HaLog;
import com.air.security.sodb.dts.receive.service.AbstractMessageRecvService;
import com.alibaba.fastjson.JSONArray;
import com.alibaba.fastjson.JSONObject;
import org.slf4j.Logger;
import org.slf4j.LoggerFactory;
import org.springframework.stereotype.Service;

/**
 * <AUTHOR>
 * @Description: 安检通道信息
 **/
@Service("ScpsMessageRecvFormatServiceImpl")
public class ScpsMessageRecvFormatServiceImpl extends AbstractMessageRecvService {
    private static final Logger log = LoggerFactory.getLogger(ScpsMessageRecvFormatServiceImpl.class);

    @Override
    public void execute(Meta meta, String messageBody) throws SystemBaseException {
        HaLog.info(log, MsgIdConstant.MS_INF_0001, meta.getEventType());

        // 响应json消息
        JSONObject outputMsg = new JSONObject();

        JSONObject json = JSONObject.parseObject(messageBody);
        Object o = json.getJSONObject("MSG").getJSONObject("DFLT").getJSONObject("Items").get("Item");
        if (o instanceof JSONArray) {
            JSONArray inputJsonArr = (JSONArray) o;
            for (Object object : inputJsonArr) {
                JSONObject childInputJson = (JSONObject) object;
                childInputJson.put("operateTime", DateTimeUtil.getCurrentTimestampStr("yyyy-MM-dd HH:mm:ss"));
            }
            // 响应消息体
            outputMsg.put("body", inputJsonArr);
        } else if (o instanceof JSONObject) {
            JSONObject childInputJson = (JSONObject) o;
            childInputJson.put("operateTime", DateTimeUtil.getCurrentTimestampStr("yyyy-MM-dd HH:mm:ss"));
            // 响应消息体
            outputMsg.put("body", childInputJson);
        }

        // 响应消息头
        outputMsg.put("meta", meta);

        // 转换为JSON格式，并发送消息到指定主题
        super.putSendMessage(outputMsg);

        HaLog.info(log, MsgIdConstant.MS_INF_0002, meta.getEventType());
    }
}
