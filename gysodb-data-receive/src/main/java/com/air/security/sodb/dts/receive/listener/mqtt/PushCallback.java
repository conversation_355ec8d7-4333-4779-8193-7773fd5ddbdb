package com.air.security.sodb.dts.receive.listener.mqtt;

import com.air.security.sodb.data.core.base.Meta;
import com.air.security.sodb.data.core.constant.MsgIdConstant;
import com.air.security.sodb.data.core.util.HaLog;
import com.air.security.sodb.data.core.util.UuidUtil;
import com.air.security.sodb.dts.receive.server.MqMessageRecvServer;
import com.air.security.sodb.dts.receive.server.MqMessageRecvServerImpl;
import com.alibaba.fastjson.JSONObject;
import org.eclipse.paho.client.mqttv3.IMqttDeliveryToken;
import org.eclipse.paho.client.mqttv3.MqttCallback;
import org.eclipse.paho.client.mqttv3.MqttMessage;
import org.slf4j.Logger;
import org.slf4j.LoggerFactory;
import org.springframework.beans.factory.annotation.Autowired;

/**
 * 发布消息的回调类
 * <p>
 * 必须实现MqttCallback的接口并实现对应的相关接口方法CallBack 类将实现 MqttCallBack。
 * 每个客户机标识都需要一个回调实例。在此示例中，构造函数传递客户机标识以另存为实例数据。
 * 在回调中，将它用来标识已经启动了该回调的哪个实例。
 * 必须在回调类中实现三个方法：
 * <p>
 * public void messageArrived(MqttTopic topic, MqttMessage message)接收已经预订的发布。
 * <p>
 * public void connectionLost(Throwable cause)在断开连接时调用。
 * <p>
 * public void deliveryComplete(MqttDeliveryToken token))
 * 接收到已经发布的 QoS 1 或 QoS 2 消息的传递令牌时调用。
 * 由 MqttClient.connect 激活此回调。
 */
public class PushCallback implements MqttCallback {
    private static final Logger log = LoggerFactory.getLogger(PushCallback.class);

    @Autowired
    private MqMessageRecvServer server = new MqMessageRecvServerImpl();

    public PushCallback() {

    }

    @Override
    public void connectionLost(Throwable cause) {
        // 连接丢失后，一般在这里面进行重连
        log.info("[MQTT] 连接断开，30S之后尝试重连...");
    }

    @Override
    public void deliveryComplete(IMqttDeliveryToken token) {

    }

    @Override
    public void messageArrived(String topic, MqttMessage message) throws Exception {
        HaLog.info(log, MsgIdConstant.MS_INF_0001, "mqtt事件消息监听");
        // subscribe后得到的消息会执行到这里面
        String messageBody = new String(message.getPayload());

        HaLog.infoJson(log, messageBody);

        JSONObject jsonObject = JSONObject.parseObject(messageBody);
        if (null == jsonObject) {
            return;
        }

        Meta meta = new Meta();
        meta.setEventType(topic);
        meta.setRecvSequence(UuidUtil.getUuid32());
        server.handle(meta, jsonObject.toString());
        HaLog.info(log, MsgIdConstant.MS_INF_0002, "mqtt事件消息监听");

    }
}