package com.air.security.sodb.dts.srvc.domain.service;

import com.air.security.sodb.data.core.service.ServiceOutDTO;
import com.air.security.sodb.dts.srvc.restsrvc.platform.demo.dto.DemoDto;
import com.air.security.sodb.dts.srvc.restsrvc.platform.demo.form.DemoForm;

import java.util.List;

/**
 * <AUTHOR>
 */
public interface DemoService {

    /**
     * 新增
     *
     * @param demo
     * @return
     */
    public ServiceOutDTO<Boolean> add(DemoForm demo);

    /**
     * 修改
     *
     * @param demo
     * @return
     */
    public ServiceOutDTO<Boolean> update(DemoForm demo);

    /**
     * 删除
     *
     * @param uuid
     * @return
     */
    public ServiceOutDTO<Boolean> delete(String uuid);

    /**
     * 根据id获取数据
     * @param uuid
     * @return
     */
    public ServiceOutDTO<DemoDto> getById(String uuid);

    /**
     * 获取集合
     * @param demo
     * @return
     */
    public ServiceOutDTO<List<DemoDto>> getList(DemoForm demo);

}
