package com.air.security.sodb.dts.srvc.restsrvc.receive.cms;

import com.air.security.sodb.data.core.constant.MsgIdConstant;
import com.air.security.sodb.data.core.exception.SystemBaseException;
import com.air.security.sodb.data.core.util.*;
import com.air.security.sodb.dts.receive.listener.mqtt.MqttRecListener;
import com.air.security.sodb.dts.srvc.restsrvc.platform.cms.dto.CmsEquSyncDto;
import com.alibaba.fastjson.JSONObject;
import com.caacitc.esb.dto.ProducerSendResult;
import com.caacitc.rabbitmq.client.EsbClient;
import org.apache.commons.lang3.StringUtils;
import org.slf4j.Logger;
import org.slf4j.LoggerFactory;
import org.springframework.web.bind.annotation.*;

import java.text.SimpleDateFormat;
import java.util.Date;

/**
 * @description: 道口设备整表请求消息
 * <AUTHOR>
 * @date 2021/11/20
 */
@CrossOrigin
@RestController
@RequestMapping("/api/release/cms/dnyc")
public class CmsEquSyncController {

    private static final Logger log = LoggerFactory.getLogger(CmsEquSyncController.class);

    @RequestMapping(value = "/execute", method = RequestMethod.POST)
    public ResultVo execute(@RequestBody CmsEquSyncDto cmsEquSyncDto){
        HaLog.info(log, MsgIdConstant.MS_INF_0001, "道口设备整表请求消息监听");
        if (cmsEquSyncDto == null || cmsEquSyncDto.getRequestType() == null) {
            throw new SystemBaseException("请求类型不能为空");
        }
        JSONObject msg = new JSONObject();
        JSONObject head = new JSONObject();
        JSONObject body = new JSONObject();
        JSONObject deviceRequest = new JSONObject();
        SimpleDateFormat dateFormat = new SimpleDateFormat("yyyyMMddHHmmssSSS");
        String nowTime = dateFormat.format(new Date());

        if (cmsEquSyncDto.getRequestType() == 1) {
            deviceRequest.put("request_type", cmsEquSyncDto.getRequestType());
        } else if (cmsEquSyncDto.getRequestType() == 2) {
            if (StringUtils.isBlank(cmsEquSyncDto.getDeviceCode())) {
                throw new SystemBaseException("按设备编码请求时，设备编码不能为空");
            }
            deviceRequest.put("request_type", cmsEquSyncDto.getRequestType());
            deviceRequest.put("device_code", cmsEquSyncDto.getDeviceCode());
        } else if (cmsEquSyncDto.getRequestType() == 3) {
            if (StringUtils.isBlank(cmsEquSyncDto.getDeviceType())) {
                throw new SystemBaseException("按设备类型请求时，设备类型不能为空");
            }
            deviceRequest.put("request_type", cmsEquSyncDto.getRequestType());
            deviceRequest.put("device_type", cmsEquSyncDto.getDeviceType());
        }
        body.put("device_request", deviceRequest);
        head.put("service_code", "SMP_CMS_DEVICE_REQUEST");
        head.put("version", "1.0");
        head.put("sender_platform", "SMP");
        head.put("sender_sys", "SMP");
        head.put("receiver_platform", "SMP");
        head.put("receiver_sys", "CMS");
        head.put("session_number", "");
        head.put("session_id", UuidUtil.getUuid32());
        head.put("time_stamp", nowTime);
        msg.put("head", head);
        msg.put("body", body);

        HaLog.info(log, MsgIdConstant.MS_INF_0004, "mqtt>>>>>", msg.toString());

        try {
            MqttRecListener en = new MqttRecListener();

            boolean flag = en.connectRabbitMq(msg.toString(),"cms/device/request");
            if (flag) {
                HaLog.info(log, MsgIdConstant.MS_INF_0002, "道口设备整表请求消息监听");
                return ResultVo.success();
            } else {
                return ResultVo.failure();
            }
        } catch (Exception e) {
            return ResultVo.failure();
        } finally {
            HaLog.info(log, MsgIdConstant.MS_INF_0002, "道口设备整表请求消息监听");
        }
    }
}
