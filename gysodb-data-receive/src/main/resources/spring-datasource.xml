<?xml version="1.0" encoding="UTF-8"?>
<beans xmlns="http://www.springframework.org/schema/beans"
       xmlns:xsi="http://www.w3.org/2001/XMLSchema-instance"
       xmlns:context="http://www.springframework.org/schema/context"
       xmlns:mvc="http://www.springframework.org/schema/mvc"
       xmlns:aop="http://www.springframework.org/schema/aop"
       xmlns:tx="http://www.springframework.org/schema/tx"
       xsi:schemaLocation="http://www.springframework.org/schema/context
        http://www.springframework.org/schema/context/spring-context-4.0.xsd
        http://www.springframework.org/schema/beans
        http://www.springframework.org/schema/beans/spring-beans-4.0.xsd
        http://www.springframework.org/schema/mvc
        http://www.springframework.org/schema/mvc/spring-mvc.xsd
        http://www.springframework.org/schema/aop
        http://www.springframework.org/schema/aop/spring-aop-4.2.xsd
        http://www.springframework.org/schema/tx
        http://www.springframework.org/schema/tx/spring-tx.xsd">

    <bean id="dataSource" class="org.apache.tomcat.jdbc.pool.DataSource"
          destroy-method="close" primary="true">
        <property name="driverClassName" value="${datasource.driverClassName}"/>
        <property name="url" value="${datasource.url}"/>
        <property name="username" value="${datasource.username}"/>
        <property name="password" value="${datasource.password}"/>
        <property name="initialSize" value="${datasource.initialSize}"/>
        <property name="maxActive" value="${datasource.maxActive}"/>
        <property name="maxIdle" value="${datasource.maxIdle}"/>
        <property name="minIdle" value="${datasource.minIdle}"/>
        <property name="maxWait" value="${datasource.maxWait}"/>
        <property name="validationQuery" value="${datasource.validationQuery}"/>
        <property name="testOnBorrow" value="${datasource.testOnBorrow}"/>
        <property name="testOnReturn" value="${datasource.testOnReturn}"/>
        <property name="timeBetweenEvictionRunsMillis"
                  value="${datasource.timeBetweenEvictionRunsMillis}"/>
        <property name="minEvictableIdleTimeMillis"
                  value="${datasource.minEvictableIdleTimeMillis}"/>
    </bean>

    <!-- 事务管理器 -->
    <bean id="transactionManager"
          class="org.springframework.jdbc.datasource.DataSourceTransactionManager">
        <property name="dataSource" ref="dataSource"/>
    </bean>

    <bean id="sqlSessionFactory" class="org.mybatis.spring.SqlSessionFactoryBean">
        <property name="dataSource" ref="dataSource"/>
        <property name="databaseIdProvider" ref="databaseIdProvider"/>
        <property name="mapperLocations" value="classpath*:mapper/*.xml"></property>
        <property name="configLocation" value="classpath:mybatis/mybatis-config.xml"></property>
        <property name="plugins">
            <array>
                <bean class="com.github.pagehelper.PageHelper">
                    <property name="properties">
                        <value>
                            dialect=${db.type}
                            <!--返回count -->
                            rowBoundsWithCount=true
                            <!--offset会当成pageNum使用，limit和pageSize含义相同 -->
                            offsetAsPageNum=true
                            <!--这时如果pageNum<1会查询第一页，如果pageNum>总页数会查询最后一页 -->
                            reasonable=true
                        </value>
                    </property>
                </bean>
            </array>
        </property>
    </bean>

    <bean id="sqlSession" class="org.mybatis.spring.SqlSessionTemplate">
        <constructor-arg index="0" ref="sqlSessionFactory"/>
    </bean>

    <bean id="databaseIdProvider" class="org.apache.ibatis.mapping.VendorDatabaseIdProvider">
        <property name="properties" ref="vendorProperties"/>
    </bean>

    <bean id="vendorProperties"
          class="org.springframework.beans.factory.config.PropertiesFactoryBean">
        <property name="properties">
            <props>
                <prop key="Oracle">oracle</prop>
                <prop key="MySQL">mysql</prop>
            </props>
        </property>
    </bean>

    <bean class="tk.mybatis.spring.mapper.MapperScannerConfigurer">
        <property name="basePackage" value="com.air.security.sodb.dts.srvc.domain.mapper"/>
        <property name="sqlSessionFactoryBeanName" value="sqlSessionFactory"/>
        <property name="properties">
            <value>
                mappers=tk.mybatis.mapper.common.Mapper,tk.mybatis.mapper.common.ConditionMapper
            </value>
        </property>
    </bean>

    <tx:annotation-driven transaction-manager="transactionManager"/>

    <tx:advice id="txAdvice" transaction-manager="transactionManager">
        <tx:attributes>
            <!-- 为连接点指定事务属性 -->
            <tx:method name="execute" isolation="DEFAULT" propagation="REQUIRED"
                       rollback-for="com.air.security.sodb.data.core.exception.SystemBaseException"/>
        </tx:attributes>
    </tx:advice>

    <aop:config>
        <!-- 切入点配置 -->
        <!-- 任意返回值 包路径 任意类 任意方法 任意参数 -->
        <aop:pointcut expression="execution(* com.air.security.sodb..service..*.*(..))"
                      id="point"/>
        <aop:advisor advice-ref="txAdvice" pointcut-ref="point"/>
    </aop:config>
</beans>