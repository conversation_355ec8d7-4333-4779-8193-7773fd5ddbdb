#4.1.1.6\u822A\u73ED\u5171\u4EAB\u53D8\u66F4\u4E8B\u4EF6\uFF08SFLG\uFF09
SFLG = DfltMessageRecvFormatServiceImpl,iSodbFlight,SFLG,FLIGHT_DATA
#4.1.1.7\u822A\u73ED\u822A\u7EBF\u53D8\u66F4\u4E8B\u4EF6\uFF08AIRL\uFF09
AIRL = DfltMessageRecvFormatServiceImpl,iSodbFlight,AIRL,FLIGHT_DATA
#4.1.1.8\u822A\u73ED\u53F7\u53D8\u66F4\u4E8B\u4EF6\uFF08HBTT\uFF09
HBTT = DfltMessageRecvFormatServiceImpl,iSodbFlight,HBTT,FLIGHT_DATA
#4.1.1.9\u822A\u73ED\u524D\u7AD9\u8D77\u98DE\u4E8B\u4EF6\uFF08ONRE\uFF09
ONRE = DfltMessageRecvFormatServiceImpl,iSodbFlight,ONRE,FLIGHT_DATA
#4.1.1.10\u822A\u73ED\u5230\u8FBE\u672C\u7AD9\u4E8B\u4EF6\uFF08ARRE\uFF09
ARRE = DfltMessageRecvFormatServiceImpl,iSodbFlight,ARRE,FLIGHT_DATA
#4.1.1.11\u822A\u73ED\u672C\u7AD9\u8D77\u98DE\u4E8B\u4EF6\uFF08DEPE\uFF09
DEPE = DfltMessageRecvFormatServiceImpl,iSodbFlight,DEPE,FLIGHT_DATA
#4.1.1.13\u822A\u73ED\u5F00\u59CB\u503C\u673A\u4E8B\u4EF6\uFF08CKIE\uFF09
CKIE = DfltMessageRecvFormatServiceImpl,iSodbFlight,CKIE,FLIGHT_DATA
#4.1.1.14\u822A\u73ED\u622A\u6B62\u503C\u673A\u4E8B\u4EF6\uFF08CKOE\uFF09
CKOE = DfltMessageRecvFormatServiceImpl,iSodbFlight,CKOE,FLIGHT_DATA
#4.1.1.15\u822A\u73ED\u5F00\u59CB\u767B\u673A\u4E8B\u4EF6\uFF08BORE\uFF09
BORE = DfltMessageRecvFormatServiceImpl,iSodbFlight,BORE,FLIGHT_DATA
#4.1.1.16\u822A\u73ED\u8FC7\u7AD9\u767B\u673A\u4E8B\u4EF6\uFF08TBRE\uFF09
TBRE = DfltMessageRecvFormatServiceImpl,iSodbFlight,TBRE,FLIGHT_DATA
#4.1.1.17\u822A\u73ED\u50AC\u4FC3\u767B\u673A\u4E8B\u4EF6\uFF08LBDE\uFF09
LBDE = DfltMessageRecvFormatServiceImpl,iSodbFlight,LBDE,FLIGHT_DATA
#4.1.1.18\u822A\u73ED\u7ED3\u675F\u767B\u673A\u4E8B\u4EF6\uFF08POKE\uFF09
POKE = DfltMessageRecvFormatServiceImpl,iSodbFlight,POKE,FLIGHT_DATA
#4.1.1.19\u822A\u73ED\u5EF6\u8BEF\u4E8B\u4EF6\uFF08DLYE\uFF09
DLYE = DfltMessageRecvFormatServiceImpl,iSodbFlight,DLYE,FLIGHT_DATA
#4.1.1.20\u822A\u73ED\u53D6\u6D88\u4E8B\u4EF6\uFF08CANE\uFF09
CANE = DfltMessageRecvFormatServiceImpl,iSodbFlight,CANE,FLIGHT_DATA
#********\u822A\u73ED\u8FD4\u822A\u4E8B\u4EF6\uFF08RTNE\uFF09
RTNE = DfltMessageRecvFormatServiceImpl,iSodbFlight,RTNE,FLIGHT_DATA
#********\u822A\u73ED\u6ED1\u56DE\u4E8B\u4EF6\uFF08BAKE\uFF09
BAKE = DfltMessageRecvFormatServiceImpl,iSodbFlight,BAKE,FLIGHT_DATA
#********\u822A\u73ED\u5907\u964D\u4E8B\u4EF6\uFF08ALTE\uFF09
ALTE = DfltMessageRecvFormatServiceImpl,iSodbFlight,ALTE,FLIGHT_DATA
#********\u822A\u73ED\u66F4\u6362\u98DE\u673A\u4E8B\u4EF6\uFF08CFCE\uFF09
CFCE = DfltMessageRecvFormatServiceImpl,iSodbFlight,CFCE,FLIGHT_DATA
#********\u822A\u73EDVIP\u4E8B\u4EF6\uFF08VIP\uFF09
VIP = DfltMessageRecvFormatServiceImpl,iSodbFlight,VIP,FLIGHT_DATA
#********\u822A\u73ED\u767B\u673A\u95E8\u52A8\u6001\u4FE1\u606F\u66F4\u65B0\u4E8B\u4EF6\uFF08GTLS\uFF09
GTLS = DfltMessageRecvFormatServiceImpl,iSodbFlight,GTLS,FLIGHT_DATA
#********\u822A\u73ED\u884C\u674E\u63D0\u53D6\u8F6C\u76D8\u52A8\u6001\u4FE1\u606F\u66F4\u65B0\u4E8B\u4EF6\uFF08BLLS\uFF09
BLLS = DfltMessageRecvFormatServiceImpl,iSodbFlight,BLLS,FLIGHT_DATA
#********\u822A\u73ED\u503C\u673A\u67DC\u53F0\u52A8\u6001\u4FE1\u606F\u66F4\u65B0\u4E8B\u4EF6\uFF08CKLS\uFF09
CKLS = DfltMessageRecvFormatServiceImpl,iSodbFlight,CKLS,FLIGHT_DATA
#********\u822A\u73ED\u673A\u4F4D\u52A8\u6001\u4FE1\u606F\u66F4\u65B0\u4E8B\u4EF6\uFF08STLS\uFF09
STLS = DfltMessageRecvFormatServiceImpl,iSodbFlight,STLS,FLIGHT_DATA
#********\u822A\u73ED\u8BA1\u5212\u65F6\u95F4\u4E8B\u4EF6\uFF08FPTT\uFF09
FPTT = DfltMessageRecvFormatServiceImpl,iSodbFlight,FPTT,FLIGHT_DATA
#********\u822A\u73ED\u9884\u8BA1\u65F6\u95F4\u4E8B\u4EF6\uFF08FETT\uFF09
FETT = DfltMessageRecvFormatServiceImpl,iSodbFlight,FETT,FLIGHT_DATA
#********\u822A\u73ED\u5B9E\u9645\u65F6\u95F4\u4E8B\u4EF6\uFF08FRTT\uFF09
FRTT = DfltMessageRecvFormatServiceImpl,iSodbFlight,FRTT,FLIGHT_DATA
#********\u822A\u73ED\u8DD1\u9053\u53D8\u66F4\u4E8B\u4EF6\uFF08RWAY\uFF09
RWAY = DfltMessageRecvFormatServiceImpl,iSodbFlight,RWAY,FLIGHT_DATA
#********\u822A\u73ED\u822A\u7AD9\u697C\u53D8\u66F4\u4E8B\u4EF6\uFF08TRML\uFF09
TRML = DfltMessageRecvFormatServiceImpl,iSodbFlight,TRML,FLIGHT_DATA
#4.1.1.37\u822A\u73ED\u5C5E\u6027\u53D8\u66F4\u4E8B\u4EF6\uFF08FATT\uFF09
FATT = DfltMessageRecvFormatServiceImpl,iSodbFlight,FATT,FLIGHT_DATA
#4.1.3.5\u5F02\u5E38\u539F\u56E0\u6574\u8868\u6570\u636E\u540C\u6B65\u4E8B\u4EF6\uFF08ARDL\uFF09
ARDL = ArdlMessageRecvFormatServiceImpl,cContainerFlight,ARDL,FLIGHT_DATA
#4.1.3.52\u767B\u673A\u95E8\u6574\u8868\u6570\u636E\u540C\u6B65\u4E8B\u4EF6\uFF08GTDL\uFF09
GTDL = GtdlMessageRecvFormatServiceImpl,cContainerEqu,GTDL_EQU,EQU
#4.1.3.67\u503C\u673A\u67DC\u53F0\u4FE1\u606F\u6574\u8868\u6570\u636E\u540C\u6B65\u4E8B\u4EF6\uFF08CCDL\uFF09
CCDL = CcdlMessageRecvFormatServiceImpl,cContainerEqu,CCDL_EQU,EQU
#4.1.1.1 \u822A\u73ED\u52A8\u6001\u589E\u52A0\u4E8B\u4EF6\u8BF4\u660E\uFF08DFIE\uFF09
DFIE = DfltMessageRecvFormatServiceImpl,iSodbFlight,DFIE,FLIGHT_DATA
#4.1.1.2 \u52A8\u6001\u5220\u9664\u4E8B\u4EF6\u8BF4\u660E\uFF08DFDE\uFF09
DFDE = DfltMessageRecvFormatServiceImpl,iSodbFlight,DFDE,FLIGHT_DATA
#4.1.1.4 \u52A8\u6001\u822A\u73ED\u6574\u8868\u540C\u6B65\u4E8B\u4EF6\uFF08DFDL\uFF09
DFDL = DfltMessageRecvFormatServiceImpl,iSodbFlight,DFDL,FLIGHT_DATA
#4.3.1.5\u6C14\u8C61\u4FE1\u606F(QXXX)
QXXX = QxxxMessageRecvFormatServiceImpl,cContainerWeather,WEATHER_DYN,WEATHER_DATA
#4.1.1.27\u822A\u73ED\u884C\u674E\u6ED1\u69FD\u53E3\u52A8\u6001\u4FE1\u606F\u66F4\u65B0\u4E8B\u4EF6\uFF08CHLS\uFF09
CHLS = DfltMessageRecvFormatServiceImpl,iSodbFlight,CHLS,FLIGHT_DATA
#4.3.1.3\u822A\u73ED\u4FDD\u969C\u4FE1\u606F(HBBZ)
DPUE = DpueMessageRecvFormatServiceImpl,cContainerFlight,DPUE,FLIGHT_DATA

NXTE = DfltMessageRecvFormatServiceImpl,iSodbFlight,NXTE,FLIGHT_DATA

STIE = StndMessageRecvFormatServiceImpl,cContainerEqu,STIE_EQU_ADD,EQU
STUE = StndMessageRecvFormatServiceImpl,cContainerEqu,STUE_EQU_UPDATE,EQU
STDE = StndMessageRecvFormatServiceImpl,cContainerEqu,STDE_EQU_DELETE,EQU
STDL = StndMessageRecvFormatServiceImpl,cContainerEqu,STDL_EQU_SYNC,EQU


######################################\u603B\u7EBFmqtt\u5BF9\u63A5\u6570\u636E######\u5F00\u59CB############################################################

acs/device/add =EquMessageRecvFormatServiceImpl,cContainerEqu,ACS_EQU_ADD,EQU
acs/device/update =EquMessageRecvFormatServiceImpl,cContainerEqu,ACS_EQU_UPDATE,EQU
acs/device/delete =EquMessageRecvFormatServiceImpl,cContainerEqu,ACS_EQU_DELETE,EQU
acs/device/sync = EquDyncMessageRecvFormatServiceImpl,cContainerEqu,ACS_EQU_SYNC,EQU
acs/device/statechange = EquUpMessageRecvFormatServiceImpl,cContainerEqu,ACS_EQU_UP,EQU_STATES
acs/accesscontrol/paybycard =CardMessageRecvFormatServiceImpl,cContainerRecord,ACS_RECORD_CARD,RECORD_CARD
acs/alarm/trigger =AlarmMessageRecvFormatServiceImpl,cContainerAlarm,ACS_ALARM,ALARM


cms/device/add = EquMessageRecvFormatServiceImpl,cContainerEqu,CMS_EQU_ADD,EQU
cms/device/update =EquMessageRecvFormatServiceImpl,cContainerEqu,CMS_EQU_UPDATE,EQU
cms/device/delete =EquMessageRecvFormatServiceImpl,cContainerEqu,CMS_EQU_DELETE,EQU
cms/device/sync =EquDeDyncMessageRecvFormatServiceImpl,cContainerEqu,CMS_EQU_SYNC,EQU
cms/device/statechange =EquUpMessageRecvFormatServiceImpl,cContainerEqu,CMS_EQU_UP,EQU_STATES
cms/alarm/trigger =AlarmMessageRecvFormatServiceImpl,cContainerAlarm,CMS_ALARM,ALARM
cms/car/transit =CrossMessageRecvFormatServiceImpl,cContainerCrossing,CMS_STAFF_PASS_INFO,CROSSING_DATA

ybbj/device/add = EquMessageRecvFormatServiceImpl,cContainerEqu,YBBJ_EQU_ADD,EQU
ybbj/device/update = EquMessageRecvFormatServiceImpl,cContainerEqu,YBBJ_EQU_UPDATE,EQU
ybbj/device/delete=EquMessageRecvFormatServiceImpl,cContainerEqu,YBBJ_EQU_DELETE,EQU
ybbj/device/sync=EquDeDyncMessageRecvFormatServiceImpl,cContainerEqu,YBBJ_EQU_SYNC,EQU
ybbj/device/statechange=EquUpMessageRecvFormatServiceImpl,cContainerEqu,YBBJ_EQU_UP,EQU_STATES
ybbj/alarm/trigger=AlarmMessageRecvFormatServiceImpl,cContainerAlarm,YBBJ_ALARM,ALARM
ais/device/add=EquMessageRecvFormatServiceImpl,cContainerEqu,AIS_EQU_ADD,EQU
ais/device/update=EquMessageRecvFormatServiceImpl,cContainerEqu,AIS_EQU_UPDATE,EQU
ais/device/delete=EquMessageRecvFormatServiceImpl,cContainerEqu,AIS_EQU_DELETE,EQU
ais/device/sync=EquDeDyncMessageRecvFormatServiceImpl,cContainerEqu,AIS_EQU_SYNC,EQU
ais/device/statechange=EquUpMessageRecvFormatServiceImpl,cContainerEqu,AIS_EQU_UP,EQU_STATES

ais/alarm/trigger=AlarmMessageRecvFormatServiceImpl,cContainerAlarm|cContainerAlarmWj,AIS_ALARM|WJ_AIS_ALARM,ALARM|WJ_ALARM
ais/alarm/deactive=AlarmMessageRecvFormatServiceImpl,cContainerAlarm|cContainerAlarmWj,AIS_ALARM_DEACTIVE|WJ_AIS_ALARM_DEACTIVE,ALARM|WJ_ALARM
t2acs/device/add=EquMessageRecvFormatServiceImpl,cContainerEqu,T2ACS_EQU_ADD,EQU
t2acs/device/update=EquMessageRecvFormatServiceImpl,cContainerEqu,T2ACS_EQU_UPDATE,EQU
hyacs/device/delete=EquMessageRecvFormatServiceImpl,cContainerEqu,T2ACS_EQU_DELETE,EQU
hyacs/device/sync=EquDyncMessageRecvFormatServiceImpl,cContainerEqu,T2ACS_EQU_SYNC,EQU
hyacs/device/statechange=EquUpMessageRecvFormatServiceImpl,cContainerEqu,T2ACS_EQU_UP,EQU_STATES
hyacs/accesscontrol/paybycard=CardMessageRecvFormatServiceImpl,cContainerRecord,T2ACS_RECORD_CARD,RECORD_CARD
hyacs/alarm/trigger=AlarmMessageRecvFormatServiceImpl,cContainerAlarm,T2ACS_ALARM,ALARM


vms/alarm/trigger =AlarmMessageRecvFormatServiceImpl,cContainerAlarm,VMS_ALARM,ALARM
zvams/alarm/trigger =MessageRecvFormatServiceImpl,cContainerAlarm,ZVAMS_ALARM,ALARM
zvams/analysis/behavior/areainvasion = MessageRecvFormatServiceImpl,cContainerAlarm,ZVAMS_AREAINVASION_ALARM,ALARM
zvams/analysis/behavior/border = MessageRecvFormatServiceImpl,cContainerAlarm,ZVAMS_BORDER_ALARM,ALARM
zvams/analysis/behavior/wandering = MessageRecvFormatServiceImpl,cContainerAlarm,ZVAMS_WANDERING_ALARM,ALARM
zvams/analysis/behavior/carryover = MessageRecvFormatServiceImpl,cContainerAlarm,ZVAMS_CARRYOVER_ALARM,ALARM
zvams/analysis/behavior/posture = MessageRecvFormatServiceImpl,cContainerAlarm,ZVAMS_POSTURE_ALARM,ALARM
zvams/analysis/density/alarm = MessageRecvFormatServiceImpl,cContainerAlarm,ZVAMS_DENSITY_ALARM,ALARM
zvams/analysis/queue/alarm = MessageRecvFormatServiceImpl,cContainerAlarm,ZVAMS_QUEUE_ALARM,ALARM
t2vms/alarm/trigger = AlarmMessageRecvFormatServiceImpl,cContainerAlarm,T2VMS_ALARM,ALARM

######################################\u603B\u7EBFmqtt\u5BF9\u63A5\u6570\u636E######\u7ED3\u675F############################################################
#3.1\u5B89\u68C0\u901A\u9053\u4FE1\u606F(SCPI)
SCPI = ScpiMessageRecvFormatServiceImpl,cContainerEqu,SCPI,EQU
#3.2\u5B89\u68C0\u901A\u9053\u7684\u5F00\u653E\u4FE1\u606F(SCPS)
SCPS = ScpsMessageRecvFormatServiceImpl,cContainerEqu,SCPS,EQU
#3.3\u8FD0\u5355\u6570\u636E\u4ECB\u5165(SCBA)
SCBA = ScbaMessageRecvFormatServiceImpl,cContainerCargo,SCBA,CARGO
#3.4\u5F00\u5305\u6570\u636E\u63A5\u5165(UODA)
UODA = ScbaMessageRecvFormatServiceImpl,cContainerCargo,UODA,CARGO
#3.5\u8FD0\u5355\u5B89\u68C0\u6570\u636E\u63A5\u5165(SCCD)
SCCD = ScbaMessageRecvFormatServiceImpl,cContainerCargo,SCCD,CARGO
#3.6\u8D27\u8FD0\u5B89\u68C0\u7ED3\u679C\u6570\u636E\uFF08FTSR\uFF09
FTSR = FtsrMessageRecvFormatServiceImpl,cContainerCargo,FTSR,CARGO