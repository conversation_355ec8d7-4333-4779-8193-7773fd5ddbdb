#\u7EBF\u7A0B\u6C60\u5927\u5C0F
threadpool.count=20

#kafkaConsumer kafkaæ¶è´¹èéç½®æä»¶è·¯å¾
#kafka.consumer.config.path=D:\\config\\kafkaConsumer.properties
kafka.consumer.config.path=/server/dtsServer/gySodbRece/config/kafkaConsumer.properties

#kafkaProducer kafkaæ¶è´¹èéç½®æä»¶è·¯å¾
#kafka.producer.config.path=D:\\config\\kafkaProducer.properties
kafka.producer.config.path=/server/dtsServer/gySodbRece/config/kafkaProducer.properties
#gisåæ åæ­¥url
gis.request.url=http://10.126.12.13:8088/GISW1GisService/incrementRest/gisdata.do?systemCode=GIS&dataTheme=anbaopt&outsr=berghaus
gis.layer.config.path=/server/dtsServer/gySodbRece/config/gisLayer.json
EC01_CONF_PATH =/server/dtsServer/gySodbRece/config/EC01_CONF_PATH.json
EC02_CONF_PATH =/server/dtsServer/gySodbRece/config/EC02_CONF_PATH.json

flight.service=true
mqtt.service = true

#æ¥æ¶ä¸»é¢
esb.accept.topic=FLIGHT

######################################æ»çº¿mqttå¯¹æ¥æ°æ®######å¼å§############################################################
sip.rabbit.mq.url =tcp://***********:1884
sip.rabbit.mq.username = sms_subsystem
sip.rabbit.mq.password =sms_gyjc!@#
sip.rabbit.mq.accept.topic =acs/device/add,acs/device/update \
,acs/device/delete \
,acs/device/sync \
,acs/device/statechange \
,acs/accesscontrol/paybycard \
,acs/alarm/trigger \
,cms/device/add \
,cms/device/update \
,cms/device/delete \
,cms/device/sync \
,cms/device/statechange \
,cms/alarm/trigger \
,cms/car/transit \
,ybbj/device/add \
,ybbj/device/update \
,ybbj/device/delete \
,ybbj/device/sync \
,ybbj/device/statechange \
,ybbj/alarm/trigger \
,ais/device/add \
,ais/device/update \
,ais/device/delete \
,ais/device/sync \
,ais/device/statechange \
,ais/alarm/trigger \
,ais/alarm/deactive \
,t2acs/device/add \
,t2acs/device/update \
,hyacs/device/delete \
,hyacs/device/sync \
,hyacs/device/statechange \
,hyacs/accesscontrol/paybycard \
,hyacs/alarm/trigger\
,vms/alarm/trigger\
,zvams/alarm/trigger\
,zvams/analysis/behavior/areainvasion\
,zvams/analysis/behavior/border\
,zvams/analysis/behavior/wandering\
,zvams/analysis/behavior/carryover\
,zvams/analysis/behavior/posture\
,t2vms/alarm/trigger\












######################################æ»çº¿mqttå¯¹æ¥æ°æ®######ç»æ############################################################




