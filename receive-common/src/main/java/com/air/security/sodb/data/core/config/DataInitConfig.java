package com.air.security.sodb.data.core.config;

/**
 * @Description: 数据初始化请求处理的配置
 * @author: zhangc
 * @Date: 2018年12月26日
 */
public class DataInitConfig {

    /**
     * 消息类型
     */
    private String msgType;

    /**
     * 消息类型名称
     */
    private String msgTypeName;

    /**
     * 事件类型
     */
    private String eventType;

    /**
     * 事件类型名称
     */
    private String eventTypeName;

    /**
     * 请求主题
     */
    private String reqTopic;

    /**
     * @return 消息类型
     */
    public String getMsgType() {
        return msgType;
    }

    /**
     * @param 消息类型
     */
    public void setMsgType(String msgType) {
        this.msgType = msgType;
    }

    /**
     * @return 消息类型名称
     */
    public String getMsgTypeName() {
        return msgTypeName;
    }

    /**
     * @param 消息类型名称
     */
    public void setMsgTypeName(String msgTypeName) {
        this.msgTypeName = msgTypeName;
    }

    /**
     * @return 事件类型
     */
    public String getEventType() {
        return eventType;
    }

    /**
     * @param 事件类型
     */
    public void setEventType(String eventType) {
        this.eventType = eventType;
    }

    /**
     * @return 事件类型名称
     */
    public String getEventTypeName() {
        return eventTypeName;
    }

    /**
     * @param 事件类型名称
     */
    public void setEventTypeName(String eventTypeName) {
        this.eventTypeName = eventTypeName;
    }

    public String getReqTopic() {
        return reqTopic;
    }

    public void setReqTopic(String reqTopic) {
        this.reqTopic = reqTopic;
    }

}
