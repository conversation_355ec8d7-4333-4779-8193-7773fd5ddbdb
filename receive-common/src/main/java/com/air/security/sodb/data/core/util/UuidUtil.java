package com.air.security.sodb.data.core.util;

import java.util.UUID;

/**
 * @Description: uuid工具类
 * @author: zhangc
 * @Date: 2018年12月10日
 */
public class UuidUtil {

    /**
     * 获取36位的UUID
     *
     * @return
     */
    public static String getUuid36() {

        return UUID.randomUUID().toString();
    }

    /**
     * 获取32位的UUID 中间去掉字符“-”
     *
     * @return
     */
    public static String getUuid32() {
        String uuid = UuidUtil.getUuid36();
        return uuid.replaceAll("-", "");
    }

    public static void main(String[] args) {
        int lens = 100;
        for (int i = 0; i < lens; i++) {

            System.out.println(UuidUtil.getUuid36());
        }
    }
}
