package com.air.security.sodb.data.core.util;

import java.util.regex.Pattern;

import org.apache.commons.lang3.ArrayUtils;
import org.apache.commons.lang3.StringUtils;

/**
 * <AUTHOR>
 */
public class StringUtil {

    private static final String IS_NUMBER_PATTERN = "^[-\\+]?[\\d]*$";

    /**
     * 拼接字符串, 返回StringBuilder对象
     *
     * @param strings
     * @return
     */
    public static StringBuilder appendsBuilder(Object... strings) {
        StringBuilder builder = new StringBuilder();

        if (ArrayUtils.isNotEmpty(strings)) {
            for (Object obj : strings) {
                if (obj == null) {
                    continue;
                }
                builder.append(obj);
            }
        }

        return builder;
    }

    /**
     * 如果为null， 则返回null
     *
     * @param obj
     * @return
     */
    public static String toString(Object obj) {
        return toString(obj, null);
    }

    /**
     * @param obj
     * @param defaultVal
     * @return
     */
    public static String toString(Object obj, String defaultVal) {
        return obj == null ? defaultVal : obj.toString();
    }

    /**
     * 拼接返回字符串
     *
     * @param strings
     * @return
     */
    public static String appends(Object... strings) {
        return appendsBuilder(strings).toString();
    }

    /**
     * 如果为空， 则返回被替换值
     *
     * @param str
     * @param replace
     * @return
     */
    public static String replaceIfBlank(String str, String replace) {
        return StringUtils.isBlank(str) ? replace : str;
    }

    /**
     * 判断是否为空，如果为空则返回第一个值，如果不为空则返回第二个值
     *
     * @param str
     * @param yes
     * @param no
     * @return
     */
    public static String ifBlank(String str, String yes, String no) {
        return StringUtils.isBlank(str) ? yes : no;
    }

    /**
     * 判断是否为null，如果为空则返回第一个值，如果不为空则返回第二个值
     *
     * @param str
     * @param yes
     * @param no
     * @return
     */
    public static String ifNull(String str, String yes, String no) {
        return str == null ? yes : no;
    }

    /**
     * @param s1
     * @param s2
     * @return
     */
    public static boolean equalsNotNull(String s1, String s2) {
        return s1 == null ? false : s1.equals(s2);
    }

    /**
     * @param s1
     * @param s2
     * @return
     */
    public static boolean equals(String s1, String s2) {
        return s1 == null ? s1 == s2 : s1.equals(s2);
    }

    /**
     * 第一个值存在于后面的值中的一个
     *
     * @param s1
     * @param strings
     * @return
     */
    public static boolean equalsOrIn(String s1, String... strings) {
        if (ArrayUtils.isEmpty(strings)) {
            return false;
        }
        for (String s : strings) {
            if (equals(s1, s)) {
                return true;
            }
        }
        return false;
    }

    /**
     * 如果第一个数组中的值存在有后面数组中的值，则返回true，否则返回false
     *
     * @param arr
     * @param strings
     * @return
     */
    public static boolean equalsOrIn(String[] arr, String... strings) {
        for (String s1 : arr) {
            if (equalsOrIn(s1, strings)) {
                return true;
            }
        }
        return false;
    }

    /**
     * 第一个数组的值trim后再比较，如果第一个数组中的值存在有后面数组中的值，则返回true，否则返回false
     *
     * @param arr
     * @param strings
     * @return
     */
    public static boolean trimEqualsOrIn(String[] arr, String... strings) {
        for (String s1 : arr) {
            if (equalsOrIn(s1.trim(), strings)) {
                return true;
            }
        }
        return false;
    }

    /**
     * 后面的所有值必须和第一个值相等
     *
     * @param s1
     * @param strings
     * @return
     */
    public static boolean equalsAndIn(String s1, String... strings) {
        for (String s : strings) {
            if (equals(s1, s) == false) {
                return false;
            }
        }
        return true;
    }

    /**
     * 判断是否为正整数
     *
     * @param s1
     * @return
     */
    public static boolean isNumber(String s1) {
        if (StringUtils.isEmpty(s1)) {
            return false;
        }
        Pattern pattern = Pattern.compile(IS_NUMBER_PATTERN);
        return pattern.matcher(s1).matches();
    }

    /**
     * 获取sql like 拼接字符串
     *
     * @param val
     * @return
     */
    public static String getSqlLike(String val) {
        return appends("%", val, "%");
    }

    /**
     * 获取最大长度字符串，如果超出长度，则以替换字符串代替
     *
     * @param str     原始字符串
     * @param length  字符串长度
     * @param replace 超过长度后替换成的内容
     * @return
     */
    public static String getLengthStr(String str, int length, String replace) {
        if (str == null) {
            return null;
        }
        if (str.length() <= length) {
            return str;
        }
        str = str.substring(0, length);
        return ifNull(replace, str, appends(str, replace));
    }

    /**
     * 字符串转Integer
     *
     * @param s
     * @param defaultVal
     * @return
     */
    public static Integer toInteger(String s, Integer defaultVal) {
        try {
            return s == null ? defaultVal : Integer.parseInt(s);
        } catch (Exception e) {
            return defaultVal;
        }
    }

}
