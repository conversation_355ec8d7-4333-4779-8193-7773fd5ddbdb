package com.air.security.sodb.data.core.util;

import java.util.Properties;

import org.apache.kafka.clients.admin.AdminClient;

import com.air.security.sodb.data.core.constant.GlobalCodeConstant;

/**
 * kafka工具类
 *
 * <AUTHOR>
 */
public class KafkaUtil {

    private static Properties properties = ResourceUtil.getInstance().getLocalProperties(
            PropertyUtil.getProperty(GlobalCodeConstant.KAFKA_CONSUMER_CONFIG_PATH_KEY), "kafkaUtil");

    private static AdminClient adminClient = null;

    /**
     * 获得一个adminClient
     *
     * @return
     */
    public static AdminClient getAdminClient() {
        if (null == adminClient) {
            synchronized (KafkaUtil.class) {
                if (null == adminClient) {
                    adminClient = AdminClient.create(properties);
                }
            }
        }
        return adminClient;
    }

}
