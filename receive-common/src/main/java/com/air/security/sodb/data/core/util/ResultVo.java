package com.air.security.sodb.data.core.util;

import java.io.Serializable;

/**
 * @version v 1.0.
 * @Descrption 请求接口返回对象
 * <AUTHOR>
 * @Date 2019-11-26
 */
public class ResultVo<T> implements Serializable {

    private T data;
    private Integer status;
    private ResultMessage message;

    private ResultVo() {
        this.status = ResultStatus.SUCCESS.status();
    }

    private ResultVo(Integer status) {
        this.status = ResultStatus.SUCCESS.status();
        this.status = status;
    }

    private ResultVo(Integer status, ResultMessage message) {
        this.status = ResultStatus.SUCCESS.status();
        this.status = status;
        this.message = message;
    }

    private ResultVo(T data, Integer status) {
        this.status = ResultStatus.SUCCESS.status();
        this.data = data;
        this.status = status;
    }

    private ResultVo(T data, Integer status, ResultMessage message) {
        this.status = ResultStatus.SUCCESS.status();
        this.data = data;
        this.status = status;
        this.message = message;
    }

    public T getData() {
        return this.data;
    }

    public ResultMessage getMessage() {
        return this.message;
    }

    public Integer getStatus() {
        return this.status;
    }

    public static <T> ResultVo<T> success(T data) {
        return new ResultVo(data, ResultStatus.SUCCESS.status());
    }

    public static <T> ResultVo<T> success() {
        return new ResultVo();
    }

    public static <T> ResultVo<T> success(T data, ResultMessage message) {
        return new ResultVo(data, ResultStatus.SUCCESS.status(), message);
    }

    public static <T> ResultVo<T> failure(T data, ResultMessage message) {
        return new ResultVo(data, ResultStatus.FAILURE.status(), message);
    }

    public static <T> ResultVo<T> failure(ResultMessage message) {
        return new ResultVo(ResultStatus.FAILURE.status(), message);
    }

    public static <T> ResultVo<T> failure() {
        return new ResultVo(ResultStatus.FAILURE.status());
    }

    public static ResultVo<Void> voidResult() {
        return new ResultVo();
    }

    @Override
    public String toString() {
        return "ResultVo{" +
                "data=" + data +
                ", status=" + status +
                ", message=" + message +
                '}';
    }
}


