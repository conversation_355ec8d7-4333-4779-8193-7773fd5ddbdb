package com.air.security.sodb.data.core.constant;

/**
 * sql常量类
 *
 * <AUTHOR>
 */
public class SqlConstant {

    /**
     * 新增sql
     */
    public final static String INSERT_COMMON = "common.insertCommon";

    /**
     * 更新sql
     */
    public final static String UPDATE_COMMON = "common.updateCommon";

    /**
     * 删除sql
     */
    public final static String DELETE_COMMON = "common.deleteCommon";

    /**
     * 查询sql
     */
    public final static String SELECT_COMMON = "common.selectCommon";

    /**
     * 大字段更新sql
     */
    public final static String UPDATE_CLOB_COMMON = "common.updateClob";

    /**
     * oracle时间戳格式
     */
    public static final String ORACLE_DATE_PATTERN = "YYYY-MM-DD HH24:MI:SS";

    /**
     * mysql时间戳格式
     */
    public static final String MYSQL_DATE_PATTERN = "%Y-%m-%d %H:%i:%s";
}
