package com.air.security.sodb.data.core.quartz;

/**
 * 定时器类
 *
 * <AUTHOR>
public class Config {
    /**
     * 任务名称
     */
    private String jobName;
    /**
     * 任务组
     */
    private String jobGroupName;
    /**
     * 任务描述
     */
    private String jobDesc;
    /**
     * 任务执行类
     */
    private String jobClass;
    /**
     * 定时器开关
     */
    private String switchTrigger;
    /**
     * 定时器名称
     */
    private String triggerName;
    /**
     * 定时器组
     */
    private String triggerGroup;
    /**
     * 定时器表达式
     */
    private String triggerCron;

    public String getJobName() {
        return jobName;
    }

    public void setJobName(String jobName) {
        this.jobName = jobName;
    }

    public String getJobGroupName() {
        return jobGroupName;
    }

    public void setJobGroupName(String jobGroupName) {
        this.jobGroupName = jobGroupName;
    }

    public String getJobDesc() {
        return jobDesc;
    }

    public void setJobDesc(String jobDesc) {
        this.jobDesc = jobDesc;
    }

    public String getJobClass() {
        return jobClass;
    }

    public void setJobClass(String jobClass) {
        this.jobClass = jobClass;
    }

    public String getTriggerName() {
        return triggerName;
    }

    public void setTriggerName(String triggerName) {
        this.triggerName = triggerName;
    }

    public String getTriggerGroup() {
        return triggerGroup;
    }

    public void setTriggerGroup(String triggerGroup) {
        this.triggerGroup = triggerGroup;
    }

    public String getTriggerCron() {
        return triggerCron;
    }

    public void setTriggerCron(String triggerCron) {
        this.triggerCron = triggerCron;
    }

    public String getSwitchTrigger() {
        return switchTrigger;
    }

    public void setSwitchTrigger(String switchTrigger) {
        this.switchTrigger = switchTrigger;
    }

}
