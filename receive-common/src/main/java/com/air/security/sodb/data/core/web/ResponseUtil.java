package com.air.security.sodb.data.core.web;

import com.air.security.sodb.data.core.constant.CodeConstant;
import com.air.security.sodb.data.core.exception.Be;
import org.slf4j.Logger;

import java.util.List;
import java.util.Map;


/**
 * <AUTHOR>
 */
public class ResponseUtil {

    public static final String SUCCESS_CODE = ResponseData.SUCCESS_CODE;
    public static final String ERROR_CODE = CodeConstant.BUSINESS_ERROR;
    public static final String LOGIN_TIMEOUT_CODE = CodeConstant.BUSINESS_LOGIN_FAILED;

    public static <T> ResponseData<T> getData() {
        return new ResponseData<T>();
    }

    /**
     * @return
     * @see CodeConstant#BUSINESS_SUCCESS
     */
    public static <T> ResponseData<T> getSuccessCodeData() {
        return getSuccessCodeData(null);
    }

    /**
     * @param data
     * @return
     * @see CodeConstant#BUSINESS_SUCCESS
     */
    public static <T> ResponseData<T> getSuccessCodeData(T data) {
        return getSuccessCodeData(null, data);
    }

    /**
     * @param message
     * @return
     */
    public static <T> ResponseData<T> getSuccessCodeData(String message) {
        return getSuccessCodeData(message, null);
    }

    /**
     * 登录超时
     *
     * @return
     */
    public static <T> ResponseData<T> getLogoutData() {
        return new ResponseData<>(LOGIN_TIMEOUT_CODE, "用户登录超时, 请重新登录");
    }

    /**
     * @param message
     * @param data
     * @return
     * @see CodeConstant#BUSINESS_SUCCESS
     */
    public static <T> ResponseData<T> getSuccessCodeData(String message, T data) {
        return new ResponseData<>(SUCCESS_CODE, message, data);
    }

    /**
     * @param message
     * @return
     * @see CodeConstant#BUSINESS_ERROR
     */
    public static <T> ResponseData<T> getErrorCodeData(String message) {
        return new ResponseData<>(ERROR_CODE, message);
    }

    /**
     * 如果是 系统业务异常，则返回系统业务异常信息，否则返回 message信息。
     *
     * @param e
     * @param message 如果不是业务异常，则返回该信息
     * @return
     */
    public static <T> ResponseData<T> getErrorCodeData(Exception e, String message) {
        return getErrorCodeData(null, e, message);
    }

    /**
     * <p>返回异常并打印异常日志。</p>
     * <p>如果是 系统业务异常，则返回系统业务异常信息，否则返回 message信息。</p>
     *
     * @param log
     * @param e
     * @param message 如果不是业务异常，则返回该信息
     * @return
     */
    public static <T> ResponseData<T> getErrorCodeData(Logger log, Exception e, String message) {
        if (log != null) {
            log.error(message, e);
        }
        return new ResponseData<>(ERROR_CODE, getExMessage(e, message));
    }

    public static <T> ResponseData<T> getData(String code, String message, T data) {
        return new ResponseData<T>(code, message, data);
    }

    public static <T> ResponseData<T> getData(String code, String message) {
        return new ResponseData<T>(code, message);
    }

    public static <T> ResponseData<T> getData(String message) {
        return new ResponseData<T>(message);
    }

    public static <T> ResponseData<T> getData(String message, T data) {
        return new ResponseData<T>(message, data);
    }


    public static <T> ResponseList<T> getList() {
        return new ResponseList<T>();
    }

    public static <T> ResponseList<T> getList(String message) {
        return new ResponseList<>(message);
    }

    public static <T> ResponseList<T> getList(String code, String message) {
        return new ResponseList<>(code, message);
    }

    public static <T> ResponseList<T> getList(String message, List<T> data) {
        return new ResponseList<>(SUCCESS_CODE, message, data);
    }

    public static <T> ResponseList<T> getList(String code, String message, List<T> data) {
        return new ResponseList<>(code, message, data);
    }


    public static <K, V> ResponseMap<K, V> getMap() {
        return new ResponseMap<K, V>();
    }

    /**
     * @param size
     * @return
     */
    public static <K, V> ResponseMap<K, V> getMap(int size) {
        return new ResponseMap<K, V>(size);
    }

    /**
     * @return
     * @see CodeConstant#BUSINESS_SUCCESS
     */
    public static <K, V> ResponseMap<K, V> getSuccessCodeMap() {
        return getSuccessCodeMap(null);
    }

    public static <K, V> ResponseMap<K, V> getSuccessCodeMap(int size) {
        return getSuccessCodeMap(null, size);
    }

    /**
     * @param message
     * @return
     * @see CodeConstant#BUSINESS_SUCCESS
     */
    public static <K, V> ResponseMap<K, V> getSuccessCodeMap(String message) {
        return new ResponseMap<>(SUCCESS_CODE, message);
    }

    /**
     * @param message 消息内容
     * @param size    map长度
     * @return
     */
    public static <K, V> ResponseMap<K, V> getSuccessCodeMap(String message, int size) {
        return new ResponseMap<>(SUCCESS_CODE, message, size);
    }

    /**
     * @param message
     * @return
     * @see CodeConstant#BUSINESS_ERROR
     */
    public static <K, V> ResponseMap<K, V> getErrorCodeMap(String message) {
        return new ResponseMap<>(ERROR_CODE, message);
    }

    public static <K, V> ResponseMap<K, V> getMap(String message) {
        return new ResponseMap<>(message);
    }

    public static <K, V> ResponseMap<K, V> getMap(String code, String message) {
        return new ResponseMap<>(code, message);
    }

    public static <K, V> ResponseMap<K, V> getMap(String message, Map<K, V> data) {
        return new ResponseMap<>(message, data);
    }

    public static <K, V> ResponseMap<K, V> getMap(String code, String message, Map<K, V> data) {
        return new ResponseMap<>(code, message, data);
    }

    /**
     * @param e
     * @param defaultMsg
     * @return
     */
    public static String getExMessage(Exception e, String defaultMsg) {
        if (e instanceof Be) {
            return e.getMessage();
        }
        return defaultMsg;
    }

}
