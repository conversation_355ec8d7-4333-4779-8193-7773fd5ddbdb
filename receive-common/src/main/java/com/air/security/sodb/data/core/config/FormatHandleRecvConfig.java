package com.air.security.sodb.data.core.config;

/**
 * @Description: 消息格式化处理的配置
 * @author: zhangc
 * @Date: 2018年12月26日
 */
public class FormatHandleRecvConfig {

    /**
     * 消息类型
     */
    private String msgType;

    /**
     * 消息类型名称
     */
    private String msgTypeName;

    /**
     * 事件类型
     */
    private String eventType;

    /**
     * 事件类型名称
     */
    private String eventTypeName;

    /**
     * spec转换配置文件路径
     */
    private String specPath;

    /**
     * 消息主题
     */
    private String topic;

    /**
     * @return 消息类型
     */
    public String getMsgType() {
        return msgType;
    }

    /**
     * @param 消息类型
     */
    public void setMsgType(String msgType) {
        this.msgType = msgType;
    }

    /**
     * @return 消息类型名称
     */
    public String getMsgTypeName() {
        return msgTypeName;
    }

    /**
     * @param 消息类型名称
     */
    public void setMsgTypeName(String msgTypeName) {
        this.msgTypeName = msgTypeName;
    }

    /**
     * @return 事件类型
     */
    public String getEventType() {
        return eventType;
    }

    /**
     * @param 事件类型
     */
    public void setEventType(String eventType) {
        this.eventType = eventType;
    }

    /**
     * @return 事件类型名称
     */
    public String getEventTypeName() {
        return eventTypeName;
    }

    /**
     * @param 事件类型名称
     */
    public void setEventTypeName(String eventTypeName) {
        this.eventTypeName = eventTypeName;
    }

    /**
     * @return spec转换配置文件路径
     */
    public String getSpecPath() {
        return specPath;
    }

    /**
     * @param spec转换配置文件路径
     */
    public void setSpecPath(String specPath) {
        this.specPath = specPath;
    }

    /**
     * @return 消息主题
     */
    public String getTopic() {
        return topic;
    }

    /**
     * @param 消息主题
     */
    public void setTopic(String topic) {
        this.topic = topic;
    }

}
