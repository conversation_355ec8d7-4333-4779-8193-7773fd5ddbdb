package com.air.security.sodb.data.core.util;

import java.util.List;

import com.air.security.sodb.data.core.config.SqlModule;
import org.apache.commons.lang3.StringUtils;

import com.alibaba.fastjson.JSONObject;

/**
 * @Description:
 * @author: zhangc
 * @Date: 2019年3月10日
 */
public class CreateSqlUtil {

    /**
     * 构建新增sql
     *
     * @param sqlModule
     * @return
     */
    public synchronized static String createInsertSql(SqlModule sqlModule) {

        List<String> columnList = sqlModule.getColumnList();
        List<String> keyList = sqlModule.getKeyList();
        List<String> typeList = sqlModule.getTypeList();
        JSONObject inputJson = sqlModule.getDataJson();

        StringBuilder columns = new StringBuilder();
        StringBuilder values = new StringBuilder();
        // 主键
        columns.append(" (CREATE_DATE,");
        values.append("(SYSDATE,");

        for (int i = 0, l = columnList.size(); i < l; i++) {
            String inputKey = keyList.get(i);
            String column = columnList.get(i);
            String type = typeList.get(i);
            String value = StringUtils.isNotBlank(inputJson.getString(inputKey)) ? inputJson.getString(inputKey) : "";
            value = value.replaceAll("'", "''");

            columns.append(column + ",");
            if (StringUtils.equals("String", type)) {
                values.append("'" + value + "',");
            } else {
                values.append("TO_DATE('" + value + "', 'YYYY-MM-DD HH24:MI:SS'),");
            }
        }

        columns.deleteCharAt(columns.length() - 1);
        values.deleteCharAt(values.length() - 1);

        // 创建时间
        columns.append(") ");
        values.append(") ");

        StringBuilder sql = new StringBuilder();
        sql.append("INSERT INTO ");
        sql.append(sqlModule.getTableName());
        sql.append(columns + " VALUES ");
        sql.append(values);

        return sql.toString();

    }

    /**
     * 构建更新数据的sql
     *
     * @param sqlModule
     * @param condition
     * @return
     */
    public synchronized static String createUpdateSql(SqlModule sqlModule, String condition) {
        List<String> columnList = sqlModule.getColumnList();
        List<String> keyList = sqlModule.getKeyList();
        List<String> typeList = sqlModule.getTypeList();
        JSONObject inputJson = sqlModule.getDataJson();

        StringBuilder sql = new StringBuilder();
        sql.append(" UPDATE ");
        sql.append(sqlModule.getTableName());
        sql.append(" SET ");

        for (int i = 0, l = columnList.size(); i < l; i++) {
            String inputKey = keyList.get(i);
            String column = columnList.get(i);

            if (StringUtils.equalsIgnoreCase("UUID", column)) {
                continue;
            }

            String type = typeList.get(i);
            String value = StringUtils.isNotBlank(inputJson.getString(inputKey)) ? inputJson.getString(inputKey) : "";
            value = value.replaceAll("'", "''");

            sql.append(column + "=");
            if (StringUtils.equals("String", type)) {
                sql.append("'" + value + "',");
            } else {
                sql.append("TO_DATE('" + value + "', 'YYYY-MM-DD HH24:MI:SS'),");
            }
        }

        sql.deleteCharAt(sql.length() - 1);

        sql.append(" WHERE " + condition);

        return sql.toString();

    }

    /**
     * 构建逻辑删除的sql
     *
     * @param sqlModule
     * @param condition
     * @return
     */
    public synchronized static String createLogicDelSql(SqlModule sqlModule, String condition) {
        StringBuilder sql = new StringBuilder();
        sql.append(" UPDATE ");
        sql.append(sqlModule.getTableName());
        sql.append(" SET ");

        sql.append(" DELETE_FLAG = 1 ");
        sql.append(" WHERE ");
        sql.append(condition);

        return sql.toString();
    }

}
