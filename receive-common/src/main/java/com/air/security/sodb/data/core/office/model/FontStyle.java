package com.air.security.sodb.data.core.office.model;

import org.apache.poi.hssf.usermodel.HSSFFont;

/**
 * 字体样式
 *
 * <AUTHOR>
 */
public class FontStyle {

    /**
     * 字体颜色
     *
     * @see HSSFFont
     */
    private Short fontColor;

    /**
     * 字体加粗
     *
     * @see HSSFFont
     */
    private boolean boldBorder;

    /**
     * 字体名称
     */
    private String fontStyleName;

    /**
     * 字体大小
     */
    private Short fontSize;

    /**
     * @return the fontColor
     */
    public Short getFontColor() {
        return fontColor;
    }

    /**
     * @param fontColor the fontColor to set
     */
    public void setFontColor(Short fontColor) {
        this.fontColor = fontColor;
    }

    /**
     * @return the boldBorder
     */
    public boolean isBoldBorder() {
        return boldBorder;
    }

    /**
     * @param boldBorder the boldBorder to set
     */
    public void setBoldBorder(boolean boldBorder) {
        this.boldBorder = boldBorder;
    }

    /**
     * @return the fontStyleName
     */
    public String getFontStyleName() {
        return fontStyleName;
    }

    /**
     * @param fontStyleName the fontStyleName to set
     */
    public void setFontStyleName(String fontStyleName) {
        this.fontStyleName = fontStyleName;
    }

    /**
     * @return the fontSize
     */
    public Short getFontSize() {
        return fontSize;
    }

    /**
     * @param fontSize the fontSize to set
     */
    public void setFontSize(Short fontSize) {
        this.fontSize = fontSize;
    }

}
