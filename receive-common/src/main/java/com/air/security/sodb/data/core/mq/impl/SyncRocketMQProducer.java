package com.air.security.sodb.data.core.mq.impl;

import com.air.security.sodb.data.core.constant.GlobalCodeConstant;
import com.air.security.sodb.data.core.constant.MsgIdConstant;
import com.air.security.sodb.data.core.mq.AbstractMyProducer;
import com.air.security.sodb.data.core.util.HaLog;
import com.air.security.sodb.data.core.util.RocketMQProducerUtil;
import org.apache.rocketmq.client.producer.SendResult;
import org.apache.rocketmq.common.message.Message;
import org.slf4j.Logger;
import org.slf4j.LoggerFactory;

/**
 * RocketMQ同步消息生产者
 * <AUTHOR>
 *
 */
public class SyncRocketMQProducer extends AbstractMyProducer {

	private static final Logger log = LoggerFactory.getLogger(SyncRocketMQProducer.class);
	
	@Override
	public Object sendMsg(String topic, String message) {
		try {
			Message msg = new Message(topic, super.pollTag(), message.getBytes(GlobalCodeConstant.FILE_ENCODE));
			SendResult sendResult = RocketMQProducerUtil.getInstance().getProducer().send(msg);
			HaLog.info(log, MsgIdConstant.MS_INF_0001, "发送成功状态为》》》"+sendResult.getSendStatus()+",MsgId为》》》"+sendResult.getMsgId());
			return sendResult;
		} catch (Exception e) {
			HaLog.error(log, e, MsgIdConstant.MS_ERR_0001, "");
		}
		return null;
	}

	@Override
	public void putBussinessId(String bussinessId) {
		
	}

	@Override
	public String pollBussinessId() {
		return null;
	}

}
