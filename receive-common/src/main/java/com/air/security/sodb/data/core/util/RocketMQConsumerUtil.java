package com.air.security.sodb.data.core.util;


import com.air.security.sodb.data.core.constant.GlobalCodeConstant;
import org.apache.rocketmq.client.consumer.DefaultMQPushConsumer;
import org.apache.rocketmq.client.exception.MQClientException;
import org.apache.rocketmq.common.consumer.ConsumeFromWhere;
import org.slf4j.Logger;
import org.slf4j.LoggerFactory;

import java.util.Properties;

public class RocketMQConsumerUtil {

	private static final Logger log = LoggerFactory.getLogger(ResourceUtil.class);

	/**
	 * rocketmq配置文件
	 */
	private static Properties properties = ResourceUtil.getInstance().getLocalProperties(
			PropertyUtil.getProperty(GlobalCodeConstant.ROCKETMQ_CONSUMER_CONFIG_PATH_KEY), "rocketMQConsumer");


	private static RocketMQConsumerConfig config;
	
	public static DefaultMQPushConsumer getConsumer(String topic) throws MQClientException {
		DefaultMQPushConsumer consumer = new DefaultMQPushConsumer(getConfig().getGroupName(),true);
		consumer.setConsumerGroup(getConfig().getGroupName());
		consumer.setNamesrvAddr(getConfig().getNamesrvAddr());
		consumer.setConsumeFromWhere(ConsumeFromWhere.CONSUME_FROM_FIRST_OFFSET);
		consumer.subscribe(topic, "*");
		return consumer;
	}
	
	private static RocketMQConsumerConfig getConfig(){
		if (null == config) {
			synchronized (RocketMQConsumerUtil.class) {
				if (null == config) {
					config = new RocketMQConsumerUtil().new RocketMQConsumerConfig();
					config.setGroupName(properties.getProperty("group.name"));
					config.setNamesrvAddr(properties.getProperty("namesrv.addr"));

				}
			}
		}
		return config;
	}
	
	/**
	 * rocketMQ的配置类
	 * <AUTHOR>
	 *
	 */
	class RocketMQConsumerConfig {
		/**
		 * 分组名
		 */
		private String groupName;
		
		/**
		 * 服务连接
		 */
		private String namesrvAddr;
		
		public String getGroupName() {
			return groupName;
		}

		public void setGroupName(String groupName) {
			this.groupName = groupName;
		}

		public String getNamesrvAddr() {
			return namesrvAddr;
		}

		public void setNamesrvAddr(String namesrvAddr) {
			this.namesrvAddr = namesrvAddr;
		}

	}
	
}
