package com.air.security.sodb.data.core.util;

import com.air.security.sodb.data.core.constant.GlobalCodeConstant;
import org.apache.commons.codec.binary.Base64;
import org.slf4j.Logger;
import org.slf4j.LoggerFactory;

import javax.crypto.Cipher;
import javax.crypto.spec.SecretKeySpec;
import java.security.MessageDigest;
import java.security.NoSuchAlgorithmException;

/**
 * AES加密解密
 * <AUTHOR>
public class HaAesCryptUtil {
    private static final Logger log = LoggerFactory.getLogger(HaAesCryptUtil.class);

    /**
     * 字符串加密
     *
     * @param content 加密的内容
     * @param key     使用加密的key
     * @return
     */
    public static String crypt(String content, String key) {
        try {
            Cipher cipher = Cipher.getInstance("AES");
            byte[] raw = getKey(128, key);
            SecretKeySpec skeySpec = new SecretKeySpec(raw, "AES");
            cipher.init(Cipher.ENCRYPT_MODE, skeySpec);
            byte[] encrypted = cipher.doFinal(content.getBytes(GlobalCodeConstant.FILE_ENCODE));
            return new String(Base64.encodeBase64(encrypted), GlobalCodeConstant.FILE_ENCODE);
        } catch (Exception e) {
            HaLog.error(log, e, "给字符串加密失败");
            throw new RuntimeException(e);
        }
    }

    /**
     * 解密字符串
     *
     * @param content
     * @param key
     * @return
     */
    public static String decrypt(String content, String key) {
        try {
            Cipher cipher = Cipher.getInstance("AES");
            byte[] raw = getKey(128, key);
            SecretKeySpec skeySpec = new SecretKeySpec(raw, "AES");
            cipher.init(Cipher.DECRYPT_MODE, skeySpec);
            byte[] encrypt = Base64.decodeBase64(content.getBytes(GlobalCodeConstant.FILE_ENCODE));
            byte[] encrypted = cipher.doFinal(encrypt);
            return new String(encrypted, GlobalCodeConstant.FILE_ENCODE);
        } catch (Exception e) {
            HaLog.error(log, e, "解密字符串失败");
            throw new RuntimeException(e);
        }
    }

    /**
     * 得到KEYBYTE
     *
     * @param keySize
     * @param key
     * @return
     * @throws NoSuchAlgorithmException
     */
    private static byte[] getKey(int keySize, String key) throws NoSuchAlgorithmException {
        int blockSize = keySize / Integer.valueOf(8);
        MessageDigest md = keySize == Integer.valueOf(128) ?
                MessageDigest.getInstance("SHA-1") : MessageDigest.getInstance("SHA-" + keySize);
        byte[] rawKey = md.digest(key.getBytes());
        byte[] keyByte = new byte[blockSize];
        int len = rawKey.length;
        for (int i = 0; i < blockSize; i++) {
            if (i < len) {
                keyByte[i] = rawKey[i];
            } else {
                keyByte[i] = 0;
            }
        }
        return keyByte;
    }

    public static void main(String[] args) {
        int len = 10;
        for (int i = 0; i < len; i++) {
            String key = "psgr2956dceacab44ecc82373d1ed5f923bd";
            String encrypted = "8jjlKTszpCsIsv7tEl2BuzZEiCBvRwaWVX5NKJED6N0=";
            HaLog.info(log, "key=:-----------" + key);
            HaLog.info(log, "encrypted=:-----" + encrypted);
            HaLog.info(log, "decrypt content=:-------" + HaAesCryptUtil.decrypt(encrypted, key));
        }
    }
}
