package com.air.security.sodb.data.core.util;

import java.text.ParseException;
import java.text.SimpleDateFormat;
import java.util.ArrayList;
import java.util.Calendar;
import java.util.Date;
import java.util.List;

/**
 * @Description: 时间工具类
 * @author: zhangc
 * @Date: 2018年11月26日
 */
public class DateTimeUtil {

    /**
     * 获取下一时间段的时间
     *
     * @param timeCompany
     * @param next
     * @return
     */
    public static Date getNextDate(int timeCompany, int next) {

        Calendar now = Calendar.getInstance();
        now.add(timeCompany, next);
        return now.getTime();
    }

    /**
     * 获取某一时间下一时间段的时间
     *
     * @param timeCompany
     * @param next
     * @return
     */
    public static Date getNextDate(int timeCompany, int next, Date date) {

        Calendar calendar = Calendar.getInstance();
        calendar.setTime(date);
        calendar.add(timeCompany, next);
        return calendar.getTime();
    }

    /**
     * 获取当前时间时间戳
     *
     * @param pattern
     * @return
     */
    public static String getCurrentTimestampStr(String pattern) {
        SimpleDateFormat sdf = new SimpleDateFormat(pattern);
        return sdf.format(new Date());
    }

    /**
     * 获取一个时间段内的所有日期
     *
     * @param start
     * @param end
     * @param pattern
     * @return
     */
    public static List<String> datePart(Date start, Date end, String pattern) {
        List<String> list = new ArrayList<String>();
        long s = start.getTime();
        long e = end.getTime();
        Long oneDay = 1000 * 60 * 60 * 24L;
        SimpleDateFormat sdf = new SimpleDateFormat(pattern);
        while (s <= e) {
            start = new Date(s);
            list.add(sdf.format(start));
            s += oneDay;
        }
        return list;
    }

    /**
     * string转date
     *
     * @param dateString
     * @param pattern
     * @return
     * @throws ParseException
     */
    public static Date getDate(String dateString, String pattern) throws ParseException {
        SimpleDateFormat sdf = new SimpleDateFormat(pattern);
        return sdf.parse(dateString);
    }

    /**
     * date转string
     *
     * @param date
     * @param pattern
     * @return
     */
    public static String getDateString(Date date, String pattern) {
        if (date == null) {
            return null;
        }
        SimpleDateFormat sdf = new SimpleDateFormat(pattern);
        return sdf.format(date);
    }

    /**
     * 时间格式转换
     *
     * @param dateStr
     * @param sourcePattern
     * @param targetPattern
     * @return
     * @throws ParseException
     */
    public static String parseDateStr(String dateStr, String sourcePattern, String targetPattern) throws ParseException {
        SimpleDateFormat sourceSdf = new SimpleDateFormat(sourcePattern);
        SimpleDateFormat targetSdf = new SimpleDateFormat(targetPattern);
        Date date = sourceSdf.parse(dateStr);
        return targetSdf.format(date);
    }

    /**
     * @return
     */
    public static String getCurrentYear() {
        return String.valueOf(Calendar.getInstance().get(Calendar.YEAR));
    }

    /**
     * @return
     */
    public static String getCurrentMonth() {
        return String.valueOf(Calendar.getInstance().get(Calendar.MONTH) + 1);
    }

    public static void main(String[] args) {
        String dateStr = "";
        String sourcePattern1 = "yyyy-MM-dd HH:mm:ss";
        String sourcePattern2 = "yyyyMMdd'T'HHmmss";
        String targetPattern = "yyyyMMddHHmmss";
        try {
            System.out.println(parseDateStr(dateStr, sourcePattern1, targetPattern));
        } catch (ParseException e) {
            try {
                System.out.println(parseDateStr(dateStr, sourcePattern2, targetPattern));
            } catch (ParseException e1) {
                e1.printStackTrace();
            }
        }
    }

}
