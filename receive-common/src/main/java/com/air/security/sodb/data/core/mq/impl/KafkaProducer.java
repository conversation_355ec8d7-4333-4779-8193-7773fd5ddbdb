package com.air.security.sodb.data.core.mq.impl;


import com.air.security.sodb.data.core.mq.AbstractMyProducer;
import com.air.security.sodb.data.core.util.KafkaProducerUtil;

public class KafkaProducer extends AbstractMyProducer {

	@Override
	public Object sendMsg(String topic, String message) {
		KafkaProducerUtil.send(topic, message);
		return true;
	}

	@Override
	public void putBussinessId(String bussinessId) {
		
	}

	@Override
	public String pollBussinessId() {
		return null;
	}

}
