package com.air.security.sodb.data.core.constant;

/**
 * 业务常量类
 *
 * <AUTHOR>
 */
public final class MsgIdConstant {

    /**
     * {0} 开始。 <BR>
     * 说明：{0}:处理业务名
     */
    public final static String MS_INF_0001 = "MS.INF.0001";

    /**
     * {0} 结束。 <BR>
     * 说明：{0}:处理业务名
     */
    public final static String MS_INF_0002 = "MS.INF.0002";

    /**
     * {0} 接收消息：{1} <BR>
     * 说明：{0}:系统名称，{1}:消息内容
     */
    public final static String MS_INF_0003 = "MS.INF.0003";

    /**
     * {0} 发布消息：{1} <BR>
     * 说明：{0}:系统名称，{1}:消息内容
     */
    public final static String MS_INF_0004 = "MS.INF.0004";

    /**
     * 正常。 <BR>
     */
    public static final String MS_INF_0005 = "MS.INF.0005";

    /**
     * 数据同步处理总件数：{0}、成功件数：{1}、失败件数：{2}。 <BR>
     */
    public static final String MS_INF_0006 = "MS.INF.0006";

    /**
     * 实时数据成功迁移到历史数据库表。数据库表：{0}、条数：{1}。<BR>
     */
    public static final String MS_INF_0007 = "MS.INF.0007";

    /**
     * {0}操作sql参数：{1}<BR>
     */
    public static final String MS_INF_0008 = "MS.INF.0008";

    /**
     * {0}运行信息：{1}
     */
    public static final String MS_INF_0009 = "MS.INF.0009";

    /**
     * eventType: {0} , SQL: {1}  , 执行结果：{2}。
     */
    public static final String MS_INF_0010 = "MS.INF.0010";

    /**
     * eventType: {0} , SqlList数量: {1} 。
     */
    public static final String MS_INF_0011 = "MS.INF.0011";

    /**
     * 发生系统异常。
     */
    public final static String MS_ERR_0001 = "MS.ERR.0001";

    /**
     * 数据库操作时，发生业务异常。 <BR>
     */
    public final static String MS_ERR_0002 = "MS.ERR.0002";

    /**
     * {0} 新增入库失败。数据主键：{1}，结果：{2} <BR>
     * 说明：{0}:处理业务名，{1}：数据主键，{2}：数据入库结果
     */
    public final static String MS_ERR_0003 = "MS.ERR.0003";

    /**
     * {0} 更新入库失败。数据主键：{1}，结果：{2} <BR>
     * 说明：{0}:处理业务名，{1}：数据主键，{2}：数据入库结果
     */
    public final static String MS_ERR_0004 = "MS.ERR.0004";

    /**
     * {0} 删除失败。数据主键：{1}，结果：{2} <BR>
     * 说明：{0}:处理业务名，{1}：数据主键，{2}：数据入库结果
     */
    public final static String MS_ERR_0005 = "MS.ERR.0005";

    /**
     * {0} 数据库操作失败：数据主键：{1}。 <BR>
     * 说明：{0}:处理业务名，{1}:业务主键
     */
    public final static String MS_ERR_0006 = "MS.ERR.0006";

    /**
     * {0} 在相同的开始日期、结束日期、出发地、目的地、航班号的情况下， 存在多条相同的长期计划。数据主键：{1}<BR>
     * 说明：{0}:处理业务名，{1}:业务主键
     */
    public final static String MS_ERR_0007 = "MS.ERR.0007";

    /**
     * {0} 接口传入参数错误.<BR>
     * 说明：{0}:处理业务名
     */
    public final static String MS_ERR_0008 = "MS.ERR.0008";

    /**
     * 加密解密异常.加密对象：{0}<BR>
     */
    public final static String MS_ERR_0009 = "MS.ERR.0009";

    /**
     * TLQ消息异常：{0}<BR>
     */
    public final static String MS_ERR_0010 = "MS.ERR.0010";

    /**
     * 获取{0}信息失败，返回数据：{1}
     */
    public static final String MS_ERR_0011 = "MS.ERR.0011";

    /**
     * 未找到匹配的eventType
     */
    public static final String MS_ERR_0012 = "MS.ERR.0012";

    /**
     * {0} Json格式消息转换为的JavaBean对象为空。<BR>
     */
    public final static String MS_WAR_0001 = "MS.WAR.0001";

    /**
     * {0} 获取对象为空。<BR>
     */
    public final static String MS_WAR_0002 = "MS.WAR.0002";

    /**
     * {0} 配置格式不正确。<BR>
     */
    public final static String MS_WAR_0003 = "MS.WAR.0003";

    /**
     * {0}数据库表查询结果不存在。查询条件为 {1}<BR>
     */
    public final static String MS_WAR_0004 = "MS.WAR.0004";

    /**
     * WS服务用户鉴权失败，消息发送者：{0}，验证码：{1}<BR>
     */
    public final static String MS_WAR_0005 = "MS.WAR.0005";

    /**
     * {0}返回结果数据出错:{1}<BR>
     */
    public final static String MS_WAR_0006 = "MS.WAR.0006";

    /**
     * 发生预期外业务错误。<BR>
     */
    public final static String MS_WAR_0007 = "MS.WAR.0007";

    /**
     * 根据数据的查询条件，数据不存在。<BR>
     */
    public final static String MS_WAR_0008 = "MS.WAR.0008";

    /**
     * 用户登录失败。<BR>
     */
    public static final String MS_WAR_0009 = "MS.WAR.0009";

    /**
     * 输入数据查询页数小于数据库数据的总页数。<BR>
     */
    public static final String MS_WAR_0010 = "MS.WAR.0010";

    /**
     * 用户名或密码为空。<BR>
     */
    public static final String MS_WAR_0011 = "MS.WAR.0011";

    /**
     * 用户名或密码错误。<BR>
     */
    public static final String MS_WAR_0012 = "MS.WAR.0012";

    /**
     * 缺少参数或参数校验失败。<BR>
     */
    public static final String MS_WAR_0013 = "MS.WAR.0013";

    /**
     * {0}存在关联数据，无法删除。数据主键：{1}。<BR>
     */
    public static final String MS_WAR_0014 = "MS.WAR.0014";

    /**
     * {0}业务数据不整合。查询条件：{1}。<BR>
     */
    public static final String MS_WAR_0015 = "MS.WAR.0015";

    /**
     * 您没有请求消息的权限！
     */
    public static final String MS_WAR_0016 = "MS.WAR.0016";

    /**
     * 您与上次请求消息时间相隔太短
     */
    public static final String MS_WAR_0017 = "MS.WAR.0017";

    /**
     * 您没有请求该消息的权限!
     */
    public static final String MS_WAR_0018 = "MS.WAR.0018";

}
