package com.air.security.sodb.data.core.config;

import java.util.List;
import java.util.Map;

/**
 * @Description: 消息格式化处理的配置
 * @author: zhangc
 * @Date: 2018年12月26日
 */
public class FormatHandleSaveConfig {

    /**
     * 消息类型
     */
    private String msgType;

    /**
     * 消息类型名称
     */
    private String msgTypeName;

    /**
     * 事件类型
     */
    private String eventType;

    /**
     * 事件类型名称
     */
    private String eventTypeName;

    /**
     * 消息主题
     */
    private String topic;

    /**
     * 表名称
     */
    private String table;

    /**
     * serviceName
     */
    private String serviceName;

    /**
     * sqlSession名称
     */
    private String session;

    /**
     * 是否为列表数据
     */
    private boolean listFlag;

    /**
     * 删除操作的依据key
     */
    private String deleteKey;

    /**
     * 删除操作的依据字段
     */
    private String deleteColumn;

    /**
     * 映射
     */
    private Map<String, List<String>> mapping;

    /**
     * 从表信息
     */
    private List<FormatHandleSaveFollowConfig> child;

    /**
     * @return 消息类型
     */
    public String getMsgType() {
        return msgType;
    }

    /**
     * @param 消息类型
     */
    public void setMsgType(String msgType) {
        this.msgType = msgType;
    }

    /**
     * @return 消息类型名称
     */
    public String getMsgTypeName() {
        return msgTypeName;
    }

    /**
     * @param 消息类型名称
     */
    public void setMsgTypeName(String msgTypeName) {
        this.msgTypeName = msgTypeName;
    }

    /**
     * @return 事件类型
     */
    public String getEventType() {
        return eventType;
    }

    /**
     * @param 事件类型
     */
    public void setEventType(String eventType) {
        this.eventType = eventType;
    }

    /**
     * @return 事件类型名称
     */
    public String getEventTypeName() {
        return eventTypeName;
    }

    /**
     * @param 事件类型名称
     */
    public void setEventTypeName(String eventTypeName) {
        this.eventTypeName = eventTypeName;
    }

    /**
     * @return 消息主题
     */
    public String getTopic() {
        return topic;
    }

    /**
     * @param 消息主题
     */
    public void setTopic(String topic) {
        this.topic = topic;
    }

    /**
     * @return 表名称
     */
    public String getTable() {
        return table;
    }

    /**
     * @param 表名称
     */
    public void setTable(String table) {
        this.table = table;
    }

    /**
     * @return serviceName
     */
    public String getServiceName() {
        return serviceName;
    }

    /**
     * @param serviceName
     */
    public void setServiceName(String serviceName) {
        this.serviceName = serviceName;
    }

    /**
     * @return sqlSession
     */
    public String getSession() {
        return session;
    }

    /**
     * @param sqlSession
     */
    public void setSession(String session) {
        this.session = session;
    }

    /**
     * @return 是否为列表数据
     */
    public boolean isListFlag() {
        return listFlag;
    }

    /**
     * @param 是否为列表数据
     */
    public void setListFlag(boolean listFlag) {
        this.listFlag = listFlag;
    }

    /**
     * @return 删除操作的依据字key
     */
    public String getDeleteKey() {
        return deleteKey;
    }

    /**
     * @param 删除操作的依据key
     */
    public void setDeleteKey(String deleteKey) {
        this.deleteKey = deleteKey;
    }

    /**
     * @return 删除操作的依据字段
     */
    public String getDeleteColumn() {
        return deleteColumn;
    }

    /**
     * @param 删除操作的依据字段
     */
    public void setDeleteColumn(String deleteColumn) {
        this.deleteColumn = deleteColumn;
    }

    /**
     * @return 映射
     */
    public Map<String, List<String>> getMapping() {
        return mapping;
    }

    /**
     * @param 映射
     */
    public void setMapping(Map<String, List<String>> mapping) {
        this.mapping = mapping;
    }

    /**
     * @return 从表信息
     */
    public List<FormatHandleSaveFollowConfig> getChild() {
        return child;
    }

    /**
     * @param 从表信息
     */
    public void setChild(List<FormatHandleSaveFollowConfig> child) {
        this.child = child;
    }

}
