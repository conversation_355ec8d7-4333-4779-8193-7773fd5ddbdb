package com.air.security.sodb.data.core.util;

import com.air.security.sodb.data.core.constant.DtsConstant;
import org.slf4j.Logger;
import org.slf4j.LoggerFactory;

import javax.crypto.Cipher;
import javax.crypto.spec.SecretKeySpec;
import java.util.Base64;

/**
 * AES加密解密
 * <AUTHOR>
public class DecryptUtil {

    private static final Logger log = LoggerFactory.getLogger(DecryptUtil.class);


    /**
     * 加密
     * @param sSrc
     * @param sKey
     * @return
     * @throws Exception
     */
    public static String encrypt(String sSrc, String sKey) throws Exception {
        if (sKey == null) {
            return null;
        }
        // 判断Key是否为16位
        int len = 16;
        if (sKey.length() != len) {
            return null;
        }
        byte[] raw = sKey.getBytes("utf-8");
        SecretKeySpec skeySpec = new SecretKeySpec(raw, "AES");
        //"算法/模式/补码方式"
        Cipher cipher = Cipher.getInstance("AES/ECB/PKCS5Padding");
        cipher.init(Cipher.ENCRYPT_MODE, skeySpec);
        byte[] encrypted = cipher.doFinal(sSrc.getBytes("utf-8"));
        //此处使用BASE64做转码功能，同时能起到2次加密的作用。
        return Base64.getEncoder().encodeToString(encrypted);
    }

    /**
     * 解密
     * @param sSrc
     * @param sKey
     * @return
     */
    public static String decrypt(String sSrc, String sKey) {
        try {
            // 判断Key是否正确
            if (sKey == null) {
                return null;
            }
            // 判断Key是否为16位

            int len = 16;
            if (sKey.length() != len) {
                return null;
            }
            byte[] raw = sKey.getBytes("utf-8");
            SecretKeySpec skeySpec = new SecretKeySpec(raw, "AES");
            Cipher cipher = Cipher.getInstance("AES/ECB/PKCS5Padding");
            cipher.init(Cipher.DECRYPT_MODE, skeySpec);
            //先用base64解密
            byte[] encrypted1 = Base64.getDecoder().decode(sSrc);
            try {
                byte[] original = cipher.doFinal(encrypted1);
                String originalString = new String(original, "utf-8");
                return originalString;
            } catch (Exception e) {
                HaLog.error(log, "解密失败", e);
            }
        } catch (Exception e) {
            HaLog.error(log, "解密失败", e);
        }
        return null;
    }

    public static void main(String[] args) throws Exception {
        /*
         * 此处使用AES-128-ECB加密模式，key需要为16位。
         */
        // 需要加密的字串
        String cSrc = "ass0420190429111";
        System.out.println(cSrc);
        // 加密
        String enString = DecryptUtil.encrypt(cSrc, DtsConstant.DECRYPT_KEY);
        System.out.println("加密后的字串是：" + enString);

        // 解密
        String deString = DecryptUtil.decrypt("iFTxknOH06PGJzsDRHYIaQ==", DtsConstant.DECRYPT_KEY);
        System.out.println("解密后的字串是：" + deString);
    }
}
