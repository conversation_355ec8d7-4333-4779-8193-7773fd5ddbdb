package com.air.security.sodb.data.core.config;

import java.util.List;

import com.alibaba.fastjson.JSONObject;

/**
 * @Description: 创建sql的模型
 * @author: zhangc
 * @Date: 2019年3月10日
 */
public class SqlModule {

    private String tableName;

    private List<String> columnList;

    private List<String> keyList;

    private List<String> typeList;

    private JSONObject dataJson;

    public void put(String tableName, List<String> columnList, List<String> keyList, List<String> typeList,
                    JSONObject dataJson) {
        this.tableName = tableName;
        this.columnList = columnList;
        this.keyList = keyList;
        this.typeList = typeList;
        this.dataJson = dataJson;
    }

    /**
     * @return the tableName
     */
    public String getTableName() {
        return tableName;
    }

    /**
     * @param tableName the tableName to set
     */
    public void setTableName(String tableName) {
        this.tableName = tableName;
    }

    /**
     * @return the columnList
     */
    public List<String> getColumnList() {
        return columnList;
    }

    /**
     * @param columnList the columnList to set
     */
    public void setColumnList(List<String> columnList) {
        this.columnList = columnList;
    }

    /**
     * @return the dataList
     */
    public List<String> getKeyList() {
        return keyList;
    }

    /**
     * @param dataList the dataList to set
     */
    public void setKeyList(List<String> keyList) {
        this.keyList = keyList;
    }

    /**
     * @return the typeList
     */
    public List<String> getTypeList() {
        return typeList;
    }

    /**
     * @param typeList the typeList to set
     */
    public void setTypeList(List<String> typeList) {
        this.typeList = typeList;
    }

    /**
     * @return the dataJson
     */
    public JSONObject getDataJson() {
        return dataJson;
    }

    /**
     * @param dataJson the dataJson to set
     */
    public void setDataJson(JSONObject dataJson) {
        this.dataJson = dataJson;
    }

}
