package com.air.security.sodb.data.core.util;

import com.air.security.sodb.data.core.constant.GlobalCodeConstant;
import org.apache.kafka.clients.producer.KafkaProducer;

import java.util.Properties;

/**
 * @Description: kafka生产者示例化工厂
 * @author: zhangc
 * @Date: 2018年12月10日
 */
public class KafkaProducerInitialize {

    /**
     * kafka配置文件
     */
    private static Properties properties = ResourceUtil.getInstance().getLocalProperties(
            PropertyUtil.getProperty(GlobalCodeConstant.KAFKA_PRODUCER_CONFIG_PATH_KEY), "kafkaProducer");

    private static KafkaProducerInitialize instance;

    private static KafkaProducer<Integer, String> producer;

    /**
     * 获取KafkaProducerInitialize实例
     *
     * @return KafkaProducerInitialize
     */
    public static KafkaProducerInitialize getInstance() {
        if (null == instance) {
            init();
            instance = new KafkaProducerInitialize();
        }
        return instance;
    }

    /**
     * KafkaProducer初始化
     */
    private static void init() {
        if (null == producer) {
            producer = new KafkaProducer<>(properties);
        }
    }

    /**
     * 获取producer
     *
     * @return
     */
    public KafkaProducer<Integer, String> getProducer() {
        return producer;
    }

    /**
     * 销毁
     */
    public void clear() {
        if (null != producer) {
            producer.close();
        }
        if (null != instance) {
            instance = null;
        }
    }
}
