package com.air.security.sodb.data.core.util;

import java.util.HashMap;

/**
 * <AUTHOR>
 */
public class MapUtil {

    private static final int MAXIMUM_CAPACITY = 1 << 30;

    /**
     * 获取map的capacity
     *
     * @param size map长度
     * @return
     */
    public static int getMapCapacity(int size) {
        int offset = 1;
        for (; ; ) {
            offset = offset << 1;
            if (offset * 0.75 >= size) {
                // 计算threshold值，map长度小于等于threshold值
                return offset;
            }
        }
    }

    /**
     * 如果是小数, 则增一
     *
     * @param d
     * @return
     */
    private static int getUpNum(double d) {
        return d == (int) d ? (int) d : ((int) d) + 1;
    }

    /**
     * 获取map的capacity
     *
     * @param size
     * @return
     */
    public static int getMapCapacity0(int size) {
        int offset = getUpNum(size / 0.75);
        return tableSizeFor(offset);
    }

    static final int tableSizeFor(int cap) {
        int n = cap - 1;
        n |= n >>> 1;
        n |= n >>> 2;
        n |= n >>> 4;
        n |= n >>> 8;
        n |= n >>> 16;
        return (n < 0) ? 1 : (n >= MAXIMUM_CAPACITY) ? MAXIMUM_CAPACITY : n + 1;
    }

    /**
     * 得到一个map
     *
     * @param size map长度
     * @return
     */
    public static <K, V> HashMap<K, V> getHashMap(int size) {
        int capacity = getMapCapacity0(size);
        HashMap<K, V> map = new HashMap<>(capacity);
        return map;
    }

    /**
     * 得到一个map并赋值
     *
     * @param key
     * @param value
     * @param size  map长度
     * @return
     */
    public static <K, V> HashMap<K, V> getHashMap(K key, V value, int size) {
        HashMap<K, V> map = getHashMap(size);
        map.put(key, value);
        return map;
    }

    /**
     * 得到一个长度为1的map并赋值
     *
     * @param key
     * @param value
     * @return
     */
    public static <K, V> HashMap<K, V> getHashMap(K key, V value) {
        return getHashMap(key, value, 1);
    }

}
