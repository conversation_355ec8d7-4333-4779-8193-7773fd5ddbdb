package com.air.security.sodb.data.core.base;


import com.air.security.sodb.data.core.util.PropertyUtil;
import org.apache.commons.lang3.StringUtils;

import com.air.security.sodb.data.core.constant.CodeConstant;
import com.air.security.sodb.data.core.constant.MsgIdConstant;

/**
 * API调用返回结果种类
 * <AUTHOR>
public enum ReceiveErrorType {

    /**
     * 处理结果：正常
     */
    SUCCESS(CodeConstant.BUSINESS_SUCCESS, PropertyUtil.getProperty(MsgIdConstant.MS_INF_0005)),

    /**
     * 业务异常
     */
    BUSINESS_ERROR(CodeConstant.BUSINESS_ERROR, PropertyUtil.getProperty(MsgIdConstant.MS_WAR_0007)),

    /**
     * 业务异常（暂无数据）
     */
    BUSINESS_DATA_NO_EXIST(CodeConstant.BUSINESS_DATA_NO_EXIST, PropertyUtil.getProperty(MsgIdConstant.MS_WAR_0008)),

    /**
     * 业务异常（登录失败）
     */
    BUSINESS_LOGIN_FAILED(CodeConstant.BUSINESS_LOGIN_FAILED, PropertyUtil.getProperty(MsgIdConstant.MS_WAR_0009)),

    /**
     * 业务异常（用户名或密码为空）
     */
    BUSINESS_LOGIN_PW_OR_USNM_NULL(CodeConstant.BUSINESS_LOGIN_PW_OR_USNM_NULL, PropertyUtil.getProperty(MsgIdConstant.MS_WAR_0011)),

    /**
     * 业务异常（用户名或密码错误）
     */
    BUSINESS_LOGIN_PW_OR_USNM_NOT_EQUALS(CodeConstant.BUSINESS_LOGIN_PW_OR_USNM_NOT_EQUALS, PropertyUtil.getProperty(MsgIdConstant.MS_WAR_0012)),


    /**
     * 业务异常（用户登陆鉴权未通过）
     */
    BUSINESS_LOGIN_AUTH_FAILED(CodeConstant.BUSINESS_LOGIN_AUTH_FAILED, PropertyUtil.getProperty(MsgIdConstant.MS_WAR_0018)),

    /**
     * 业务异常（请求页码超出）
     */
    BUSINESS_DATA_ABOVE_PAGEMAX(CodeConstant.BUSINESS_DATA_ABOVE_PAGEMAX, PropertyUtil.getProperty(MsgIdConstant.MS_WAR_0010)),

    /**
     * 业务异常（缺少参数或参数验证失败）
     */
    BUSINESS_LACK_OR_ERROR_PARAMS(CodeConstant.BUSINESS_LACK_OR_ERROR_PARAMS, PropertyUtil.getProperty(MsgIdConstant.MS_WAR_0013)),

    /**
     * 系统错误
     */
    SYSTEM_ERROR(CodeConstant.SYSTEM_ERROR, PropertyUtil.getProperty(MsgIdConstant.MS_ERR_0001)),

    /**
     * 系统错误
     */
    NOT_FOUND_EVENT_TYPE(CodeConstant.NOT_FOUND_EVENT_TYPE, PropertyUtil.getProperty(MsgIdConstant.MS_ERR_0012));

    /**
     * 代码
     */
    private final String code;

    /**
     * 消息
     */
    private final String message;

    /**
     * 构造函数
     *
     * @param code    代码
     * @param message 消息
     */
    ReceiveErrorType(String code, String message) {
        this.code = code;
        this.message = message;
    }

    /**
     * @return 代码
     */
    public String getCode() {
        return this.code;
    }

    /**
     * @return 消息
     */
    public String getMessage() {
        if (!StringUtils.isEmpty(message)) {
            return message;
        } else {
            return null;
        }
    }
}
