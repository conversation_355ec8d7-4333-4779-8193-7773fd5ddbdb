package com.air.security.sodb.data.core.constant;

/**
 * 数据仓库:数据字典定义常量类
 * <AUTHOR>
public final class CodeConstant {

    /**
     * 删除状态：未删除
     */
    public static final Character IS_DELETE_NO = '0';

    /**
     * 删除状态：已删除
     */
    public static final Character IS_DELETE_YES = '1';

    /**
     * 否
     */
    public static final int NO = 0;

    /**
     * 是
     */
    public static final int YES = 1;

    /**
     * 处理结果：业务正常
     */
    public static final String BUSINESS_SUCCESS = "0";

    /**
     * 处理结果：业务异常
     */
    public static final String BUSINESS_ERROR = "1";

    /**
     * 处理结果：业务异常（暂无数据）
     */
    public static final String BUSINESS_DATA_NO_EXIST = "2";

    /**
     * 处理结果：业务异常（登录失败）
     */
    public static final String BUSINESS_LOGIN_FAILED = "3";

    /**
     * 处理结果：业务异常（用户名或密码为空）
     */
    public static final String BUSINESS_LOGIN_PW_OR_USNM_NULL = "4";

    /**
     * 处理结果： 业务异常（用户名或密码错误）
     */
    public static final String BUSINESS_LOGIN_PW_OR_USNM_NOT_EQUALS = "5";

    /**
     * 处理结果：业务异常（请求页码超出）
     */
    public static final String BUSINESS_DATA_ABOVE_PAGEMAX = "6";

    /**
     * 处理结果：业务异常（缺少参数或参数验证失败）
     */
    public static final String BUSINESS_LACK_OR_ERROR_PARAMS = "7";

    /**
     * 处理结果：业务异常（用户登陆鉴权未通过）
     */
    public static final String BUSINESS_LOGIN_AUTH_FAILED = "8";

    /**
     * 处理结果：系统异常
     */
    public static final String SYSTEM_ERROR = "9";


    /**
     * 处理结果：未找到匹配的eventType
     */
    public static final String NOT_FOUND_EVENT_TYPE = "12";

    /**
     * 统一用户信息加密密钥
     */
    public static final String IDCARD_AES_CRYPT_KEY = "Sodbdts";

    /**
     * 数据字典 class coding
     *
     * <AUTHOR>
     */
    public static interface ClassCoding {

        /**
         * 操作对象
         */
        String SYSTEM_OP_OBJECT = "systemOpObject";

        /**
         * 操作类型
         */
        String SYSTEM_OP_TYPE = "systemOpType";

        /**
         * 操作结果
         */
        String SYSTEM_OP_RESULT = "systemOpResult";

    }

}
