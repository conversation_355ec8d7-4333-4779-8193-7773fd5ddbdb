package com.air.security.sodb.data.core.service;

import com.air.security.sodb.data.core.base.ReceiveErrorType;

import java.io.Serializable;

/**
 * 服务层输出结果类
 * <AUTHOR>
 * @param <T> 返回值类型
 */
public class ServiceOutDTO<T> implements Serializable {

    private static final long serialVersionUID = 1L;

    /**
     * 返回结果ID
     */
    private ReceiveErrorType returnCd;

    /**
     * 返回值
     */
    private T resut;

    /**
     * @return 返回结果ID
     */
    public ReceiveErrorType getReturnCd() {
        return returnCd;
    }

    /**
     * @param 返回结果ID
     */
    public void setReturnCd(ReceiveErrorType returnCd) {
        this.returnCd = returnCd;
    }

    /**
     * @return 返回值
     */
    public T getResut() {
        return resut;
    }

    /**
     * @param 返回值
     */
    public void setResut(T resut) {
        this.resut = resut;
    }

    @Override
    public String toString() {
        return "ServiceOutDTO{" +
                "returnCd=" + returnCd +
                ", resut=" + resut +
                '}';
    }
}
