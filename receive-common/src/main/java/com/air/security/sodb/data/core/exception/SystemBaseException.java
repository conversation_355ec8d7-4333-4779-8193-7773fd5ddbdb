package com.air.security.sodb.data.core.exception;

import com.air.security.sodb.data.core.constant.GlobalCodeConstant;

/**
 * @Description: 系统运行异常
 * @author: zhangc
 * @Date: 2018年12月5日
 */
public class SystemBaseException extends RuntimeException {

    private static final long serialVersionUID = -8680716796220938155L;

    public Integer getErrorCode() {
        return GlobalCodeConstant.BASE_ERROR_CODE;
    }

    public String getErrorMsg() {
        return GlobalCodeConstant.BASE_ERROR_CODE_NAME;
    }

    public SystemBaseException() {
        super();
    }

    public SystemBaseException(String msg, Throwable e) {
        super(msg, e);
    }

    public SystemBaseException(String msg) {
        super(msg);
    }

    public SystemBaseException(Throwable e) {
        super(e);
    }

}
