#\u6279\u91CF\u540C\u6B65\u8BBE\u5907
EQU_RULE=RuleRecvServiceImpl,cContainerbRuleSync,EQU_RULE,EQU
#\u589E\u52A0\u8BBE\u5907
EQU_ADD=RecvServiceImpl,cContainerEqu,EQU_ADD,EQU
#\u66F4\u65B0\u8BBE\u5907
EQU_UPDATE=RecvServiceImpl,cContainerEqu,EQU_UPDATE,EQU
#\u5220\u9664\u8BBE\u5907
EQU_DELETE=RecvServiceImpl,cContainerEqu,EQU_DELETE,EQU
#\u6279\u91CF\u540C\u6B65\u8BBE\u5907
EQU_SYNC=EquSyncRecvServiceImpl,cContainerEquSync,EQU_SYNC,EQU
#\u72B6\u6001\u53D8\u66F4
EQU_STATECHANGE=RecvServiceImpl,cContainerEqu,EQU_STATECHANGE,EQU_STATES
#\u62A5\u8B66\u4FE1\u606F
ALARM_TRIGGER=AlarmTriggerRecvServiceImpl,cContainerAlarm|cContainerVideoAlarm,ALARM_TRIGGER|W_VIDEO_ALARM_TRIGGER,ALARM|ALARM
#\u5904\u7F6E\u62A5\u8B66\u4FE1\u606F
ALARM_DISPOSE=RecvServiceImpl,cContainerAlarm,ALARM_DISPOSE,ALARM
ASS03=ASS03RecvServiceImpl,cSodbAlarmDispose,WIP_ALARM_DISPOSE,ALARM
ASS02=ASS02RecvServiceImpl,cSodbAlarmDispose,WIP_ALARM_DISPOSE,ALARM

EQU_REQUEST=RecvServiceImpl,cSodbEquReq,EQU_REQUEST,EQU
RULE_REQUEST=RecvServiceImpl,cSodbRuleReq,RULE_REQUEST,RULE
