#\u8BBE\u7F6E\u4E0A\u4E0B\u6587\u6839 \u9ED8\u8BA4 \u201C/\u201D
#server.servlet.path=/sodb-ams
#\u8BBE\u7F6E\u8BBF\u95EE\u7AEF\u53E3 \u9ED8\u8BA4\u201C8080\u201D
server.port=11021
#\u6587\u4EF6\u4E0A\u4F20\u5927\u5C0F\u914D\u7F6E
spring.http.multipart.maxFileSize=100Mb
spring.http.multipart.maxRequestSize=100Mb
spring.activiti.check-process-definitions=false
spring.autoconfigure.exclude=org.springframework.boot.autoconfigure.jdbc.DataSourceAutoConfiguration,org.springframework.boot.autoconfigure.jdbc.DataSourceTransactionManagerAutoConfiguration
db.type=mysql
#\u6570\u636E\u5E93\u8FDE\u63A5\u914D\u7F6E\uFF1Adts
datasource.driverClassName=com.mysql.jdbc.Driver
datasource.url=***************************************************************************************************************************
#datasource.url=********************************************
datasource.username=root
datasource.password=123456
datasource.initialSize=5
datasource.maxActive=300
datasource.maxIdle=10
datasource.minIdle=2
datasource.maxWait=60000
datasource.validationQuery=select VERSION()
datasource.testOnBorrow=true
datasource.testOnReturn=true
datasource.timeBetweenEvictionRunsMillis=60000
datasource.minEvictableIdleTimeMillis=180000
datasource.removeAbandoned=true
datasource.removeAbandonedTimeout=250
##############################################################################################
#\u6570\u636E\u5E93\u8FDE\u63A5\u914D\u7F6E\uFF1Ahabs
##############################################################################################
bs.datasource.driverClassName=com.mysql.jdbc.Driver
bs.datasource.url=***************************************************************************************************************************
bs.datasource.username=root
bs.datasource.password=123456
bs.datasource.initialSize=5
bs.datasource.maxActive=300
bs.datasource.maxIdle=1
bs.datasource.minIdle=2
bs.datasource.maxWait=60000
bs.datasource.validationQuery=select VERSION()
bs.datasource.testOnBorrow=true
bs.datasource.testOnReturn=true
bs.datasource.timeBetweenEvictionRunsMillis=60000
bs.datasource.minEvictableIdleTimeMillis=180000
bs.datasource.removeAbandoned=true
bs.datasource.removeAbandonedTimeout=250
##############################################################################################


spring.redis.host=***************
spring.redis.password=hasodb@2021A
spring.redis.port=6379
spring.redis.timeout=6000
spring.redis.connect-timeout=3000
spring.redis.lettuce.pool.max-active=1000
spring.redis.lettuce.pool.max-idle=100
spring.redis.lettuce.pool.min-idle=5