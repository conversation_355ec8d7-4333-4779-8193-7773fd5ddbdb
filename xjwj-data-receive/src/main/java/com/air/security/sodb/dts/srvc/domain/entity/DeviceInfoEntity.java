package com.air.security.sodb.dts.srvc.domain.entity;

import io.swagger.annotations.ApiModel;
import io.swagger.annotations.ApiModelProperty;
import lombok.Data;
import org.springframework.format.annotation.DateTimeFormat;

import javax.persistence.Column;
import javax.persistence.Id;
import javax.persistence.Table;
import java.util.Date;

@Data
@Table(name = "psip_device_info")
@ApiModel("设备信息实体对象（DeviceInfoEntity）")
public class DeviceInfoEntity {

    @Id
    @Column(name = "UUID")
    @ApiModelProperty(value = "UUID", required = true)
    private String uuid;

    @Column(name = "PARENT_ID")
    @ApiModelProperty(value = "父ID")
    private String parentId;

    @Column(name = "DEVICE_CATEGORY_CODE")
    @ApiModelProperty(value = "设备分类编码")
    private String deviceCategoryCode;

    @Column(name = "DEVICE_CATEGORY_NAME")
    @ApiModelProperty(value = "设备分类名称")
    private String deviceCategoryName;

    @Column(name = "DEVICE_NAME")
    @ApiModelProperty(value = "设备名称")
    private String deviceName;

    @Column(name = "DEVICE_CODE")
    @ApiModelProperty(value = "设备编号")
    private String deviceCode;

    @Column(name = "DEVICE_TYPE_CODE")
    @ApiModelProperty(value = "设备类型编码")
    private String deviceTypeCode;

    @Column(name = "DEVICE_TYPE_NAME")
    @ApiModelProperty(value = "设备类型名称")
    private String deviceTypeName;

    @Column(name = "DEVICE_IP")
    @ApiModelProperty(value = "设备IP")
    private String deviceIp;

    @Column(name = "DEVICE_STATE_CODE")
    @ApiModelProperty(value = "设备状态编码（ES01：在线；ES02：离线；ES03：故障）")
    private String deviceStateCode;

    @Column(name = "DEVICE_STATE_NAME")
    @ApiModelProperty(value = "设备状态名称")
    private String deviceStateName;

    @Column(name = "DEVICE_IO_STATE_CODE")
    @ApiModelProperty(value = "设备IO状态编码（0：打开；1：关闭）")
    private String deviceIoStateCode;

    @Column(name = "DEVICE_IO_STATE_NAME")
    @ApiModelProperty(value = "设备IO状态名称")
    private String deviceIoStateName;

    @Column(name = "DEVICE_DESCRIPTION")
    @ApiModelProperty(value = "设备描述")
    private String deviceDescription;

    @Column(name = "SYSTEM_CODE")
    @ApiModelProperty(value = "所属系统编码")
    private String systemCode;

    @Column(name = "SYSTEM_NAME")
    @ApiModelProperty(value = "所属系统名称")
    private String systemName;

    @Column(name = "REGION_ID")
    @ApiModelProperty(value = "目录ID")
    private String regionId;

    @Column(name = "CREATE_BY")
    @ApiModelProperty(value = "创建人")
    private String createBy;

    @Column(name = "CREATE_DATE")
    @DateTimeFormat(pattern = "yyyy-MM-dd HH:mm:ss")
    @ApiModelProperty(value = "创建时间")
    private Date createDate;

    @Column(name = "UPDATE_BY")
    @ApiModelProperty(value = "更新人")
    private String updateBy;

    @Column(name = "UPDATE_DATE")
    @DateTimeFormat(pattern = "yyyy-MM-dd HH:mm:ss")
    @ApiModelProperty(value = "更新时间")
    private Date updateDate;

}
