package com.air.security.sodb.dts.receive.service.alarm;

import com.air.security.sodb.data.core.base.Meta;
import com.air.security.sodb.data.core.constant.MsgIdConstant;
import com.air.security.sodb.data.core.exception.SystemBaseException;
import com.air.security.sodb.data.core.util.HaLog;
import com.air.security.sodb.dts.receive.service.AbstractMessageRecvService;
import com.alibaba.fastjson.JSON;
import com.alibaba.fastjson.JSONObject;
import org.slf4j.Logger;
import org.slf4j.LoggerFactory;
import org.springframework.stereotype.Service;

import java.text.SimpleDateFormat;
import java.util.Date;
import java.util.UUID;

/**
 * @Description:设备信息处理
 * <AUTHOR>
@Service("ASS03RecvServiceImpl")
public class ASS03RecvServiceImpl extends AbstractMessageRecvService {
    private static final Logger log = LoggerFactory.getLogger(ASS03RecvServiceImpl.class);

    @Override
    public void execute(Meta meta, String messageBody) throws SystemBaseException {
        HaLog.info(log, MsgIdConstant.MS_INF_0001, meta.getEventType());

        JSONObject json = JSON.parseObject(messageBody);
        JSONObject json1 = new JSONObject();
                // 获取到的json业务数据
        JSONObject jsonObject = new JSONObject();
        meta.setReceiver("WJ");
        meta.setSender("WIP");
        meta.setEventType("WIP_ALARM_DISPOSE");
        meta.setMsgType("ALARM");
        meta.setSequence(UUID.randomUUID().toString());
        SimpleDateFormat sdf = new SimpleDateFormat("yyyyMMddHHmmss");
        String currentTime = sdf.format(new Date());
        meta.setSendTime(currentTime);
        // 响应消息头
        jsonObject.put("meta", meta);
        json1.put("alarmSourceId", json.getString("alarmCode"));
        json1.put("alarmName", json.getString("alarmName"));
        json1.put("alarmNameCode", json.getString("alarmNameCode"));
        json1.put("alarmClassCode", json.getString("alarmClassCode"));
        json1.put("alarmClassName", json.getString("alarmClassName"));
        json1.put("alarmEquCode", json.getString("deviceCode"));
        json1.put("alarmEquName", json.getString("deviceName"));
        json1.put("areaCode", json.getString("perimeterAreaId"));
        json1.put("alarmLevelCode", json.getString("alarmLevelCode"));
        json1.put("alarmLevelName", json.getString("alarmLevelName"));
        json1.put("cardReaderCode", "");
        json1.put("cardReaderName", "");
        json1.put("alarmTime", json.getString("alarmTime"));
        json1.put("alarmDescribe", json.getString("dealContent"));
        json1.put("dealType", json.getString("dealType"));
        json1.put("updateTime", json.getString("dealTime"));
        json1.put("alarmStateCode", json.getString("alarmStateCode"));
        json1.put("alarmStateName", json.getString("alarmStateName"));
        json1.put("disposePersonId", json.getString("dealUserId"));
        json1.put("disposePersonName", json.getString("dealUserName"));
        json1.put("systemCode", json.getString("systemCode"));
        json1.put("systemName", json.getString("systemName"));
        // 响应消息体
        jsonObject.put("body", json1);

        // 转换为JSON格式，并发送消息到指定主题
        super.putSendMessage(jsonObject);
        HaLog.info(log, MsgIdConstant.MS_INF_0002, meta.getEventType());
    }
}
