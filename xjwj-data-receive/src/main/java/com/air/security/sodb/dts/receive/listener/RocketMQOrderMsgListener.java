package com.air.security.sodb.dts.receive.listener;

import com.air.security.sodb.data.core.base.Meta;
import com.air.security.sodb.data.core.constant.GlobalCodeConstant;
import com.air.security.sodb.data.core.constant.MsgIdConstant;
import com.air.security.sodb.data.core.mq.MyProducer;
import com.air.security.sodb.data.core.mq.impl.OrderRocketMQProducer;
import com.air.security.sodb.data.core.util.DataTransferLogRecordUtil;
import com.air.security.sodb.data.core.util.HaLog;
import com.air.security.sodb.data.core.util.RocketMQConsumerUtil;
import com.air.security.sodb.dts.receive.server.MqMessageRecvServer;
import com.air.security.sodb.dts.receive.server.MqMessageRecvServerImpl;
import com.alibaba.fastjson.JSON;
import com.alibaba.fastjson.JSONArray;
import com.alibaba.fastjson.JSONObject;
import org.apache.commons.lang3.StringUtils;
import org.apache.rocketmq.client.consumer.DefaultMQPushConsumer;
import org.apache.rocketmq.client.consumer.listener.ConsumeOrderlyContext;
import org.apache.rocketmq.client.consumer.listener.ConsumeOrderlyStatus;
import org.apache.rocketmq.client.consumer.listener.MessageListenerOrderly;
import org.apache.rocketmq.client.exception.MQClientException;
import org.apache.rocketmq.common.message.MessageExt;
import org.slf4j.Logger;
import org.slf4j.LoggerFactory;

import java.util.List;

/**
 * rocketmq的顺序消息监听
 * <AUTHOR>
 *
 */
public class RocketMQOrderMsgListener implements Runnable {

	private static final Logger log = LoggerFactory.getLogger(RocketMQOrderMsgListener.class);

	/**
	 * 消费者主题
	 */
	private String consumerTopic;

	/**
	 * RocketMQ消费者
	 */
	private DefaultMQPushConsumer consumer;

	/**
	 * 消费者组
	 */
	private String consumerGroup;

	/**
	 * RocketMQ生产者
	 */
	private MyProducer producer = new OrderRocketMQProducer();

	@Override
	public void run() {
		this.consumer.setConsumerGroup(this.consumerGroup);
		this.consumer.registerMessageListener(new MessageListenerOrderly() {

			@Override
			public ConsumeOrderlyStatus consumeMessage(List<MessageExt> msgs, ConsumeOrderlyContext context) {
				context.setAutoCommit(true);
				for (MessageExt msg : msgs) {
					try {
						String message = new String(msg.getBody(), "utf-8");
						JSONObject jsonObject = JSONObject.parseObject(message);

						if (jsonObject == null) {
							continue;
						}
						HaLog.info(log, MsgIdConstant.MS_INF_0001, "原始数据为》》》"+message+",MsgId为》》》"+msg.getMsgId());

						// 获取消息头标签
						Meta meta = JSON.parseObject(jsonObject.getString("meta"), Meta.class);
						if (null == meta) {
							continue;
						}

						// 记录数据交互日志
						DataTransferLogRecordUtil.record(meta, jsonObject, GlobalCodeConstant.LOG_TYPE_RECV,
								GlobalCodeConstant.LOG_SERVICE_TYPE_RECV);

						Object body = jsonObject.get("body");

						// 获取消息头标签的消息类型styp
						String eventType = meta.getEventType();
						if (StringUtils.isNotBlank(eventType)) {

							// 消息分发处理器
							MqMessageRecvServer server = new MqMessageRecvServerImpl(producer);

							if (body instanceof JSONArray) {
								JSONArray bodyArray = (JSONArray) body;
								for (Object object : bodyArray) {
									server.handle(meta, object.toString());
								}
							} else {
								server.handle(meta, body.toString());
							}
						}
					} catch (Exception e) {
						HaLog.error(log, e, MsgIdConstant.MS_ERR_0001, "");
					}
				}

				return ConsumeOrderlyStatus.SUCCESS;
			}
		});
		try {
			this.consumer.start();
		} catch (MQClientException e) {
			HaLog.error(log, e, MsgIdConstant.MS_ERR_0001, "");
		}
	}

	public String getConsumerTopic() {
		return consumerTopic;
	}

	public void setConsumerTopic(String consumerTopic) {
		this.consumerTopic = consumerTopic;
		try {
			this.consumer = RocketMQConsumerUtil.getConsumer(this.consumerTopic);
		} catch (MQClientException e) {
			HaLog.error(log, e, MsgIdConstant.MS_ERR_0001, "");
		}
	}

	public DefaultMQPushConsumer getConsumer() {
		return consumer;
	}

	public void setConsumer(DefaultMQPushConsumer consumer) {
		this.consumer = consumer;
	}

	public String getConsumerGroup() {
		return consumerGroup;
	}

	public void setConsumerGroup(String consumerGroup) {
		this.consumerGroup = consumerGroup;
	}

}
