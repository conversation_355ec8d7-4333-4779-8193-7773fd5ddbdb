package com.air.security.sodb.dts.receive.listener;

import com.air.security.sodb.data.core.base.Meta;
import com.air.security.sodb.data.core.constant.GlobalCodeConstant;
import com.air.security.sodb.data.core.constant.MsgIdConstant;
import com.air.security.sodb.data.core.mq.MyProducer;
import com.air.security.sodb.data.core.mq.impl.SyncRocketMQProducer;
import com.air.security.sodb.data.core.spring.ApplicationContextUtil;
import com.air.security.sodb.data.core.util.DataTransferLogRecordUtil;
import com.air.security.sodb.data.core.util.HaLog;
import com.air.security.sodb.data.core.util.RocketMQConsumerUtil;
import com.air.security.sodb.data.core.util.ThreadUtil;
import com.air.security.sodb.dts.receive.server.MqMessageRecvServer;
import com.air.security.sodb.dts.receive.server.MqMessageRecvServerImpl;
import com.air.security.sodb.dts.srvc.domain.service.RedisService;
import com.alibaba.fastjson.JSON;
import com.alibaba.fastjson.JSONArray;
import com.alibaba.fastjson.JSONObject;
import org.apache.commons.lang3.StringUtils;
import org.apache.rocketmq.client.consumer.DefaultMQPushConsumer;
import org.apache.rocketmq.client.consumer.listener.ConsumeConcurrentlyContext;
import org.apache.rocketmq.client.consumer.listener.ConsumeConcurrentlyStatus;
import org.apache.rocketmq.client.consumer.listener.MessageListenerConcurrently;
import org.apache.rocketmq.client.exception.MQClientException;
import org.apache.rocketmq.common.message.MessageExt;
import org.slf4j.Logger;
import org.slf4j.LoggerFactory;

import java.util.List;

/**
 * rocketmq的异步消息监听
 *
 * <AUTHOR>
 */
public class RocketMQMsgListener implements Runnable {

    private static final Logger log = LoggerFactory.getLogger(RocketMQMsgListener.class);

    /**
     * 消费者主题
     */
    private String consumerTopic;

    /**
     * RocketMQ消费者
     */
    private DefaultMQPushConsumer consumer;

    /**
     * 消费者组
     */
    private String consumerGroup;

    /**
     * RocketMQ生产者
     */
    private MyProducer producer = new SyncRocketMQProducer();

    @Override
    public void run() {
        this.consumer.setConsumerGroup(this.consumerGroup);
        consumer.registerMessageListener(new MessageListenerConcurrently() {

            @Override
            public ConsumeConcurrentlyStatus consumeMessage(List<MessageExt> msgs, ConsumeConcurrentlyContext context) {
                for (MessageExt msg : msgs) {
                    try {
                        String message = new String(msg.getBody(), "utf-8");

                        if (StringUtils.isBlank(message)) {
                            continue;
                        }
                        ThreadUtil.runWithThreadPool(new Runnable() {

                            @Override
                            public void run() {
                                JSONObject jsonObject = JSONObject.parseObject(message);
                                HaLog.info(log, MsgIdConstant.MS_INF_0001, "原始数据为》》》" + message + ",MsgId为》》》" + msg.getMsgId());
                                // 获取消息头标签
                                Meta meta = JSON.parseObject(jsonObject.getString("meta"), Meta.class);
                                if (null == meta) {
                                    return;
                                }
                                String eventType1 = meta.getEventType();
                                String receiver = meta.getReceiver();
                                if (eventType1.contains("_SYNC")) {
                                    if (StringUtils.isBlank(receiver)){
                                        HaLog.info(log, MsgIdConstant.MS_INF_0002, "receiver为空，已经过滤");
                                        return;
                                    }
                                    if (!"WIP".equals(receiver)){
                                        HaLog.info(log, MsgIdConstant.MS_INF_0002, "该数据不是SODB请求的数据，已经过滤");
                                        return;
                                    }
                                    JSONObject object = jsonObject.getJSONObject("body");
                                    String batchId = object.getString("batchId");
                                    RedisService service1 = (RedisService) ApplicationContextUtil.getBean("RedisService");
                                    Boolean isBatchIdPresent = service1.selectRedisTime(batchId);
                                    if (!isBatchIdPresent) {
                                        HaLog.info(log, MsgIdConstant.MS_INF_0002, "该数据不是本次请求的数据，已经过滤");
                                        return;
                                    }
                                }

                                // 记录数据交互日志
                                DataTransferLogRecordUtil.record(meta, jsonObject, GlobalCodeConstant.LOG_TYPE_RECV, GlobalCodeConstant.LOG_SERVICE_TYPE_RECV);

                                Object body = jsonObject.get("body");

                                // 获取消息头标签的消息类型styp
                                String eventType = meta.getEventType();
                                if (StringUtils.isNotBlank(eventType)) {

                                    // 消息分发处理器
                                    MqMessageRecvServer server = new MqMessageRecvServerImpl(producer);

                                    if (body instanceof JSONArray) {
                                        JSONArray bodyArray = (JSONArray) body;
                                        for (Object object : bodyArray) {
                                            server.handle(meta, object.toString());
                                        }
                                    } else {
                                        server.handle(meta, body.toString());
                                    }
                                    HaLog.info(log, MsgIdConstant.MS_INF_0002, "mq监听");
                                }
                            }

                        });
                    } catch (Exception e) {
                        HaLog.error(log, e, MsgIdConstant.MS_ERR_0001, "");
                    }
                }
                return ConsumeConcurrentlyStatus.CONSUME_SUCCESS;
            }
        });

        try {
            this.consumer.start();
        } catch (MQClientException e) {
            HaLog.error(log, e, MsgIdConstant.MS_ERR_0001, "");
        }
    }

    public String getConsumerTopic() {
        return consumerTopic;
    }

    public void setConsumerTopic(String consumerTopic) {
        this.consumerTopic = consumerTopic;
        try {
            this.consumer = RocketMQConsumerUtil.getConsumer(this.consumerTopic);
        } catch (MQClientException e) {
            HaLog.error(log, e, MsgIdConstant.MS_ERR_0001, "");
        }
    }

    public DefaultMQPushConsumer getConsumer() {
        return consumer;
    }

    public void setConsumer(DefaultMQPushConsumer consumer) {
        this.consumer = consumer;
    }

    public String getConsumerGroup() {
        return consumerGroup;
    }

    public void setConsumerGroup(String consumerGroup) {
        this.consumerGroup = consumerGroup;
    }

}
