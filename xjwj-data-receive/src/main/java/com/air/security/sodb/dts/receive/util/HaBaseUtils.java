package com.air.security.sodb.dts.receive.util;

import org.slf4j.Logger;
import org.slf4j.LoggerFactory;
import org.springframework.http.HttpEntity;
import org.springframework.http.HttpMethod;
import org.springframework.http.ResponseEntity;

import javax.servlet.http.HttpServletRequest;

/**
 * 向组件框架发送请求  执行工具类
 * <AUTHOR>
 *
 */
public class HaBaseUtils {
    private static final Logger log = LoggerFactory.getLogger(HaBaseUtils.class);

    /**
     * api数据接口请求
     *
     * @param request
     * @param url 请求地址
     * @param responseType 返回的数据类型  例如String.class xx.class等
     * @param obj 请求参数
     * @return
     */
    public <T> T getHaObj(HttpServletRequest request,String url,Class<T> responseType,Object obj){
        T body = null;
        try{
            HttpEntity<Object> httpEntity = HttpUtil.getInstance().getHttpEntity(request, obj);
            ResponseEntity<T> entity = RestTemplateUtil.getInstance().exchange(url, HttpMethod.POST,
                    httpEntity, responseType);
            body = entity.getBody();
        } catch(Exception e){
            if(log.isErrorEnabled()){
                log.error("请求失败，请求的URL为：" + url, e);
            }
            throw e;
        }
        return body;
    }

    public static HaBaseUtils getInstance(){
        return new HaBaseUtils();
    }

    /**
     * 验证是否超时异常
     * @param e
     * @return
     */
    public static boolean isConnectTimeoutException(Throwable e) {
        return e == null? false: e.getMessage().contains("Connection timed out: connect");
    }

}
