package com.air.security.sodb.dts.srvc.restsrvc.receive.common;

import com.air.security.sodb.data.core.base.Meta;
import com.air.security.sodb.data.core.constant.GlobalCodeConstant;
import com.air.security.sodb.data.core.constant.MsgIdConstant;
import com.air.security.sodb.data.core.mq.MyProducer;
import com.air.security.sodb.data.core.mq.impl.SyncRocketMQProducer;
import com.air.security.sodb.data.core.spring.ApplicationContextUtil;
import com.air.security.sodb.data.core.util.*;
import com.air.security.sodb.dts.receive.server.MqMessageRecvServer;
import com.air.security.sodb.dts.receive.server.MqMessageRecvServerImpl;
import com.air.security.sodb.dts.srvc.domain.service.RedisService;
import com.alibaba.fastjson.JSONObject;
import org.slf4j.Logger;
import org.slf4j.LoggerFactory;
import org.springframework.web.bind.annotation.CrossOrigin;
import org.springframework.web.bind.annotation.PostMapping;
import org.springframework.web.bind.annotation.RequestMapping;
import org.springframework.web.bind.annotation.RestController;

import javax.servlet.http.HttpServletRequest;
import java.text.SimpleDateFormat;
import java.util.Date;
import java.util.Properties;
import java.util.UUID;

@RestController
@CrossOrigin
@RequestMapping("/api/release/common")
public class ComSearchReleaseController {

    private static final Logger log = LoggerFactory.getLogger(ComSearchReleaseController.class);

    /**
     * RocketMQ生产者
     */
    private MyProducer producer = new SyncRocketMQProducer();

    /**
     * 同步配置文件
     */
    private static Properties properties = ResourceUtil.getInstance().getLocalProperties(
            PropertyUtil.getProperty(GlobalCodeConstant.FULL_DATA_SYNC_CONFIG_PATH_KEY), "fullDataSync");


    @PostMapping("/execute")
    public ResultVo execute(HttpServletRequest request) {

        JSONObject jsonObj = RequestUtil.requestGetJson(request);
        String dataTypeCode = jsonObj.getJSONObject("meta").getString("dataTypeCode");
        String receiver = jsonObj.getJSONObject("meta").getString("receiver");
        ValidateUtil.notBlank(dataTypeCode, "接入数据类型编码不能为空");
        ValidateUtil.notBlank(receiver, "receiver不能为空");
        String property = properties.getProperty(dataTypeCode);
        String[] split = property.split(",");

        HaLog.info(log, MsgIdConstant.MS_INF_0001, split[1]);

        // 提取body部分
        JSONObject body = jsonObj.getJSONObject("body");
        ValidateUtil.notNull(body, "body不能为空");

        // 创建新的JSON对象用于消息传递
        JSONObject json = new JSONObject();
        String batchId = UUID.randomUUID().toString();
        RedisService service1 = (RedisService) ApplicationContextUtil.getBean("RedisService");
        service1.setRedisTime(batchId);
        json.put("batchId", batchId);
        JSONObject object = body.getJSONObject("object");
        if (object != null) {
            if ("EQU_REQUEST".equals(split[1])){
                log.info(">>>>>>>>>>>>>>>>>>>>"+object.toJSONString());
                String startTime = object.getString("startTime");
                String endTime = object.getString("endTime");
                if ("Invalid Date".equals(startTime)){
                    object.put("startTime", "");
                    log.info("3"+object.toJSONString());
                }
                if ("Invalid Date".equals(endTime)){
                    object.put("endTime", "");
                    log.info("5"+object.toJSONString());
                }
                log.info("7"+object.toJSONString());
            }
            json.put("object", object);
        }
        json.put("page", body.getJSONObject("page"));

        try {
            // 获取当前时间并格式化
            SimpleDateFormat sdf = new SimpleDateFormat("yyyyMMddHHmmss");
            String currentTime = sdf.format(new Date());

            // 获取消息头标签
            Meta meta = new Meta();
            meta.setSender("WIP");
            meta.setSequence(UUID.randomUUID().toString());
            meta.setSendTime(currentTime);
            meta.setForwardTime("");
            meta.setMsgType(split[0]);
            meta.setEventType(split[1]);
            meta.setRecvSequence("");
            meta.setRecvTime("");
            meta.setReceiver(receiver);
            MqMessageRecvServer server = new MqMessageRecvServerImpl(producer);
            server.handle(meta, json.toJSONString());
            json.put("meta", meta);
            return ResultVo.success(json);
        } catch (Exception e) {
            HaLog.error(log, MsgIdConstant.MS_ERR_0001, e, "消息处理异常");
            ResultMessage resultMessage = new ResultMessage("EventType:" + split[1] + " MsgType:" + split[0]);
            return ResultVo.failure(resultMessage);
        } finally {
            HaLog.info(log, MsgIdConstant.MS_INF_0002, split[1]);
        }
    }
}
