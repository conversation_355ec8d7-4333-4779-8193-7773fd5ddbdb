package com.air.security.sodb.dts.receive.service.equ;

import com.air.security.sodb.data.core.base.Meta;
import com.air.security.sodb.data.core.constant.MsgIdConstant;
import com.air.security.sodb.data.core.exception.SystemBaseException;
import com.air.security.sodb.data.core.util.HaLog;
import com.air.security.sodb.dts.receive.service.AbstractMessageRecvService;
import com.alibaba.fastjson.JSON;
import com.alibaba.fastjson.JSONObject;
import org.slf4j.Logger;
import org.slf4j.LoggerFactory;
import org.springframework.stereotype.Service;

/**
 * <AUTHOR> @Description:设备信息处理
 **/
@Service("EquRecvServiceImpl")
public class EquRecvServiceImpl extends AbstractMessageRecvService {
    private static final Logger log = LoggerFactory.getLogger(EquRecvServiceImpl.class);

    @Override
    public void execute(Meta meta, String messageBody) throws SystemBaseException {
        HaLog.info(log, MsgIdConstant.MS_INF_0001, meta.getEventType());

        JSONObject jsonObject = JSON.parseObject(messageBody);

        JSONObject json = new JSONObject();

        Object event = jsonObject.get("device");

        // 响应消息头
        json.put("meta", meta);

        // 响应消息体
        json.put("body", event);

        // 转换为JSON格式，并发送消息到指定主题
        super.putSendMessage(json);
        HaLog.info(log, MsgIdConstant.MS_INF_0002, meta.getEventType());
    }
}
