package com.air.security.sodb.dts.receive.util;

import com.air.security.sodb.data.core.util.HttpRequestUtil;
import com.air.security.sodb.data.core.util.PropertyUtil;
import com.alibaba.fastjson.JSONArray;
import com.alibaba.fastjson.JSONObject;

import java.util.HashMap;
import java.util.List;
import java.util.Map;

public class CrossEquStatusCache {

	private static Map<String, String> map = new HashMap<>();
	
	private static String url = PropertyUtil.getProperty("sodb.cross.equ.status.req.url");

	public static Map<String, String> getCacheList(){
		if (map.size()==0 || map.isEmpty()) {
			String result = HttpRequestUtil.sendPost(url, "");
			JSONObject resJson = JSONObject.parseObject(result);
			JSONArray cacheArray = resJson.getJSONArray("datalist");
			List<CrossEquStatus> cacheList = JSONArray.parseArray(cacheArray.toJSONString(), CrossEquStatus.class);
			for (CrossEquStatus ysEquInfo : cacheList) {
				map.put(ysEquInfo.getEquCode(),ysEquInfo.getTimeStateId());
			}
			System.out.println(map.size());
		}
		return map;
	}

}
