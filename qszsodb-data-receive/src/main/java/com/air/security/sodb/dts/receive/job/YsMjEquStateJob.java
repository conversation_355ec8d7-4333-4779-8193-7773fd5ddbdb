package com.air.security.sodb.dts.receive.job;

import com.air.security.sodb.data.core.base.Meta;
import com.air.security.sodb.data.core.constant.GlobalCodeConstant;
import com.air.security.sodb.data.core.constant.MsgIdConstant;
import com.air.security.sodb.data.core.exception.SystemBaseException;
import com.air.security.sodb.data.core.quartz.AbstractJob;
import com.air.security.sodb.data.core.util.DateTimeUtil;
import com.air.security.sodb.data.core.util.HaLog;
import com.air.security.sodb.data.core.util.UuidUtil;
import com.air.security.sodb.dts.receive.server.MqMessageRecvServer;
import com.air.security.sodb.dts.receive.server.MqMessageRecvServerImpl;
import com.air.security.sodb.dts.receive.util.DeviceService;
import com.air.security.sodb.dts.receive.util.HkEquStatus;
import com.air.security.sodb.dts.receive.util.HkEquStatusCache;
import com.alibaba.fastjson.JSONArray;
import com.alibaba.fastjson.JSONObject;
import org.apache.commons.lang3.StringUtils;
import org.quartz.JobDataMap;
import org.slf4j.Logger;
import org.slf4j.LoggerFactory;

import java.io.IOException;
import java.util.HashSet;
import java.util.List;
import java.util.Set;
import java.util.stream.Collectors;

/**
 * 门禁报警资源信息获取任务
 *
 * <AUTHOR>
 */
public class YsMjEquStateJob extends AbstractJob {

    private static final Logger log = LoggerFactory.getLogger(YsMjEquStateJob.class);


    /**
     *
     */
    private MqMessageRecvServer server = new MqMessageRecvServerImpl();

    @Override
    public void executeJob(JobDataMap paramMap) {
        HaLog.info(log, MsgIdConstant.MS_INF_0001, "获取门禁报警状态定时任务");

        try {
            execPost();
        } catch (Exception e) {
            throw new SystemBaseException("获取门禁报警状态定时任务失败", e);
        } finally {
            HaLog.info(log, MsgIdConstant.MS_INF_0002, "获取门禁报警状态定时任务");
        }

    }

    /**
     * @throws IOException
     */
    private void execPost() throws IOException {
        // 获取缓存列表和设备记录
        List<HkEquStatus> cacheList = HkEquStatusCache.getmjCacheList();
        JSONArray jsonArray = DeviceService.queryDeviceRecordList();

        // 从cacheList中提取所有equCode
        Set<String> equCodes = cacheList.stream().map(HkEquStatus::getEquCode).collect(Collectors.toSet());

        // 准备集合来存储交集
        Set<String> intersection = new HashSet<>();

        // 遍历jsonArray来查找交集
        for (int i = 0; i < jsonArray.size(); i++) {
            JSONObject deviceRecord = jsonArray.getJSONObject(i);
            String deviceCode = deviceRecord.getString("deviceCode");

            if (equCodes.contains(deviceCode)) {
                intersection.add(deviceCode);
            }
        }

        // 检查并处理交集中的项目
        for (String code : intersection) {
            // 查找对应的HkEquStatus对象
            HkEquStatus equStatus = cacheList.stream().filter(e -> e.getEquCode().equals(code)).findFirst().orElse(null);

            if (equStatus != null) {
                // 查找相应的设备记录
                for (int i = 0; i < jsonArray.size(); i++) {
                    JSONObject deviceRecord = jsonArray.getJSONObject(i);
                    if (deviceRecord.getString("deviceCode").equals(code)) {
                        String onlineStatus = deviceRecord.getString("status");
                        onlineStatus = formatStatus(onlineStatus);
                        if (!StringUtils.equals(equStatus.getTimeStateId(), onlineStatus)) {
                            // 状态不一致，更新cacheList并构建JSON对象
                            equStatus.setTimeStateId(onlineStatus);
                            JSONObject json = new JSONObject();
                            json.put("operateTime", DateTimeUtil.getCurrentTimestampStr("yyyy-MM-dd HH:mm:ss"));
                            json.put("timeStateId", onlineStatus);
                            Meta meta = new Meta();
                            meta.setEventType("ysMjEquState");
                            meta.setRecvSequence(UuidUtil.getUuid32());
                            server.handle(meta, json.toString());
                        }
                        break;
                    }
                }
            }
        }

    }

    /**
     * 资源编码转换
     *
     * @param onlineStatus 状态
     * @return
     */
    private String formatStatus(String onlineStatus) {
        if (GlobalCodeConstant.ZERO.equalsIgnoreCase(onlineStatus)) {
            onlineStatus = "ES02";
        }
        if (GlobalCodeConstant.ONE.equalsIgnoreCase(onlineStatus)) {
            onlineStatus = "ES01";
        }
        return onlineStatus;
    }
}

