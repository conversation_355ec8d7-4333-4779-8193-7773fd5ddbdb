package com.air.security.sodb.dts.receive.service.car;

import com.air.security.sodb.data.core.base.Meta;
import com.air.security.sodb.data.core.constant.MsgIdConstant;
import com.air.security.sodb.data.core.util.Base64Util;
import com.air.security.sodb.data.core.util.HaLog;
import com.air.security.sodb.data.core.util.MinioUtil;
import com.air.security.sodb.data.core.util.PropertyUtil;
import com.air.security.sodb.dts.receive.service.AbstractMessageRecvService;
import com.alibaba.fastjson.JSONObject;
import org.apache.commons.lang3.StringUtils;
import org.slf4j.Logger;
import org.slf4j.LoggerFactory;
import org.springframework.stereotype.Service;

import java.io.InputStream;

@Service("YsCarPhotoMessageRecvFormatServiceImpl")
public class YsCarPhotoMessageRecvFormatServiceImpl extends AbstractMessageRecvService {

    private static final Logger log = LoggerFactory.getLogger(YsCarPhotoMessageRecvFormatServiceImpl.class);

    private static final String BUCKET = PropertyUtil.getProperty("bucket");
    private static final String AIRPORT_IATA = PropertyUtil.getProperty("airport.iata");

    @Override
    public JSONObject execute(Meta meta, String messageBody) throws Exception {
        HaLog.info(log, MsgIdConstant.MS_INF_0001, meta.getEventType());

        JSONObject json = JSONObject.parseObject(messageBody);

        // 上传图片到服务器，返回url，拼接json
        this.uploadPicture(json, meta, "Files");

        // 响应json消息
        JSONObject outputMsg = new JSONObject();

        // 响应消息头
        outputMsg.put("meta", meta);

        // 响应消息体
        outputMsg.put("body", json);

        // 转换为JSON格式，并发送消息到指定主题
        super.putSendMessage(outputMsg);

        HaLog.info(log, MsgIdConstant.MS_INF_0002, meta.getEventType());

        return outputMsg;
    }

    /**
     * 上传图片到服务器，返回url，拼接json
     *
     * @param json
     * @param meta
     * @param blobKey 字段
     * @throws Exception
     */
    private void uploadPicture(JSONObject json, Meta meta, String blobKey) throws Exception {

        // base64图片
        String imgName = json.getString(blobKey);
        if (StringUtils.isNotBlank(imgName)) {

            // 事件类型
            String eventType = meta.getEventType();

            String name = "";

            //运单号
            String recordId = json.getJSONObject("Data").getString("RecordID");

            if (StringUtils.isNotBlank(recordId)) {
                name = recordId + "_";
            }

            // 拼接路径
            String objectKey = MinioUtil.splicingPath(AIRPORT_IATA, eventType, name);

            InputStream stream = Base64Util.generateIs(imgName);

            int available = stream.available();

            // 上传图片至图片服务器
            String imgNameUrl = MinioUtil.uploadInputStream(BUCKET, objectKey, stream, available);

            // 删除之前json中的base64图片
            json.remove(blobKey);

            // 图片url
            json.put(blobKey, imgNameUrl);

            stream.close();

            HaLog.info(log, MsgIdConstant.MS_INF_0009, "上传停车场图片到minio", imgNameUrl);

        }

    }
}