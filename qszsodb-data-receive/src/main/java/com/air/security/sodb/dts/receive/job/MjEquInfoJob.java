package com.air.security.sodb.dts.receive.job;

import com.air.security.sodb.data.core.base.Meta;
import com.air.security.sodb.data.core.constant.MsgIdConstant;
import com.air.security.sodb.data.core.exception.SystemBaseException;
import com.air.security.sodb.data.core.quartz.AbstractJob;
import com.air.security.sodb.data.core.util.HaLog;
import com.air.security.sodb.data.core.util.PropertyUtil;
import com.air.security.sodb.data.core.util.UuidUtil;
import com.air.security.sodb.dts.receive.server.MqMessageRecvServer;
import com.air.security.sodb.dts.receive.server.MqMessageRecvServerImpl;
import com.air.security.sodb.dts.receive.util.CertificationInfoUtil;
import com.air.security.sodb.dts.receive.util.PageInfo;
import com.air.security.sodb.dts.receive.util.SrvcHelper;
import com.alibaba.fastjson.JSONArray;
import com.alibaba.fastjson.JSONObject;
import org.quartz.JobDataMap;
import org.slf4j.Logger;
import org.slf4j.LoggerFactory;

import java.io.IOException;

/**
 * 门禁报警资源信息获取任务
 *
 * <AUTHOR>
 */
public class MjEquInfoJob extends AbstractJob {

    private static final Logger log = LoggerFactory.getLogger(MjEquInfoJob.class);

    private static String mjEquInfo = PropertyUtil.getProperty("hk.mjEquInfo.service.code");

    private static String url = PropertyUtil.getProperty("hk.mjEquInfo.url");


    /**
     *
     */
    private MqMessageRecvServer server = new MqMessageRecvServerImpl();

    @Override
    public void executeJob(JobDataMap paramMap) {
        HaLog.info(log, MsgIdConstant.MS_INF_0001, "获取门禁报警资源信息定时任务");

        try {
            execPost(url, SrvcHelper.PAGE_SIZE, 1);
        } catch (Exception e) {
            throw new SystemBaseException("获取门禁报警资源信息失败", e);
        } finally {
            HaLog.info(log, MsgIdConstant.MS_INF_0002, "获取门禁报警资源信息定时任务");
        }

    }

    /**
     * @param url
     * @param pageSize
     * @param pageNo
     * @throws IOException
     */
    private void execPost(String url, int pageSize, int pageNo) throws IOException {
        JSONObject jsonObject = SrvcHelper.getPageParam(pageNo, pageSize);
        jsonObject.put("resourceType", "door");
        String messageBody = CertificationInfoUtil.send(url, jsonObject.toJSONString());

        // 获取分页数据
        PageInfo page = SrvcHelper.getPageInfo(messageBody);
        if (page.getTotalRows() > 0) {
            JSONObject json = JSONObject.parseObject(messageBody);
            JSONArray jsonJsonArray = json.getJSONObject("data").getJSONArray("list");
            Meta meta = new Meta();
            meta.setEventType(mjEquInfo);
            meta.setRecvSequence(UuidUtil.getUuid32());
            server.handle(meta, jsonJsonArray.toString());

            if (page.hasNextPage()) {
                // 查询下一页
                this.execPost(url, pageSize, page.getCurrentPage() + 1);
            }
        }
    }

}
