package com.air.security.sodb.dts.receive.job;

import cn.hutool.crypto.digest.DigestUtil;
import cn.hutool.http.ContentType;
import cn.hutool.http.HttpRequest;
import cn.hutool.http.HttpResponse;
import com.air.security.sodb.core.util.HaLog;
import com.air.security.sodb.core.util.HaUtil;
import com.air.security.sodb.data.core.base.Meta;
import com.air.security.sodb.data.core.util.PropertyUtil;
import com.air.security.sodb.data.core.util.UuidUtil;
import com.air.security.sodb.dts.receive.server.MqMessageRecvServer;
import com.air.security.sodb.dts.receive.server.MqMessageRecvServerImpl;
import com.alibaba.fastjson.JSON;
import com.alibaba.fastjson.JSONArray;
import com.alibaba.fastjson.JSONObject;
import org.apache.commons.lang3.StringUtils;
import org.quartz.Job;
import org.quartz.JobExecutionContext;
import org.quartz.JobExecutionException;
import org.slf4j.Logger;
import org.slf4j.LoggerFactory;

public class ScheduleCarBlackListTask implements Job {
    private static final Logger log = LoggerFactory.getLogger(ScheduleCarBlackListTask.class);

    private static String car_url = PropertyUtil.getProperty("ys.car.url");

    private static String username = PropertyUtil.getProperty("ys.car.username");

    private static String password = PropertyUtil.getProperty("ys.car.password");

    /**
     *
     */
    private MqMessageRecvServer server = new MqMessageRecvServerImpl();

    @Override
    public void execute(JobExecutionContext jobExecutionContext) throws JobExecutionException {
        String instr = "获取停车场黑名单定时任务";
        HaLog.info(log, HaUtil.formatMsg(HaUtil.MSG_START, instr));
        try {
            String url = car_url + "/LAPI/V1.0/ParkingLots/Vehicles/BlackLists";

            // 发送 HTTP 请求获取第一页数据
            HttpResponse response = sendAuthenticatedRequest(url, "{\"Limit\":200,\"Offset\":0}");

            // 处理第一页数据
            handleResponse(response);

            // 获取总数
            // 获取总数
            JSONObject jsonResponse = JSON.parseObject(response.body());
            int total = jsonResponse.getJSONObject("Response").getJSONObject("Data").getIntValue("Total");
            int offset = 200;

            // 获取剩余数据
            while (offset < total) {
                // 发送 HTTP 请求获取下一页数据
                response = sendAuthenticatedRequest(url, "{\"Limit\":200,\"Offset\":" + offset + "}");

                // 处理下一页数据
                handleResponse(response);

                // 更新偏移量
                offset += 200;
            }
        } catch (Exception e) {
            HaLog.error(log, e.getMessage(), e);
        } finally {
            HaLog.info(log, HaUtil.formatMsg(HaUtil.MSG_END, instr));
        }
    }

    private HttpResponse sendAuthenticatedRequest(String url, String body) {
        // 发送 HTTP 请求获取 WWW-Authenticate 头信息
        HttpResponse authResponse = HttpRequest.post(url).execute();
        String authHeader = authResponse.header("WWW-Authenticate");

        // 解析 WWW-Authenticate 头信息获取 realm 和 nonce
        String realm = getValueFromWWWAuthenticate("realm", authHeader);
        String nonce = getValueFromWWWAuthenticate("nonce", authHeader);

        // 生成 Authorization 头信息
        String method = "POST";
        String uri = "/LAPI/V1.0/ParkingLots/Vehicles/BlackLists";
        String authResponseValue = getAuthResponse(username, realm, password, method, uri, nonce);
        String authorizationHeader = "Digest username=\"" + username + "\", realm=\"" + realm + "\", nonce=\"" + nonce + "\", uri=\"" + uri + "\", response=\"" + authResponseValue + "\"";

        // 发送带有 Digest 鉴权的 HTTP POST 请求
        return HttpRequest.post(url).header("Authorization", authorizationHeader).body(body).contentType(ContentType.JSON.getValue()).execute();
    }

    /**
     * 处理HTTP响应。
     *
     * @param response 包含从服务器接收的HTTP响应内容的对象。
     *                 响应体被解析以获取特定格式的数据，并根据数据类型进行进一步处理。
     */
    private void handleResponse(HttpResponse response) {

        // 获取响应体字符串
        String body = response.body();
        // 如果响应体为空或只包含空白字符，则记录信息并终止处理
        if (StringUtils.isBlank(body)) {
            log.info("暂无数据");
            return;
        }
        // 解析响应体字符串为JSON对象
        JSONObject jsonObject = JSONObject.parseObject(body);
        // 从JSON对象中获取"Response"键对应的JSON对象
        JSONObject jsonResponse = jsonObject.getJSONObject("Response").getJSONObject("Data");
        // 获取"AllowedVehicleList"键对应的值
        Object allowedVehicleList = jsonResponse.get("VehicleBlackList");
        // 初始化Meta对象，并设置事件类型
        Meta meta = new Meta();
        meta.setEventType("YS_CAR_BLACK_INFO");
        // 检查"AllowedVehicleList"是否非空
        if (allowedVehicleList != null) {
            // 如果"AllowedVehicleList"是一个JSONArray，进行遍历处理
            if (allowedVehicleList instanceof JSONArray) {
                JSONArray bodyArray = (JSONArray) allowedVehicleList;
                for (Object object : bodyArray) {
                    JSONObject json = (JSONObject) object;
                    // 为每个处理项设置唯一的接收序列号
                    meta.setRecvSequence(UuidUtil.getUuid32());
                    // 调用server对象处理每个项目
                    server.handle(meta, json.toJSONString());
                }
            }
            // 如果"AllowedVehicleList"不是JSONArray，直接处理
            else {
                JSONObject json = (JSONObject) allowedVehicleList;
                meta.setRecvSequence(UuidUtil.getUuid32());
                server.handle(meta, json.toJSONString());
            }
        }
    }

    /**
     * 从WWW-Authenticate头中提取指定参数的值。
     *
     * @param paramName       需要提取的参数名，会自动添加等号和双引号以匹配头信息中的格式。
     * @param wwwAuthenticate WWW-Authenticate头的内容。
     * @return 参数值，位于双引号之间。
     */
    private String getValueFromWWWAuthenticate(String paramName, String wwwAuthenticate) {
        String paramValue;
        // 根据参数名格式化后查找参数值的起始位置
        paramName = paramName + "=\"";
        wwwAuthenticate = wwwAuthenticate.substring(wwwAuthenticate.indexOf(paramName) + paramName.length());
        // 提取双引号之间的参数值
        paramValue = wwwAuthenticate.substring(0, wwwAuthenticate.indexOf("\""));
        return paramValue;
    }

    /**
     * 计算摘要认证响应。
     *
     * @param username 用户名。
     * @param realm    命名空间。
     * @param password 密码。
     * @param method   请求方法。
     * @param uri      请求URI。
     * @param nonce    服务器提供的随机数。
     * @return 计算得到的摘要认证响应值。
     */
    private String getAuthResponse(String username, String realm, String password, String method, String uri, String nonce) {
        // 计算HA1值（用户名、命名空间、密码的组合经过MD5加密）
        String paramHA1 = concatString(username, realm, password);
        String HA1 = DigestUtil.md5Hex(paramHA1);
        // 计算HA2值（请求方法和URI的组合经过MD5加密）
        String paramHA2 = concatString(method, uri);
        String HA2 = DigestUtil.md5Hex(paramHA2);
        // 计算最终的响应值（HA1、nonce、HA2的组合经过MD5加密）
        String paramResponse = concatString(HA1, nonce, HA2);
        return DigestUtil.md5Hex(paramResponse);
    }

    /**
     * 拼接字符串。
     *
     * @param param 字符串数组，第一个元素作为拼接的起始字符串，之后的元素依次拼接。
     * @return 拼接后的字符串。
     */
    private String concatString(String... param) {
        StringBuilder stringBuilder = new StringBuilder(param[0]);
        // 循环拼接剩余参数
        for (int i = 1; i < param.length; i++) {
            stringBuilder.append(":").append(param[i]);
        }
        return stringBuilder.toString();
    }
}
