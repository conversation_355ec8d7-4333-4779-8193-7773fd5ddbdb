package com.air.security.sodb.dts.receive.job;

import com.air.security.sodb.data.core.base.Meta;
import com.air.security.sodb.data.core.constant.MsgIdConstant;
import com.air.security.sodb.data.core.exception.SystemBaseException;
import com.air.security.sodb.data.core.quartz.AbstractJob;
import com.air.security.sodb.data.core.util.HaLog;
import com.air.security.sodb.data.core.util.UuidUtil;
import com.air.security.sodb.dts.receive.server.MqMessageRecvServer;
import com.air.security.sodb.dts.receive.server.MqMessageRecvServerImpl;
import com.air.security.sodb.dts.receive.util.DeviceService;
import com.alibaba.fastjson.JSONArray;
import org.quartz.JobDataMap;
import org.slf4j.Logger;
import org.slf4j.LoggerFactory;

import java.io.IOException;

/**
 * 门禁报警资源信息获取任务
 *
 * <AUTHOR>
 */
public class YsMjEquInfoJob extends AbstractJob {

    private static final Logger log = LoggerFactory.getLogger(YsMjEquInfoJob.class);


    /**
     *
     */
    private MqMessageRecvServer server = new MqMessageRecvServerImpl();

    @Override
    public void executeJob(JobDataMap paramMap) {
        HaLog.info(log, MsgIdConstant.MS_INF_0001, "获取防区（门禁报警）资源信息定时任务");

        try {

            execPost();
        } catch (Exception e) {
            throw new SystemBaseException("获取防区（手动报警）资源信息失败", e);
        } finally {
            HaLog.info(log, MsgIdConstant.MS_INF_0002, "获取防区（手动报警）资源信息定时任务");
        }

    }

    /**
     * @throws IOException
     */
    private void execPost() throws IOException {
        Meta meta = new Meta();
        meta.setEventType("ysMjEquInfo");
        meta.setRecvSequence(UuidUtil.getUuid32());
        JSONArray jsonArray = DeviceService.queryDeviceRecordList();
        if (jsonArray == null || jsonArray.size() == 0) {
            log.info("暂无门禁资源数据");
            return;
        }
        server.handle(meta, jsonArray.toJSONString());
    }


}

