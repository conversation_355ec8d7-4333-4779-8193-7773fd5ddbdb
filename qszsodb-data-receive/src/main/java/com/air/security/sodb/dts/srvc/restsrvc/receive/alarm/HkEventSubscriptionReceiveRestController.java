package com.air.security.sodb.dts.srvc.restsrvc.receive.alarm;

import com.air.security.sodb.data.core.base.ReceiveErrorType;
import com.air.security.sodb.data.core.constant.GlobalCodeConstant;
import com.air.security.sodb.data.core.constant.MsgIdConstant;
import com.air.security.sodb.data.core.util.HaLog;
import com.air.security.sodb.data.core.util.PropertyUtil;
import com.air.security.sodb.data.core.util.RequestUtil;
import com.air.security.sodb.dts.receive.util.CertificationInfoUtil;
import com.air.security.sodb.dts.receive.util.ResponseDTO;
import com.alibaba.fastjson.JSONObject;
import org.slf4j.Logger;
import org.slf4j.LoggerFactory;
import org.springframework.web.bind.annotation.CrossOrigin;
import org.springframework.web.bind.annotation.RequestMapping;
import org.springframework.web.bind.annotation.RequestMethod;
import org.springframework.web.bind.annotation.RestController;

import javax.servlet.http.HttpServletRequest;
import javax.servlet.http.HttpServletResponse;

/**
 * @Description: 海康按事件类型订阅事件
 * @author: mk
 * @Date: 2020-08-13
 */
@CrossOrigin
@RestController
@RequestMapping("/api/release/eventSubscription")
public class HkEventSubscriptionReceiveRestController {

    private static final Logger log = LoggerFactory.getLogger(HkEventSubscriptionReceiveRestController.class);

    private static String eventSubscriptionUrl = PropertyUtil.getProperty("hk.eventSubscription.url");

    /**
     * 接口
     */
    @RequestMapping(value = "/execute", method = RequestMethod.POST)
    public Object execute(HttpServletRequest request, HttpServletResponse response) {
        JSONObject jsonObj = RequestUtil.requestGetJson(request);
        ResponseDTO<JSONObject> responseDto = new ResponseDTO<>();
        HaLog.info(log, MsgIdConstant.MS_INF_0001, "海康告警信息接入接口");
        try {
            // 执行业务处理
            if (null != jsonObj) {
                // 执行业务处理
                this.execute(jsonObj);
            }
            HaLog.info(log, MsgIdConstant.MS_INF_0002, "海康告警信息接入接口");
            return responseDto;
        } catch (Exception e) {
            responseDto.failed(ReceiveErrorType.SYSTEM_ERROR.getCode(), ReceiveErrorType.SYSTEM_ERROR.getMessage(),
                    "", GlobalCodeConstant.SYS_CODE_SAEMS);
            HaLog.error(log, e, MsgIdConstant.MS_ERR_0001);
            return responseDto;
        }

    }

    /**
     * 执行业务处理
     *
     * @param jsonObj
     */
    private void execute(JSONObject jsonObj) {
        CertificationInfoUtil.send(eventSubscriptionUrl, jsonObj.toJSONString());
    }

}
