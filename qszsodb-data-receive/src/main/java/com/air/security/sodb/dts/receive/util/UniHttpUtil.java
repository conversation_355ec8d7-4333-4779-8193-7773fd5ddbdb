package com.air.security.sodb.dts.receive.util;
/**
 * Copyright (C),  2011-2018, ZheJiang Uniview Technologies Co., Ltd. All rights reserved.
 * <http://www.uniview.com/>
 * <p>
 * FileName : UniHttpUtil
 * Author   : zW4705
 * Date     : 2018/9/19 16:42
 * DESCRIPTION: 速通门接口调用http工具类
 * <p>
 * History:
 * DATE        NAME        DESC
 */

import com.alibaba.fastjson.JSONObject;
import org.apache.commons.lang3.StringUtils;
import org.apache.http.HttpEntity;
import org.apache.http.HttpHeaders;
import org.apache.http.HttpStatus;
import org.apache.http.client.CookieStore;
import org.apache.http.client.methods.*;
import org.apache.http.client.protocol.HttpClientContext;
import org.apache.http.entity.ContentType;
import org.apache.http.entity.StringEntity;
import org.apache.http.entity.mime.MultipartEntityBuilder;
import org.apache.http.impl.client.BasicCookieStore;
import org.apache.http.impl.client.CloseableHttpClient;
import org.apache.http.impl.client.HttpClients;
import org.apache.http.protocol.BasicHttpContext;
import org.apache.http.protocol.HttpContext;
import org.apache.http.util.EntityUtils;
import org.slf4j.Logger;
import org.slf4j.LoggerFactory;

import java.io.IOException;
import java.net.URLEncoder;
import java.nio.charset.Charset;
import java.text.SimpleDateFormat;
import java.util.Locale;

/**
 * @author: zW4705
 * @date: 2018/9/19 16:42
 * @description: 速通门接口调用http工具类
 */
public class UniHttpUtil {
    /**
     * 日志信息
     */
    private static final Logger logger = LoggerFactory.getLogger(UniHttpUtil.class);
    /**
     * utf-8 编码格式
     */
    protected static ContentType contentType = ContentType.create("text/plain", Charset.forName("UTF-8"));
    /**
     * 时间格式转换
     */
    protected static SimpleDateFormat sdf = new SimpleDateFormat("EEE MMM dd yyyy HH:mm:ss 'GMT'Z", Locale.ENGLISH);
    /**
     * 接口调用所需的cookie
     */
    private static CookieStore cookieStore;

    public static void setCookieStore(CookieStore cookieStore) {
        UniHttpUtil.cookieStore = cookieStore;
    }

    /**
     * 获取登录的cookie信息
     *
     * @return
     */
    public static CookieStore loginForCookieStore(String loginUrl, String user, String passwd) throws Exception {
        JSONObject loginParm = new JSONObject();
        if (null != user && null != passwd) {
            loginParm.put("Id", user);
            loginParm.put("Pwd", passwd);
        } else {
            logger.error("user:{}, passwd:{} illegal.", user, passwd);
            return null;
        }

        CookieStore cookieStore = new BasicCookieStore();
        HttpContext localContext = new BasicHttpContext();
        localContext.setAttribute(HttpClientContext.COOKIE_STORE, cookieStore);
        //创建HttpClient对象
        CloseableHttpClient httpClient = HttpClients.custom().setDefaultCookieStore(cookieStore).build();

        //创建httpPost请求对象
        HttpPost httpPost = new HttpPost(loginUrl);
        httpPost.setHeader(HttpHeaders.CONTENT_TYPE, BaseConstant.CONTENT_TYPE_JSON);
        httpPost.setEntity(new StringEntity(loginParm.toString(), ContentType.APPLICATION_JSON));

        //执行请求
        CloseableHttpResponse httpResponse = httpClient.execute(httpPost, localContext);
        HttpEntity entity = httpResponse.getEntity();
        //解析请求结果
        String response = EntityUtils.toString(entity, "UTF-8");
        UniResult uniResult = JSONObject.parseObject(response, UniResult.class);
        //资源关闭
        close(httpClient, httpResponse);
        if (uniResult == null) {
            return null;
        } else if (HttpStatus.SC_OK != uniResult.getErrCode()) {
            logger.error("login failed, UniResult: {}", uniResult.toString());
        } else {
            logger.debug("login success, url:{}", loginUrl);
            // 赋值
            setCookieStore(cookieStore);
        }
        return cookieStore;
    }


    /**
     * 发送get请求
     *
     * @param httpUrl
     * @param json
     * @return
     * @throws IOException
     */
    public static UniResult sendHttpGetJson(String httpUrl, String json) throws Exception {
        String response = null;
        String params = null;
        HttpGet httpGet = null;
        if (StringUtils.isNotEmpty(json)) {
            params = URLEncoder.encode(json, "UTF-8");
            httpGet = new HttpGet(httpUrl + params);
        } else {
            httpGet = new HttpGet(httpUrl);
        }
        // 获取cookieStore
        CloseableHttpClient httpClient = HttpClients.custom().setDefaultCookieStore(cookieStore).build();
        CloseableHttpResponse httpResponse = httpClient.execute(httpGet);
        response = EntityUtils.toString(httpResponse.getEntity(), "UTF-8");
        //资源关闭
        close(httpClient, httpResponse);
        return JSONObject.parseObject(response, UniResult.class);
    }


    /**
     * 删除请求
     *
     * @param httpUrl
     * @param param
     * @return
     * @throws IOException
     */
    public static UniResult sendHttpDeleteJson(String httpUrl, String param) throws Exception {
        String msg = null;
        // 获取cookieStore
        CloseableHttpClient httpClient = HttpClients.custom().setDefaultCookieStore(cookieStore).build();
        HttpDeleteWithBody httpDelete = new HttpDeleteWithBody(httpUrl);
        httpDelete.setHeader(BaseConstant.CONTENT_TYPE, BaseConstant.CONTENT_TYPE_JSON);
        if (StringUtils.isNotEmpty(param)) {
            httpDelete.setEntity(new StringEntity(param, ContentType.APPLICATION_JSON));
        }
        CloseableHttpResponse httpResponse = httpClient.execute(httpDelete);
        HttpEntity httpEntity = httpResponse.getEntity();
        if (httpEntity != null) {
            msg = EntityUtils.toString(httpEntity, BaseConstant.CHARSET_UTF8);
        }
        //资源关闭
        close(httpClient, httpResponse);
        return JSONObject.parseObject(msg, UniResult.class);
    }

    /**
     * patch：部分字段更新
     *
     * @param httpUrl
     * @param json
     * @return
     * @throws IOException
     */
    public static UniResult sendHttpPatchJson(String httpUrl, String json) throws Exception {
        String response = null;
        HttpContext localContext = new BasicHttpContext();
        // 获取cookieStore
        localContext.setAttribute(HttpClientContext.COOKIE_STORE, cookieStore);

        // 1. 创建HttpClient对象
        CloseableHttpClient httpClient = HttpClients.custom().setDefaultCookieStore(cookieStore).build();
        // 2. 创建HttpPatch对象
        HttpPatch httpPatch = new HttpPatch(httpUrl);
        httpPatch.setHeader(HttpHeaders.CONTENT_TYPE, BaseConstant.CONTENT_TYPE_JSON);
        // 3. 设置POST请求传递参数
        if (json != null) {
            httpPatch.setEntity(new StringEntity(json, ContentType.APPLICATION_JSON));
        }
        // 4. 执行请求并处理响应
        CloseableHttpResponse httpResponse = httpClient.execute(httpPatch, localContext);
        HttpEntity entity = httpResponse.getEntity();
        response = EntityUtils.toString(entity);
        //资源关闭
        close(httpClient, httpResponse);
        return JSONObject.parseObject(response, UniResult.class);
    }

    /**
     * 发送put 请求
     *
     * @param httpUrl
     * @param json
     * @return
     * @throws IOException
     */
    public static UniResult sendHttpPutJson(String httpUrl, String json) throws Exception {
        // 获取cookieStore
        CloseableHttpClient httpClient = HttpClients.custom().setDefaultCookieStore(cookieStore).build();
        HttpPut httpPut = new HttpPut(httpUrl);
        httpPut.setHeader(HttpHeaders.CONTENT_TYPE, BaseConstant.CONTENT_TYPE_JSON);
        // 3. 设置put请求传递参数
        if (StringUtils.isNotEmpty(json)) {
            httpPut.setEntity(new StringEntity(json, ContentType.APPLICATION_JSON));
        }
        CloseableHttpResponse httpResponse = httpClient.execute(httpPut);
        HttpEntity httpEntity = httpResponse.getEntity();
        String msg = null;
        if (httpEntity != null) {
            msg = EntityUtils.toString(httpEntity, BaseConstant.CHARSET_UTF8);
        }
        //资源关闭
        close(httpClient, httpResponse);
        return JSONObject.parseObject(msg, UniResult.class);
    }

    /**
     * Post 请求
     *
     * @param httpUrl
     * @param json
     * @return
     * @throws IOException
     */
    public static UniResult sendHttpPostJson(String httpUrl, String json) throws Exception {
        HttpContext localContext = new BasicHttpContext();

        // 获取cookieStore
        localContext.setAttribute(HttpClientContext.COOKIE_STORE, cookieStore);

        // 1. 创建HttpClient对象
        CloseableHttpClient httpClient = HttpClients.custom().setDefaultCookieStore(cookieStore).build();
        // 2. 创建HttpPost对象
        HttpPost httpPost = new HttpPost(httpUrl);
        httpPost.setHeader(HttpHeaders.CONTENT_TYPE, BaseConstant.CONTENT_TYPE_JSON);
        // 3. 设置POST请求传递参数
        if (json != null) {
            httpPost.setEntity(new StringEntity(json, ContentType.APPLICATION_JSON));
        }
        // 4. 执行请求并处理响应
        CloseableHttpResponse httpResponse = httpClient.execute(httpPost, localContext);
        HttpEntity entity = httpResponse.getEntity();
        String response = EntityUtils.toString(entity);
        //资源关闭
        close(httpClient, httpResponse);
        return JSONObject.parseObject(response, UniResult.class);
    }

    /**
     * Post Form 请求
     *
     * @param httpUrl
     * @param builder
     * @param encoding
     * @return
     */
    public static UniResult sendHttpPostFormData(String httpUrl, MultipartEntityBuilder builder, String encoding) {
        // 设置cookie信息
        CloseableHttpClient httpClient = HttpClients.custom().setDefaultCookieStore(cookieStore).build();
        HttpPost httpPost = new HttpPost(httpUrl);
        if (builder != null) {
            HttpEntity reqEntity = builder.build();
            httpPost.setEntity(reqEntity);
        }

        String msg = execute(httpClient, httpPost, encoding);
        return JSONObject.parseObject(msg, UniResult.class);
    }

    /**
     * Put Form 请求
     *
     * @param httpUrl
     * @param builder
     * @param encoding
     * @return
     */
    public static UniResult sendHttpPutFormData(String httpUrl, MultipartEntityBuilder builder, String encoding) {
        // 设置cookie信息
        CloseableHttpClient httpClient = HttpClients.custom().setDefaultCookieStore(cookieStore).build();
        HttpPut httpPut = new HttpPut(httpUrl);
        if (builder != null) {
            HttpEntity httpEntity = builder.build();
            httpPut.setEntity(httpEntity);
        }
        String msg = execute(httpClient, httpPut, encoding);
        return JSONObject.parseObject(msg, UniResult.class);
    }

    /**
     * 接口请求
     *
     * @param httpClient
     * @param request
     * @param encoding
     * @return
     */
    public static String execute(CloseableHttpClient httpClient, HttpUriRequest request, String encoding) {
        String msg = null;
        CloseableHttpResponse httpResponse = null;
        try {
            httpResponse = httpClient.execute(request);
            HttpEntity httpEntity = httpResponse.getEntity();
            if (httpEntity != null) {
                msg = EntityUtils.toString(httpEntity, encoding);
            }
        } catch (Exception e) {
            logger.error("{}", e);
            return null;
        } finally {
            close(httpClient, httpResponse);
        }
        return msg;
    }

    /**
     * 资源释放
     *
     * @param httpClient
     * @param httpResponse
     */
    public static void close(CloseableHttpClient httpClient, CloseableHttpResponse httpResponse) {
        // 释放资源
        if (httpResponse != null) {
            try {
                httpResponse.close();
            } catch (IOException e) {
                logger.error("httpResponse close error \n", e);
            }
        }
        if (httpClient != null) {
            try {
                httpClient.close();
            } catch (IOException e) {
                logger.error("httpClient close error \n", e);
            }
        }
    }
}


