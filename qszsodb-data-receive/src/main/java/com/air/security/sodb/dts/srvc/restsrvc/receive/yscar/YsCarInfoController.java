package com.air.security.sodb.dts.srvc.restsrvc.receive.yscar;


import com.air.security.sodb.data.core.base.Meta;
import com.air.security.sodb.data.core.util.RequestUtil;
import com.air.security.sodb.data.core.util.UuidUtil;
import com.air.security.sodb.dts.receive.server.MqMessageRecvServer;
import com.air.security.sodb.dts.receive.server.MqMessageRecvServerImpl;
import com.alibaba.fastjson.JSONObject;
import org.apache.commons.lang3.StringUtils;
import org.slf4j.Logger;
import org.slf4j.LoggerFactory;
import org.springframework.web.bind.annotation.CrossOrigin;
import org.springframework.web.bind.annotation.PostMapping;
import org.springframework.web.bind.annotation.RequestMapping;
import org.springframework.web.bind.annotation.RestController;

import javax.crypto.Cipher;
import javax.crypto.KeyGenerator;
import javax.crypto.SecretKey;
import javax.crypto.spec.SecretKeySpec;
import javax.servlet.http.HttpServletRequest;
import java.security.SecureRandom;
import java.util.Base64;

@CrossOrigin
@RestController
@RequestMapping("/bms/park/cloud")
public class YsCarInfoController {

    /**
     * 日志
     */
    private static final Logger logger = LoggerFactory.getLogger(YsCarInfoController.class);

    /**
     *
     */
    private MqMessageRecvServer server = new MqMessageRecvServerImpl();

    private static final String PASSWORD = "46EBA22EF5204DD5B110A1F730513965";


    /**
     * 注册接口
     *
     * @param request request
     * @return
     */
    @PostMapping(value = "/register")
    public JSONObject register(HttpServletRequest request) {
        logger.info(" 注册接口》》》》开始");
        JSONObject jsonObj = RequestUtil.requestGetJson(request);
        logger.info(" 注册接口》》》》结束");
        JSONObject jsonObj1 = new JSONObject();
        jsonObj1.put("code", 200);
        jsonObj1.put("msg", "成功");
        jsonObj1.put("uuid", UuidUtil.getUuid32());
        return jsonObj1;
    }

    /**
     * 心跳保活接口
     *
     * @param request request
     * @return
     */
    @PostMapping(value = "/keepalive")
    public JSONObject keepalive(HttpServletRequest request) {
        logger.info(" 心跳保活接口》》》》开始");
        JSONObject jsonObj = RequestUtil.requestGetJson(request);
        logger.info(" 心跳保活接口》》》》结束");
        JSONObject jsonObj1 = new JSONObject();
        jsonObj1.put("code", 200);
        jsonObj1.put("msg", "成功");
        jsonObj1.put("uuid", UuidUtil.getUuid32());
        return jsonObj1;
    }

    @PostMapping(value = "/carin")
    public JSONObject carin(HttpServletRequest request) {
        logger.info("入场记录接口》》》》开始");
        JSONObject jsonObj = RequestUtil.requestGetJson(request);
        // 获取 'cipher' 字段
        String cipher = jsonObj.getString("cipher");

        try {
            // 解密 cipher
            String decryptedCipher = decryptAES(cipher);
            logger.info("解密后的 cipher: " + decryptedCipher);

            if (StringUtils.isNotBlank(decryptedCipher)) {
                JSONObject jsonObject = JSONObject.parseObject(decryptedCipher);
                Meta meta = new Meta();
                String parkingType = jsonObject.getJSONObject("Data").getString("ParkingType");
                if (StringUtils.isNotBlank(parkingType)&&"4".equals(parkingType)){
                    meta.setEventType("YS_CAR_ALARM");
                }else{
                    meta.setEventType("YS_CAR_IN");
                }

                meta.setRecvSequence(UuidUtil.getUuid32());

                if (jsonObject != null && jsonObject.size() != 0) {
                    server.handle(meta, jsonObject.toJSONString());
                }


            } else {
                logger.error("解密后的 cipher 为空或无效。");
                // 处理解密后的 cipher 为空或无效的情况
                // 您可以在此处抛出自定义异常或记录适当的消息
            }
        } catch (Exception e) {
            logger.error("解密 cipher 时出错：" + e.getMessage());
            // 处理解密异常（例如，无效的 cipher、解密失败）
            // 您可以在此处抛出自定义异常或记录适当的消息
        }

        logger.info("入场记录接口》》》》结束");
        JSONObject responseJson = new JSONObject();
        responseJson.put("code", 200);
        responseJson.put("msg", "成功");
        responseJson.put("uuid", UuidUtil.getUuid32());

        return responseJson;
    }

    @PostMapping(value = "/carout")
    public JSONObject carout(HttpServletRequest request) {
        logger.info("出场场记录接口》》》》开始");
        JSONObject jsonObj = RequestUtil.requestGetJson(request);
        // 获取 'cipher' 字段
        String cipher = jsonObj.getString("cipher");

        try {
            // 解密 cipher
            String decryptedCipher = decryptAES(cipher);
            logger.info("解密后的 cipher: " + decryptedCipher);

            if (StringUtils.isNotBlank(decryptedCipher)) {
                JSONObject jsonObject = JSONObject.parseObject(decryptedCipher);
                Meta meta = new Meta();
                String parkingType = jsonObject.getJSONObject("Data").getString("ParkingType");
                if (StringUtils.isNotBlank(parkingType)&&"4".equals(parkingType)){
                    meta.setEventType("YS_CAR_ALARM");
                }else{
                    meta.setEventType("YS_CAR_OUT");
                }
                meta.setRecvSequence(UuidUtil.getUuid32());

                if (jsonObject != null && jsonObject.size() != 0) {
                    server.handle(meta, jsonObject.toJSONString());
                }

            } else {
                logger.error("解密后的 cipher 为空或无效。");
                // 处理解密后的 cipher 为空或无效的情况
                // 您可以在此处抛出自定义异常或记录适当的消息
            }
        } catch (Exception e) {
            logger.error("解密 cipher 时出错：" + e.getMessage());
            // 处理解密异常（例如，无效的 cipher、解密失败）
            // 您可以在此处抛出自定义异常或记录适当的消息
        }

        logger.info("出场场记录接口》》》》结束");
        JSONObject responseJson = new JSONObject();
        responseJson.put("code", 200);
        responseJson.put("msg", "成功");
        responseJson.put("uuid", UuidUtil.getUuid32());

        return responseJson;
    }


    @PostMapping(value = "/uploadphoto")
    public JSONObject uploadphoto(HttpServletRequest request) {
        logger.info("上传图片接口》》》》开始");
        JSONObject jsonObj = RequestUtil.requestGetJson(request);
        // 获取 'cipher' 字段
        String cipher = jsonObj.getString("cipher");

        try {
            // 解密 cipher
            String decryptedCipher = decryptAES(cipher);
            logger.info("解密后的 cipher: " + decryptedCipher);

            if (StringUtils.isNotBlank(decryptedCipher)) {
                JSONObject jsonObject = JSONObject.parseObject(decryptedCipher);
                Meta meta = new Meta();
                meta.setEventType("YS_CAR_PHOTO");
                meta.setRecvSequence(UuidUtil.getUuid32());

                if (jsonObject != null && jsonObject.size() != 0) {
                    server.handle(meta, jsonObject.toJSONString());
                }

            } else {
                logger.error("解密后的 cipher 为空或无效。");
                // 处理解密后的 cipher 为空或无效的情况
                // 您可以在此处抛出自定义异常或记录适当的消息
            }
        } catch (Exception e) {
            logger.error("解密 cipher 时出错：" + e.getMessage());
            // 处理解密异常（例如，无效的 cipher、解密失败）
            // 您可以在此处抛出自定义异常或记录适当的消息
        }

        logger.info("上传图片接口》》》》结束");
        JSONObject responseJson = new JSONObject();
        responseJson.put("code", 200);
        responseJson.put("msg", "成功");
        responseJson.put("uuid", UuidUtil.getUuid32());

        return responseJson;
    }

    public static String decryptAES(String encryptResultStr) {
        try {
            String decrpt = ebotongDecrypto(encryptResultStr);
            byte[] decryptFrom = parseHexStr2Byte(decrpt);
            byte[] decryptResult = decrypt(decryptFrom, PASSWORD);
            return new String(decryptResult);
        } catch (Exception e) {
            System.out.println("解密异常");
            return null;
        }
    }

    private static String ebotongDecrypto(String str) {
        try {
            byte[] encodeByte = Base64.getDecoder().decode(str);
            return new String(encodeByte);
        } catch (Exception e) {
            System.out.println("IO 异常");
            return str;
        }
    }

    private static byte[] parseHexStr2Byte(String hexStr) {
        if (hexStr.length() < 1) {
            return null;
        }
        byte[] result = new byte[hexStr.length() / 2];
        for (int i = 0; i < hexStr.length() / 2; i++) {
            int high = Integer.parseInt(hexStr.substring(i * 2, i * 2 + 1), 16);
            int low = Integer.parseInt(hexStr.substring(i * 2 + 1, i * 2 + 2), 16);
            result[i] = (byte) (high * 16 + low);
        }
        return result;
    }

    private static byte[] decrypt(byte[] content, String password) {
        try {
            KeyGenerator kgen = KeyGenerator.getInstance("AES");
            SecureRandom secureRandom = SecureRandom.getInstance("SHA1PRNG");
            secureRandom.setSeed(password.getBytes());
            kgen.init(128, secureRandom);
            SecretKey secretKey = kgen.generateKey();
            byte[] enCodeFormat = secretKey.getEncoded();
            SecretKeySpec key = new SecretKeySpec(enCodeFormat, "AES");
            Cipher cipher = Cipher.getInstance("AES");
            cipher.init(Cipher.DECRYPT_MODE, key);
            byte[] result = cipher.doFinal(content);
            return result;
        } catch (Exception e) {
            System.out.println("解密异常");
            return null;
        }
    }

}
