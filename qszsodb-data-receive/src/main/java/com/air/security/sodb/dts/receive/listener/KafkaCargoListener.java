package com.air.security.sodb.dts.receive.listener;

import com.air.security.sodb.data.core.base.Meta;
import com.air.security.sodb.data.core.constant.GlobalCodeConstant;
import com.air.security.sodb.data.core.constant.MsgIdConstant;
import com.air.security.sodb.data.core.util.*;
import com.air.security.sodb.dts.receive.server.MqMessageRecvServer;
import com.air.security.sodb.dts.receive.server.MqMessageRecvServerImpl;
import com.air.security.sodb.dts.receive.util.KafkaConscumerUtils;
import com.alibaba.fastjson.JSON;
import com.alibaba.fastjson.JSONObject;
import org.apache.commons.lang.StringUtils;
import org.apache.kafka.clients.consumer.ConsumerRecord;
import org.apache.kafka.clients.consumer.ConsumerRecords;
import org.apache.kafka.clients.consumer.KafkaConsumer;
import org.slf4j.Logger;
import org.slf4j.LoggerFactory;

import java.time.Duration;

/**
 * @Description: kafka事件消息监听
 * @author: zhangc
 * @Date: 2018年12月18日
 */
public class KafkaCargoListener implements Runnable {

    private static final Logger log = LoggerFactory.getLogger(KafkaCargoListener.class);

    /**
     * 消息分发处理器
     */
    private static MqMessageRecvServer server = new MqMessageRecvServerImpl();

    /**
     * 报警消息发布服务主题
     */
    private static String topic = PropertyUtil.getProperty("kafka.cargo.msg.output.topic");

    @Override
    public void run() {

        HaLog.info(log, MsgIdConstant.MS_INF_0001, "kafka事件货检消息监听");

        KafkaConsumer<String, String> consumer = KafkaConscumerUtils.getConsumer(new String[] { topic });
        while (true) {
            ThreadUtil.sleepThreadUnit();

            // 消费消息
            ConsumerRecords<String, String> records = consumer.poll(Duration.ZERO);
            for (ConsumerRecord<String, String> record : records) {
                String message = record.value();

                if (StringUtils.isBlank(message)) {
                    continue;
                }

                // 接收消息日志
                HaLog.infoJson(log, message);

                try {

                    JSONObject jsonObject = JSONObject.parseObject(message);

                    if (jsonObject == null) {
                        return;
                    }

                    // 获取消息头标签
                    Meta meta = JSON.parseObject(jsonObject.getString("meta"), Meta.class);
                    if (null == meta) {
                        return;
                    }

                    meta.setRecvTime(DateTimeUtil.getCurrentTimestampStr("yyyyMMddHHmmss"));

                    // 记录数据交互日志
                    DataTransferLogRecordUtil.record(meta, jsonObject, GlobalCodeConstant.LOG_TYPE_RECV,
                            GlobalCodeConstant.LOG_SERVICE_TYPE_RELS);

                    // 获取消息头标签的消息类型styp
                    String eventType = meta.getEventType();
                    if (StringUtils.isNotBlank(eventType)) {

                        String body = jsonObject.getString("body");

                        if (StringUtils.isBlank(body)) {
                            return;
                        }

                        server.handle(meta, body);
                    }

                } catch (Exception e) {
                    HaLog.error(log, MsgIdConstant.MS_ERR_0001, e, "消息处理异常");
                }
            }
        }
    }

}
