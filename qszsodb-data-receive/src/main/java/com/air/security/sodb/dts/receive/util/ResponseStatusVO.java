package com.air.security.sodb.dts.receive.util;

import com.alibaba.fastjson.annotation.JSONField;

import java.util.Date;

public class ResponseStatusVO {

    /**
     * 资源定位符 对应操作的URL
     */
    @JSONField(name = "RequestURL")
    private String requestURL;
    /**
     * 状态码
     */
    @JSONField(name = "StatusCode")
    private int statusCode;
    /**
     * 状态描述
     */
    @JSONField(name = "StatusString")
    private String statusString;
    /**
     * 资源ID  POST方法创建资源时会返回ID，创建成功后必须返回新的ID，创建失败则无此ID
     */
    @JSONField(name = "Id")
    private String id;
    /**
     * 日期时间 当前时间， 用于需要校时的场合
     */
    @JSONField(name = "LocalTime", format = "yyyyMMddHHmmss")
    private Date localTime = new Date();

    public ResponseStatusVO() {
    }

    public ResponseStatusVO(String requestURL) {
        this.requestURL = requestURL;
    }

    public ResponseStatusVO(String requestURL, int statusCode, String statusString) {
        this.requestURL = requestURL;
        this.statusCode = statusCode;
        this.statusString = statusString;
    }

    public String getRequestURL() {
        return requestURL;
    }

    public void setRequestURL(String requestURL) {
        this.requestURL = requestURL;
    }

    public int getStatusCode() {
        return statusCode;
    }

    public void setStatusCode(int statusCode) {
        this.statusCode = statusCode;
    }

    public String getStatusString() {
        return statusString;
    }

    public void setStatusString(String statusString) {
        this.statusString = statusString;
    }

    public String getId() {
        return id;
    }

    public void setId(String id) {
        this.id = id;
    }

    public Date getLocalTime() {
        return localTime;
    }

    public void setLocalTime(Date localTime) {
        this.localTime = localTime;
    }
}
