package com.air.security.sodb.dts.receive.util;


import com.fasterxml.jackson.annotation.JsonProperty;

import java.math.BigInteger;

/**
 * <AUTHOR>
 * @date 2018/10/27 13:41
 * @description 详细设备信息
 */
public class DeviceRecord {
    /**
     * 通道id
     */
    @JsonProperty("Passid")
    private BigInteger passid;
    /**
     * 通道编码
     */
    @JsonProperty("Code")
    private String code;
    /**
     * 通道名称
     */
    @JsonProperty("Name")
    private String name;
    /**
     * 进出标志位
     */
    @JsonProperty("Inorout")
    private Integer inorout;
    /**
     * 自增主键
     */
    @JsonProperty("Seqid")
    private Integer seqid;
    /**
     * 设备编码
     */
    @JsonProperty("DeviceCode")
    private String deviceCode;
    /**
     * 设备名称
     */
    @JsonProperty("DeviceName")
    private String deviceName;
    /**
     * 设备类型
     */
    @JsonProperty("DeviceType")
    private Integer deviceType;
    /**
     * 设备子类型
     */
    @JsonProperty("DeviceSubType")
    private Integer deviceSubtype;
    /**
     * 设备状态
     */
    @JsonProperty("Status")
    private Integer status;
    /**
     * ip 地址
     */
    @JsonProperty("IpAddress")
    private String ipAddress;
    /**
     * 经度
     */
    @JsonProperty("Longitude")
    private String longitude;
    /**
     * 纬度
     */
    @JsonProperty("Latitudee")
    private String latitude;
    /**
     * 预留字段
     */
    @JsonProperty("Reserved")
    private String reserved;
    /**
     * 是否可用
     */
    @JsonProperty("Isenable")
    private Integer isenable;
    /**
     * 用户名
     */
    @JsonProperty("Username")
    private String username;
    /**
     * 密码
     */
    @JsonProperty("Passwd")
    private String passwd;
    /**
     * 端口
     */
    @JsonProperty("Port")
    private String port;
    /**
     * 型号
     */
    @JsonProperty("ModelNumber")
    private String modelNumber;
    /**
     * 版本
     */
    @JsonProperty("VersionNumber")
    private String versionNumber;
    /**
     * 工作方式
     */
    @JsonProperty("CheckWay")
    private Integer checkWay;
    /**
     * 删除标记  0 删除 1 未删除
     */
    @JsonProperty("Isdel")
    private Integer isdel;

    /**
     * 备注
     */
    @JsonProperty("Remark")
    private String remark;

    /**
     * 修改时间
     */
    private Integer lastModificationTime;
    /**
     * 序列号
     */
    @JsonProperty("SerialNo")
    private String serialNo;

    public BigInteger getPassid() {
        return passid;
    }

    public void setPassid(BigInteger passid) {
        this.passid = passid;
    }

    public String getCode() {
        return code;
    }

    public void setCode(String code) {
        this.code = code;
    }

    public String getName() {
        return name;
    }

    public void setName(String name) {
        this.name = name;
    }

    public Integer getInorout() {
        return inorout;
    }

    public void setInorout(Integer inorout) {
        this.inorout = inorout;
    }

    public Integer getSeqid() {
        return seqid;
    }

    public void setSeqid(Integer seqid) {
        this.seqid = seqid;
    }

    public String getDeviceCode() {
        return deviceCode;
    }

    public void setDeviceCode(String deviceCode) {
        this.deviceCode = deviceCode;
    }

    public String getDeviceName() {
        return deviceName;
    }

    public void setDeviceName(String deviceName) {
        this.deviceName = deviceName;
    }

    public Integer getDeviceType() {
        return deviceType;
    }

    public void setDeviceType(Integer deviceType) {
        this.deviceType = deviceType;
    }

    public Integer getDeviceSubtype() {
        return deviceSubtype;
    }

    public void setDeviceSubtype(Integer deviceSubtype) {
        this.deviceSubtype = deviceSubtype;
    }

    public Integer getStatus() {
        return status;
    }

    public void setStatus(Integer status) {
        this.status = status;
    }

    public String getIpAddress() {
        return ipAddress;
    }

    public void setIpAddress(String ipAddress) {
        this.ipAddress = ipAddress;
    }

    public String getLongitude() {
        return longitude;
    }

    public void setLongitude(String longitude) {
        this.longitude = longitude;
    }

    public String getLatitude() {
        return latitude;
    }

    public void setLatitude(String latitude) {
        this.latitude = latitude;
    }

    public String getReserved() {
        return reserved;
    }

    public void setReserved(String reserved) {
        this.reserved = reserved;
    }

    public Integer getIsenable() {
        return isenable;
    }

    public void setIsenable(Integer isenable) {
        this.isenable = isenable;
    }

    public String getUsername() {
        return username;
    }

    public void setUsername(String username) {
        this.username = username;
    }

    public String getPasswd() {
        return passwd;
    }

    public void setPasswd(String passwd) {
        this.passwd = passwd;
    }

    public String getPort() {
        return port;
    }

    public void setPort(String port) {
        this.port = port;
    }

    public String getModelNumber() {
        return modelNumber;
    }

    public void setModelNumber(String modelNumber) {
        this.modelNumber = modelNumber;
    }

    public String getVersionNumber() {
        return versionNumber;
    }

    public void setVersionNumber(String versionNumber) {
        this.versionNumber = versionNumber;
    }

    public Integer getCheckWay() {
        return checkWay;
    }

    public void setCheckWay(Integer checkWay) {
        this.checkWay = checkWay;
    }

    public Integer getIsdel() {
        return isdel;
    }

    public void setIsdel(Integer isdel) {
        this.isdel = isdel;
    }

    public String getRemark() {
        return remark;
    }

    public void setRemark(String remark) {
        this.remark = remark;
    }

    public Integer getLastModificationTime() {
        return lastModificationTime;
    }

    public void setLastModificationTime(Integer lastModificationTime) {
        this.lastModificationTime = lastModificationTime;
    }

    public String getSerialNo() {
        return serialNo;
    }

    public void setSerialNo(String serialNo) {
        this.serialNo = serialNo;
    }
}
