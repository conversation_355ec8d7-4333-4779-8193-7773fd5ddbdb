<?xml version="1.0" encoding="UTF-8"?>
<!--定时任务配置文件： -->
<quartz>

    <job>
        <job-detail>
            <name>handEquInfo-Job</name>
            <group>handEquInfoGroup</group>
            <description>获取手动报警资源信息定时任务Job</description>
            <job-class>com.air.security.sodb.dts.receive.job.HandEquInfoJob</job-class>
        </job-detail>
        <trigger>
            <!-- JOB开关，ON:开启 OFF:关闭 -->
            <switch>on</switch>
            <name>handEquInfo-Job-trigger</name>
            <group>handEquInfoGroup</group>
            <cron-expression>0 0 3 * * ?</cron-expression>
        </trigger>
    </job>

    <job>
        <job-detail>
            <name>handEquState-Job</name>
            <group>handEquStateGroup</group>
            <description>获取手动报警资源变更信息定时任务Job</description>
            <job-class>com.air.security.sodb.dts.receive.job.HandEquStateJob</job-class>
        </job-detail>
        <trigger>
            <!-- JOB开关，ON:开启 OFF:关闭 -->
            <switch>on</switch>
            <name>handEquState-Job-trigger</name>
            <group>handEquStateGroup</group>
            <cron-expression>0 0/20 * * * ?</cron-expression>
        </trigger>
    </job>


    <job>
        <job-detail>
            <name>ysMjEquInfoJob-Job</name>
            <group>ysMjEquInfoJobGroup</group>
            <description>获取门禁报警资源信息定时任务Job</description>
            <job-class>com.air.security.sodb.dts.receive.job.YsMjEquInfoJob</job-class>
        </job-detail>
        <trigger>
            <!-- JOB开关，ON:开启 OFF:关闭 -->
            <switch>on</switch>
            <name>ysMjEquInfo-Job-trigger</name>
            <group>ysMjEquInfoJobGroup</group>
            <cron-expression>0 0 2 * * ?</cron-expression>
        </trigger>
    </job>

    <job>
        <job-detail>
            <name>ysMjEquStateJob-Job</name>
            <group>ysMjEquStateJobGroup</group>
            <description>获取门禁报警资源信息定时任务Job</description>
            <job-class>com.air.security.sodb.dts.receive.job.YsMjEquStateJob</job-class>
        </job-detail>
        <trigger>
            <!-- JOB开关，ON:开启 OFF:关闭 -->
            <switch>on</switch>
            <name>ysMjEquState-Job-trigger</name>
            <group>ysMjEquStateJobGroup</group>
            <cron-expression>0 0/30 * * * ?</cron-expression>
        </trigger>
    </job>

    <job>
        <job-detail>
            <name>mjEquInfo-Job</name>
            <group>mjEquInfoGroup</group>
            <description>获取门禁报警资源信息定时任务Job</description>
            <job-class>com.air.security.sodb.dts.receive.job.MjEquInfoJob</job-class>
        </job-detail>
        <trigger>
            <!-- JOB开关，ON:开启 OFF:关闭 -->
            <switch>off</switch>
            <name>mjEquInfo-Job-trigger</name>
            <group>mjEquInfoGroup</group>
            <cron-expression>* * * * * ? *</cron-expression>
        </trigger>
    </job>

    <job>
        <job-detail>
            <name>mjEquState-Job</name>
            <group>mjEquStateGroup</group>
            <description>获取门禁资源变更信息定时任务Job</description>
            <job-class>com.air.security.sodb.dts.receive.job.MjEquStateJob</job-class>
        </job-detail>
        <trigger>
            <!-- JOB开关，ON:开启 OFF:关闭 -->
            <switch>off</switch>
            <name>mjEquState-Job-trigger</name>
            <group>mjEquStateGroup</group>
            <cron-expression>0 0/5 * * * ?</cron-expression>
        </trigger>
    </job>
</quartz>