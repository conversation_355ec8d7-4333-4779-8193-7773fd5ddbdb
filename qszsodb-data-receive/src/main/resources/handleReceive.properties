#\u6D77\u5EB7\u624B\u52A8\u62A5\u8B66\u8D44\u6E90\u4FE1\u606F
hkhandEquInfo=MessageRecvFormatServiceImpl,cContainerEqu,HK_SD_EQU,EQU
hkhandEquState=MessageRecvFormatServiceImpl,cContainerEqu,HK_SD_EQU_STATUS_UE,EQU_STATUS
#\u6D77\u5EB7\u5165\u4FB5\u62A5\u8B66\u4E8B\u4EF6\uFF08\u624B\u52A8\u62A5\u8B66\u62A5\u8B66\uFF09
handAlarmMessage=MessageRecvFormatServiceImpl,cContainerAlarm,HK_SD_ALARM,ALARM
#\u95E8\u7981\u8D44\u6E90\u4FE1\u606F
ysMjEquInfo=MessageRecvFormatServiceImpl,cContainerEqu,YS_MJ_EQU,EQU
#\u95E8\u7981\u8D44\u6E90\u53D8\u66F4\u4FE1\u606F
ysMjEquState=MessageRecvFormatServiceImpl,cContainerEqu,YS_MJ_EQU_STATUS_UE,EQU_STATUS
#\u95E8\u7981\u62A5\u8B66\u548C\u95E8\u7981\u5237\u5361
ysMjRecordMessage=CardMessageRecvFormatServiceImpl,cContainerRecord,YS_MJ_RECORD_CARD,RECORD_CARD
ysMjAlarmMessage=YsMjMessageRecvFormatServiceImpl,cContainerAlarm,YS_MJ_ALARM,ALARM

#\u6D77\u5EB7\u624B\u52A8\u62A5\u8B66\u8D44\u6E90\u4FE1\u606F
hkmjEquInfo=MessageRecvFormatServiceImpl,cContainerEqu,HK_MJ_EQU,EQU
#\u6D77\u5EB7\u624B\u52A8\u62A5\u8B66\u8D44\u6E90\u53D8\u66F4\u4FE1\u606F
hkmjEquState=MessageRecvFormatServiceImpl,cContainerEqu,HK_MJ_EQU_STATUS_UE,EQU_STATUS
#\u95E8\u7981\u62A5\u8B66\u548C\u95E8\u7981\u5237\u5361
mjAlarmMessage=MessageRecvFormatServiceImpl,cContainerAlarm|cContainerRecord,HK_MJ_ALARM|HK_MJ_RECORD_CARD,ALARM|RECORD_CARD

#\u65C5\u68C0\u6570\u636E
check=ScimsImgMessageRecvFormatServiceImpl,cContainerPassenger,PRSCJSON_SECURITY,PSGR_DATA
bag=ScimsImgMessageRecvFormatServiceImpl,cContainerPassenger,PSGR_OPEN_INFO,PSGR_DATA
flight=MessageRecvFormatServiceImpl,cContainerFlight,FLIGHT_DYN,FLIGHT_DATA
#\u8D27\u8FD0\u5B89\u68C0\u7CFB\u7EDF-\u8FD0\u5355\u57FA\u672C\u4FE1\u606F
WAY_BILL_LOG=MessageRecvFormatServiceImpl,cContainerCargo,WAY_BILL_LOG,CARGO
#\u8D27\u8FD0\u5B89\u68C0\u7CFB\u7EDF-\u8FD0\u5355\u5F00\u5305\u4FE1\u606F
OPEN_LOG=CargoOpenMessageRecvFormatServiceImpl,cContainerCargo,OPEN_LOG,CARGO
#\u8D27\u8FD0\u5B89\u68C0\u7CFB\u7EDF-\u8FD0\u5355\u5B89\u68C0\u4FE1\u606F
SECURITY_LOG=MessageRecvFormatServiceImpl,cContainerCargo,SECURITY_LOG,CARGO
#\u8D27\u8FD0\u5B89\u68C0\u7CFB\u7EDF-\u5B89\u68C0\u901A\u9053\u7684\u5F00\u653E\u4FE1\u606F
CHECKSTATUS_EQU=MessageRecvFormatServiceImpl,cContainerCargo,CHECKSTATUS_EQU,CARGO

#\u505C\u8F66\u573A\u9ED1\u540D\u5355\u63A5\u5165
YS_CAR_BLACK_INFO=MessageRecvFormatServiceImpl,cContainerCar,YS_CAR_BLACK_INFO,CAR_DATA
#\u505C\u8F66\u573A\u767D\u540D\u5355\u63A5\u5165
YS_CAR_WHITE_INFO=MessageRecvFormatServiceImpl,cContainerCar,YS_CAR_WHITE_INFO,CAR_DATA
#\u505C\u8F66\u573A\u62A5\u8B66\u63A5\u5165
YS_CAR_ALARM=MessageRecvFormatServiceImpl,cContainerAlarm,YS_CAR_ALARM,ALARM
#\u505C\u8F66\u573A\u8FDB\u51FA\u8F66\u8F86\u63A5\u5165
YS_CAR_IN=MessageRecvFormatServiceImpl,cContainerCar,YS_CAR_IN,CAR_DATA
YS_CAR_OUT=MessageRecvFormatServiceImpl,cContainerCar,YS_CAR_OUT,CAR_DATA
YS_CAR_PHOTO=YsCarPhotoMessageRecvFormatServiceImpl,cContainerCar,YS_CAR_PHOTO,CAR_DATA
