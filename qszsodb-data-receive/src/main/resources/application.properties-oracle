#设置上下文根 默认 “/”
#server.servlet.path=/sodb-ams
#设置访问端口 默认“8080”
server.port=11012
#文件上传大小配置
spring.http.multipart.maxFileSize=100Mb
spring.http.multipart.maxRequestSize=100Mb
spring.activiti.check-process-definitions=false
spring.autoconfigure.exclude=org.springframework.boot.autoconfigure.jdbc.DataSourceAutoConfiguration,org.springframework.boot.autoconfigure.jdbc.DataSourceTransactionManagerAutoConfiguration
db.type=oracle

#数据库连接配置：dts
#datasource.driverClassName=com.mysql.jdbc.Driver
datasource.driverClassName=oracle.jdbc.driver.OracleDriver
#datasource.url=************************************************************
datasource.url=*********************************************
datasource.username=dts
datasource.password=123456
datasource.initialSize=5
datasource.maxActive=300
datasource.maxIdle=10
datasource.minIdle=2
datasource.maxWait=60000
#datasource.validationQuery=select VERSION()
datasource.validationQuery=select 1 from dual
datasource.testOnBorrow=true
datasource.testOnReturn=true
datasource.timeBetweenEvictionRunsMillis=60000
datasource.minEvictableIdleTimeMillis=180000
datasource.removeAbandoned=true
datasource.removeAbandonedTimeout=250

##############################################################################################
#数据库连接配置：video
##############################################################################################
#video.datasource.driverClassName=com.mysql.jdbc.Driver
video.datasource.driverClassName=oracle.jdbc.driver.OracleDriver
#video.datasource.url=************************************************************
video.datasource.url=*********************************************
video.datasource.username=dts
video.datasource.password=123456
video.datasource.initialSize=5
video.datasource.maxActive=300
video.datasource.maxIdle=1
video.datasource.minIdle=2
video.datasource.maxWait=60000
#video.datasource.validationQuery=select VERSION()
video.datasource.validationQuery=select 1 from dual
video.datasource.testOnBorrow=true
video.datasource.testOnReturn=true
video.datasource.timeBetweenEvictionRunsMillis=60000
video.datasource.minEvictableIdleTimeMillis=180000
video.datasource.removeAbandoned=true
video.datasource.removeAbandonedTimeout=250

#redis配置
redis.host=************
#redis.host=**************
redis.port=6379
redis.pass=123456
redis.maxIdle=300
redis.maxTotal=200
redis.maxWaitMillis=2000
redis.testOnBorrow=true
redis.timeout=2000