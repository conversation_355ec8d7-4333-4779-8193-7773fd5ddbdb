package com.air.security.sodb.dts.receive.listener;

import com.air.security.sodb.data.core.base.Meta;
import com.air.security.sodb.data.core.constant.MsgIdConstant;
import com.air.security.sodb.data.core.mq.MyProducer;
import com.air.security.sodb.data.core.mq.impl.OrderRocketMQProducer;
import com.air.security.sodb.data.core.util.HaLog;
import com.air.security.sodb.data.core.util.KafkaConscumerUtil;
import com.air.security.sodb.data.core.util.ThreadUtil;
import com.air.security.sodb.dts.receive.server.MqMessageRecvServer;
import com.air.security.sodb.dts.receive.server.MqMessageRecvServerImpl;
import org.apache.commons.lang3.StringUtils;
import org.apache.kafka.clients.consumer.ConsumerRecord;
import org.apache.kafka.clients.consumer.ConsumerRecords;
import org.apache.kafka.clients.consumer.KafkaConsumer;
import org.slf4j.Logger;
import org.slf4j.LoggerFactory;

import java.time.Duration;


/**
 * @Description: mqs消息-rocketmq的顺序消息处理监听（航班单独处理）
 * @author: mk
 * @Date: 2018年12月18日
 */
public class MqsFlightForRocketMQOrderMsgListener implements Runnable {

    private static final Logger log = LoggerFactory.getLogger(MqsFlightForRocketMQOrderMsgListener.class);

    /**
     * 消费者主题
     */
    private String consumerTopic;

    private KafkaConsumer<String, String> consumer;

    /**
     * RocketMQ生产者
     */
    private MyProducer producer = new OrderRocketMQProducer();

    @Override
    public void run() {
        do {
            // 消费消息
            ConsumerRecords<String, String> records = consumer.poll(Duration.ZERO);
            for (ConsumerRecord<String, String> record : records) {
                String message = record.value();

                if (StringUtils.isBlank(message)) {
                    continue;
                }

                // 接收消息日志
                HaLog.infoJson(log, message);


                try {
                    // 获取消息头标签
                    Meta meta = new Meta();
                    meta.setEventType("flight");
                    MqMessageRecvServer server = new MqMessageRecvServerImpl(producer);
                    server.handle(meta, message);
                } catch (Exception e) {
                    HaLog.error(log, MsgIdConstant.MS_ERR_0001, e, "消息处理异常");
                }
            }
            ThreadUtil.sleepThreadUnit();
        } while (true);
    }

    public String getConsumerTopic() {
        return consumerTopic;
    }

    public void setConsumerTopic(String consumerTopic) {
        this.consumerTopic = consumerTopic;
        this.consumer = KafkaConscumerUtil.getConsumer(new String[]{this.consumerTopic});
    }

    public KafkaConsumer<String, String> getConsumer() {
        return consumer;
    }

    public void setConsumer(KafkaConsumer<String, String> consumer) {
        this.consumer = consumer;
    }

}
