package com.air.security.sodb.dts.srvc.restsrvc.receive.demo;

import com.air.security.sodb.data.core.constant.MsgIdConstant;
import com.air.security.sodb.data.core.util.HaLog;
import com.air.security.sodb.dts.srvc.domain.service.DemoService;
import com.google.common.base.Strings;
import org.slf4j.Logger;
import org.slf4j.LoggerFactory;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.web.bind.annotation.CrossOrigin;
import org.springframework.web.bind.annotation.RequestMapping;
import org.springframework.web.bind.annotation.RequestMethod;
import org.springframework.web.bind.annotation.RestController;

import javax.servlet.http.HttpServletRequest;

/**
 * demo服务
 *
 * <AUTHOR>
 */
@CrossOrigin
@RestController
@RequestMapping("/api/release/demoSearchRelease")
public class DemoSearchReleaseController {

    private static final Logger log = LoggerFactory.getLogger(DemoSearchReleaseController.class);

    @Autowired
    DemoService service;

    @RequestMapping(value = "/execute", method = RequestMethod.POST)
    public void execute(HttpServletRequest request) {
        HaLog.info(log, MsgIdConstant.MS_INF_0001, "demo查询服务");
        String ipAddress = getIpAddress(request);
        String asd ="2";
        try {
            HaLog.info(log, MsgIdConstant.MS_INF_0002, "ipAddress>>>"+ipAddress);
            HaLog.info(log, MsgIdConstant.MS_INF_0002, "demo查询服务>>>"+asd);

        } catch (Exception e) {
            HaLog.error(log, e, MsgIdConstant.MS_ERR_0001);
        }
    }

    public static String getIpAddress(HttpServletRequest request) {
        String Xip = request.getHeader("X-Real-IP");
        String XFor = request.getHeader("X-Forwarded-For");
        if (!Strings.isNullOrEmpty(XFor) && !"unKnown".equalsIgnoreCase(XFor)) {
            //多次反向代理后会有多个ip值，第一个ip才是真实ip
            int index = XFor.indexOf(",");
            if (index != -1) {
                return XFor.substring(0, index);
            } else {
                return XFor;
            }
        }
        XFor = Xip;
        if (!Strings.isNullOrEmpty(XFor) && !"unKnown".equalsIgnoreCase(XFor)) {
            return XFor;
        }
        if (Strings.nullToEmpty(XFor).trim().isEmpty() || "unknown".equalsIgnoreCase(XFor)) {
            XFor = request.getHeader("Proxy-Client-IP");
        }
        if (Strings.nullToEmpty(XFor).trim().isEmpty() || "unknown".equalsIgnoreCase(XFor)) {
            XFor = request.getHeader("WL-Proxy-Client-IP");
        }
        if (Strings.nullToEmpty(XFor).trim().isEmpty() || "unknown".equalsIgnoreCase(XFor)) {
            XFor = request.getHeader("HTTP_CLIENT_IP");
        }
        if (Strings.nullToEmpty(XFor).trim().isEmpty() || "unknown".equalsIgnoreCase(XFor)) {
            XFor = request.getHeader("HTTP_X_FORWARDED_FOR");
        }
        if (Strings.nullToEmpty(XFor).trim().isEmpty() || "unknown".equalsIgnoreCase(XFor)) {
            XFor = request.getRemoteAddr();
        }
        return XFor;
    }
}
