package com.air.security.sodb.dts.srvc.restsrvc.receive.mj;

import com.air.security.sodb.data.core.base.Meta;
import com.air.security.sodb.data.core.constant.MsgIdConstant;
import com.air.security.sodb.data.core.mq.MyProducer;
import com.air.security.sodb.data.core.mq.impl.OrderRocketMQProducer;
import com.air.security.sodb.data.core.util.*;
import com.air.security.sodb.dts.receive.server.MqMessageRecvServer;
import com.air.security.sodb.dts.receive.server.MqMessageRecvServerImpl;
import com.alibaba.fastjson.JSONArray;
import com.alibaba.fastjson.JSONObject;
import org.apache.commons.lang3.StringUtils;
import org.slf4j.Logger;
import org.slf4j.LoggerFactory;
import org.springframework.web.bind.annotation.CrossOrigin;
import org.springframework.web.bind.annotation.RequestMapping;
import org.springframework.web.bind.annotation.RequestMethod;
import org.springframework.web.bind.annotation.RestController;

/**
 * 门禁卡信息查询服务
 *
 * <AUTHOR>
 */
@CrossOrigin
@RestController
@RequestMapping("/api/release/cardMakeInfoRelease")
public class CardMakeInfoReleaseController {

    private static final String URL = PropertyUtil.getProperty("card_make_url");

    /**
     * RocketMQ生产者
     */
    private MyProducer producer = new OrderRocketMQProducer();

    private static int rowNum = 0;

    private static int totalRowNum = 0;

    private static int count = 0;


    private static final Logger log = LoggerFactory.getLogger(CardMakeInfoReleaseController.class);

    @RequestMapping(value = "/execute", method = RequestMethod.POST)
    public ResultVo execute() {
        HaLog.info(log, MsgIdConstant.MS_INF_0001, "门禁卡信息查询服务");
        try {
            JSONArray array = new JSONArray();
            int i = 0;
            doMakeInfo(array, URL, 1, 2000, i);
            if (null != array && array.size() > 0) {
                for (Object o : array) {
                    JSONObject jsonbject = (JSONObject) o;
                    Meta metaInfo = new Meta();
                    metaInfo.setEventType("card_make");
                    metaInfo.setSequence(UuidUtil.getUuid32());
                    MqMessageRecvServer server = new MqMessageRecvServerImpl(producer);
                    server.handle(metaInfo, jsonbject.toString());
                }
            }
            HaLog.info(log, MsgIdConstant.MS_INF_0002, "门禁卡信息查询服务");
            return ResultVo.success();
        } catch (Exception e) {
            HaLog.error(log, e, MsgIdConstant.MS_ERR_0001);
            return ResultVo.failure(new ResultMessage("门禁卡信息查询服务失败!"));
        }
    }

    /**
     * 循环查询门禁卡信息
     *
     * @param array
     * @param url   访问url
     * @param start
     * @param size
     */
    private void doMakeInfo(JSONArray array, String url, int start, int size, int i) {

        String param = "start=" + start + "&size=" + size;
        String s = HttpRequestUtil.sendGet(url, param);
        JSONObject json1 = JSONObject.parseObject(s);
        if (i == 0) {
            if (StringUtils.isNotBlank(json1.getString("size")) && StringUtils.isNotBlank(json1.getString("start"))) {
                rowNum = Integer.parseInt(json1.getString("size"));
                totalRowNum = Integer.parseInt(json1.getString("count"));
                count = (totalRowNum + rowNum - 1) / rowNum - 1;
            }
        }
        JSONArray jsonJsonArray = json1.getJSONArray("CardInfos");
        if (null != jsonJsonArray && jsonJsonArray.size() > 0) {
            array.addAll(jsonJsonArray);
        }
        if (i < count) {
            System.out.println(i);
            System.out.println(count);
            i++;
            this.doMakeInfo(array, url, i * 2000 + 1, 2000, i);
        }

    }

}
