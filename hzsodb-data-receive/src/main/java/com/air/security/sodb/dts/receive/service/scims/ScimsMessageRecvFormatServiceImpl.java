package com.air.security.sodb.dts.receive.service.scims;

import com.air.security.sodb.data.core.base.Meta;
import com.air.security.sodb.data.core.constant.GlobalCodeConstant;
import com.air.security.sodb.data.core.constant.MsgIdConstant;
import com.air.security.sodb.data.core.exception.SystemBaseException;
import com.air.security.sodb.data.core.util.HaAesCryptUtil;
import com.air.security.sodb.data.core.util.HaLog;
import com.air.security.sodb.dts.receive.service.AbstractMessageRecvService;
import com.alibaba.fastjson.JSONObject;
import org.apache.commons.lang3.StringUtils;
import org.slf4j.Logger;
import org.slf4j.LoggerFactory;
import org.springframework.stereotype.Service;

@Service("ScimsMessageRecvFormatServiceImpl")
public class ScimsMessageRecvFormatServiceImpl extends AbstractMessageRecvService {
    private static final Logger log = LoggerFactory.getLogger(ScimsMessageRecvFormatServiceImpl.class);

    @Override
    public void execute(Meta meta, String messageBody) throws SystemBaseException {
        HaLog.info(log, MsgIdConstant.MS_INF_0001, meta.getEventType());

        JSONObject jsons = new JSONObject();

        JSONObject json = JSONObject.parseObject(messageBody);

        JSONObject jsonObject = json.getJSONObject("MSG").getJSONObject("DATAINFO");

        if (jsonObject != null) {
            String lk_ajStates = jsonObject.getString("lk_ajStates");
            if (StringUtils.isBlank(lk_ajStates)|| !"1".equals(lk_ajStates)){
                HaLog.info(log, MsgIdConstant.MS_INF_0003, "安检状态为"+lk_ajStates);
                return;
            }

            String lk_card = jsonObject.getString("lk_card");
            if (StringUtils.isNotBlank(lk_card)){
                String crypt = HaAesCryptUtil.crypt(lk_card, GlobalCodeConstant.AES_KEY_PSGR);
                jsonObject.put("lk_card_num",crypt);
            }
        }
        // 响应消息头
        jsons.put("meta", meta);

        // 响应消息体
        jsons.put("body", jsonObject);

        // 转换为JSON格式，并发送消息到指定主题
        super.putSendMessage(jsons);
        HaLog.info(log, MsgIdConstant.MS_INF_0002, meta.getEventType());
    }
}
