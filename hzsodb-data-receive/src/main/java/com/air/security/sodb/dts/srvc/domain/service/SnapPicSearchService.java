package com.air.security.sodb.dts.srvc.domain.service;

import com.air.security.sodb.dts.srvc.restsrvc.platform.snap.dto.SnapResultDto;
import org.springframework.web.multipart.MultipartFile;

import java.io.File;

/**
 * @Description: 视频找人服务service
 * @author: wanglk
 * @Date: 2021/11/17 16:01
 */
public interface SnapPicSearchService {
    /**
     * 视频找人服务
     * @param queryVo
     * @param file
     * @return 结果
     */
    SnapResultDto findSnapPicList (MultipartFile file, String queryVo);
}
