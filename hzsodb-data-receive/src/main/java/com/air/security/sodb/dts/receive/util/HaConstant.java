package com.air.security.sodb.dts.receive.util;

import org.springframework.beans.factory.annotation.Value;
import org.springframework.http.MediaType;
import org.springframework.stereotype.Component;

import java.nio.charset.Charset;

/**
 * 公用常量类
 *
 * <AUTHOR>
 */
@Component
public class HaConstant {

    /**
     * 组件框架服务访问地址
     */
    @Value("${sf.url}")
    private String sfUrl;

    public String getSfUrl() {
        return sfUrl;
    }

    public void setSfUrl(String sfUrl) {
        this.sfUrl = sfUrl;
    }

    /**
     * 删除状态：已删除
     */
    public static final String DELETE_FLAG_YES = "1";

    /**
     * 删除状态：未删除
     */
    public static final String DELETE_FLAG_NO = "0";

    /**
     * 启用
     */
    public static final String SYS_CODE_ENABLE = "0";
    public static final String SYS_CODE_ENABLE_NAME = "启用";

    /**
     * 停用
     */
    public static final String SYS_CODE_DISABLE = "1";
    public static final String SYS_CODE_DISABLE_NAME = "停用";

    /**
     * 否
     */
    public static final String SYS_CODE_NO = "0";
    public static final String SYS_CODE_NO_NAME = "否";

    /**
     * 是
     */
    public static final String SYS_CODE_YES = "1";
    public static final String SYS_CODE_YES_NAME = "是";

    /**
     * 取消发布
     */
    public static final String SYS_CODE_CANCEL_PUBLISH = "0";
    public static final String SYS_CODE_CANCEL_PUBLISH_NAME = "取消发布";

    /**
     * 发布
     */
    public static final String SYS_CODE_PUBLISH = "1";
    public static final String SYS_CODE_PUBLISH_NAME = "发布";

    /**
     * 操作结果-0：失败
     */
    public static final String OP_RESULT_FAILED = "0";
    public static final String OP_RESULT_FAILED_NAME = "失败";

    /**
     * 操作结果-1：成功
     */
    public static final String OP_RESULT_SUCCESS = "1";
    public static final String OP_RESULT_SUCCESS_NAME = "成功";

    /**
     * 操作类别-add：增加
     */
    public static final String OP_TYPE_ADD = "add";

    /**
     * 操作类别-upload：上传
     */
    public static final String OP_TYPE_UPLOAD = "upload";

    /**
     * 操作类别-download：下载
     */
    public static final String OP_TYPE_DOWNLOAD = "download";

    /**
     * 操作类别-delete：删除
     */
    public static final String OP_TYPE_DELETE = "delete";

    /**
     * 操作类别-modify：修改
     */
    public static final String OP_TYPE_MODIFY = "modify";

    /**
     * 操作类别-loginon 用户登录
     */
    public static final String OP_TYPE_LOGIN_ON = "loginon";

    /**
     * 操作类别-loginout 用户退出登录
     */
    public static final String OP_TYPE_LOGIN_OUT = "loginout";

    /**
     * 用户登录key
     */
    public static final String HAYC_USER_LOGIN_KEY = "HAYC_USER_LOGIN_KEY";

    public static final Integer BASE_ERROR_CODE = 10000;
    public static final String BASE_ERROR_CODE_NAME = "错误";

    public static final Integer LOGIN_ERROR_CODE = 11000;
    public static final String LOGIN_ERROR_CODE_NAME = "登录错误";

    public static final Integer SAVE_ERROR_CODE = 12001;
    public static final String SAVE_ERROR_CODE_NAME = "添加错误";

    public static final Integer UPDATE_ERROR_CODE = 12002;
    public static final String UPDATE_ERROR_CODE_NAME = "编辑错误";

    public static final Integer DELETE_ERROR_CODE = 12003;
    public static final String DELETE_ERROR_CODE_NAME = "删除错误";

    public static final Integer SEARCH_ERROR_CODE = 12004;
    public static final String SEARCH_ERROR_CODE_NAME = "查询错误";

    public static final Integer PARAMS_ERROR_CODE = 12005;
    public static final String PARAMS_ERROR_CODE_NAME = "参数错误";

    /**
     * 请求成功
     */
    public static final Integer HA_API_RESULT_CODE_SUCCESS = 200;
    public static final String HA_API_RESULT_CODE_SUCCESS_MSG = "SUCCESS";

    /**
     * 请求失败
     */
    public static final Integer HA_API_RESULT_CODE_FAILED = -1;
    public static final String HA_API_RESULT_CODE_FAILED_MSG = "FAILED";

    /**
     * 请求失败，Not Modified 禁止修改
     */
    public static final Integer HA_API_RESULT_CODE_NOT_MODIFIED = 304;
    public static final String HA_API_RESULT_CODE_NOT_MODIFIED_MSG = "Not Modified";

    /**
     * 请求失败，Bad Request 错误请求
     */
    public static final Integer HA_API_RESULT_CODE_BAD_REQUEST = 400;
    public static final String HA_API_RESULT_CODE_BAD_REQUEST_MSG = "Bad Request";

    /**
     * 请求失败，Unauthorized 未授权
     */
    public static final Integer HA_API_RESULT_CODE_UNAUTHORIZED = 401;
    public static final String HA_API_RESULT_CODE_UNAUTHORIZED_MSG = "Unauthorized";

    /**
     * 请求失败，Forbidden 权限不足  禁止访问
     */
    public static final Integer HA_API_RESULT_CODE_FORBIDDEN = 403;
    public static final String HA_API_RESULT_CODE_FORBIDDEN_MSG = "Forbidden";

    /**
     * 请求失败，Not Found
     */
    public static final Integer HA_API_RESULT_CODE_NOT_FOUND = 404;
    public static final String HA_API_RESULT_CODE_NOT_FOUND_MSG = "Not Found";

    /**
     * 请求失败，Internal Server Error 系统内部错误
     */
    public static final Integer HA_API_RESULT_CODE_INTERNAL_SERVER_ERROR = 500;
    public static final String HA_API_RESULT_CODE_INTERNAL_SERVER_ERROR_MSG = "Internal Server Error";

    /**
     * 请求失败，Service Unavailable 服务不可用
     */
    public static final Integer HA_API_RESULT_CODE_SERVICE_UNAVAILABLE = 503;
    public static final String HA_API_RESULT_CODE_SERVICE_UNAVAILABLE_MSG = "Service Unavailable";

    /**
     * 该账号已在其他客户端登录，您已被强制下线！
     */
    public static final Integer HA_API_RESULT_CODE_LOGIN_OFFLINE = 11001;
    public static final String HA_API_RESULT_CODE_LOGIN_OFFLINE_MSG = "该账号已在其他客户端登录，您已被强制下线！";

    /**
     * 许可证书校验失败！
     */
    public static final Integer HA_API_RESULT_CODE_LOGIN_LICENSE = 11002;
    public static final String HA_API_RESULT_CODE_LOGIN_LICENSE_MSG = "许可证书校验失败！";

    /**
     * 请求失败，尚未登录或登录已失效
     */
    public static final Integer HA_API_RESULT_CODE_LOGIN_ERROR = 11000;
    public static final String HA_API_RESULT_CODE_LOGIN_ERROR_MSG = "尚未登录或登录已失效！";

    /**
     * 请求失败，请求参数错误
     */
    public static final Integer HA_API_RESULT_CODE_PARAMS_ERROR = 12005;
    public static final String HA_API_RESULT_CODE_PARAMS_ERROR_MSG = "请求参数错误！";

    /**
     * 全局编码UTF-8
     */
    public static final Charset GLOBAL_CHARSET = Charset.forName(HaConstant.GLOBAL_CHARSET_STR);

    /**
     * 全局编码UTF-8
     */
    public static final String GLOBAL_CHARSET_STR = "UTF-8";

    /**
     * MediaType json utf8
     */
    public static final MediaType APPLICATION_JSON_UTF8 = MediaType.valueOf("application/json;charset=UTF-8");

    /**
     * kafka消息推送
     */
    public static final String KAFKA_MESSAGE_SEND_CODE = "0";
    public static final String KAFKA_MESSAGE_SEND_NAME = "推送";

    /**
     * kafka消息接收
     */
    public static final String KAFKA_MESSAGE_RECEIVE_CODE = "1";
    public static final String KAFKA_MESSAGE_RECEIVE_NAME = "接收";

    /**
     * 定时任务运行状态：未运行
     */
    public static final String QUARTZ_STATE_NONE = "NONE";

    /**
     * 定时任务运行状态：正常运行
     */
    public static final String QUARTZ_STATE_NORMAL = "NORMAL";

    /**
     * 系统消息接收人类型：1 个人
     */
    public final static String MESSAGE_RECEIVE_TYPE_PERSON = "1";

    /**
     * 系统消息接收人类型：2 角色
     */
    public final static String MESSAGE_RECEIVE_TYPE_ROLE = "2";

    /**
     * 系统消息接收人类型：3 所有人
     */
    public final static String MESSAGE_RECEIVE_TYPE_ALL = "3";

    /**
     * 系统消息接收人类型：4 部门
     */
    public final static String MESSAGE_RECEIVE_TYPE_DEPT = "4";

    /**
     * 操作敏感度  OSC01 普通操作
     */
    public final static String OP_SENSITIVE_COMMON_CODE = "OSC01";
    public final static String OP_SENSITIVE_COMMON_NAME = "普通操作";

    /**
     * 操作敏感度  OSC02 敏感操作
     */
    public final static String OP_SENSITIVE_SENS_CODE = "OSC02";
    public final static String OP_SENSITIVE_SENS_NAME = "敏感操作";


}