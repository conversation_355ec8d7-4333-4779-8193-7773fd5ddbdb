package com.air.security.sodb.dts.receive.util;

import org.springframework.web.client.RestTemplate;

/**
 * 获取restTemplate bean工具类
 *
 * <AUTHOR>
 */
public class RestTemplateUtil {

    private static RestTemplate restTemplate;

    private static final Object LOCK = new Object();

    public static RestTemplate getInstance() {
        synchronized (LOCK) {
            if (restTemplate == null) {
                restTemplate = (RestTemplate) BeanUtils.getBean("restTemplate");
            }
        }
        return restTemplate;
    }
}