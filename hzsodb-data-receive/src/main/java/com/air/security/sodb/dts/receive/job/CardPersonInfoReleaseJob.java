package com.air.security.sodb.dts.receive.job;

import com.air.security.sodb.data.core.base.Meta;
import com.air.security.sodb.data.core.constant.MsgIdConstant;
import com.air.security.sodb.data.core.exception.SystemBaseException;
import com.air.security.sodb.data.core.mq.MyProducer;
import com.air.security.sodb.data.core.mq.impl.OrderRocketMQProducer;
import com.air.security.sodb.data.core.quartz.AbstractJob;
import com.air.security.sodb.data.core.util.HaLog;
import com.air.security.sodb.data.core.util.HttpRequestUtil;
import com.air.security.sodb.data.core.util.PropertyUtil;
import com.air.security.sodb.data.core.util.UuidUtil;
import com.air.security.sodb.dts.receive.server.MqMessageRecvServer;
import com.air.security.sodb.dts.receive.server.MqMessageRecvServerImpl;
import com.alibaba.fastjson.JSONArray;
import com.alibaba.fastjson.JSONObject;
import org.quartz.JobDataMap;
import org.slf4j.Logger;
import org.slf4j.LoggerFactory;

/**
 * 门禁人员信息服务
 *
 * <AUTHOR>
 */

public class CardPersonInfoReleaseJob  extends AbstractJob {

    private static final String URL = PropertyUtil.getProperty("card_person_url");

    /**
     * RocketMQ生产者
     */
    private MyProducer producer = new OrderRocketMQProducer();

    private static int rowNum = 0;

    private static int totalRowNum = 0;

    private static int count = 0;


    private static final Logger log = LoggerFactory.getLogger(CardPersonInfoReleaseJob.class);

    /**
     * 循环查询门禁卡信息
     *
     * @param url
     * @param start
     * @param size
     */
    private void doMakeInfo(String url, int start, int size, int i) {
        String param = "start=" + start + "&size=" + size;
        String s = HttpRequestUtil.sendGet(url, param);
        JSONObject json1 = JSONObject.parseObject(s);
        JSONArray jsonJsonArray = json1.getJSONArray("PersonInfos");
        if (null == jsonJsonArray || jsonJsonArray.size() == 0) {
            return;
        }
        for (Object o : jsonJsonArray) {
            JSONObject jsonbject = (JSONObject) o;
            Meta metaInfo = new Meta();
            metaInfo.setEventType("card_person");
            metaInfo.setRecvSequence(UuidUtil.getUuid32());
            MqMessageRecvServer server = new MqMessageRecvServerImpl(producer);
            server.handle(metaInfo, jsonbject.toString());
        }
        i++;
        this.doMakeInfo(url, i * 100 + 1, 100, i);

    }

    @Override
    public void executeJob(JobDataMap paramMap) {
        HaLog.info(log, MsgIdConstant.MS_INF_0001, "门禁人员信息查询服务");
        try {
            int i = 0;
            doMakeInfo(URL, 1, 100, i);
        } catch (Exception e) {
            throw new SystemBaseException("门禁人员信息查询服务", e);
        } finally {
            HaLog.info(log, MsgIdConstant.MS_INF_0002, "门禁人员信息查询服务");
        }
    }
}
