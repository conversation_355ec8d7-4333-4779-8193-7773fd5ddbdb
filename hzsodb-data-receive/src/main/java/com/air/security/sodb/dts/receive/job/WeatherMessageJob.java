package com.air.security.sodb.dts.receive.job;

import com.air.security.sodb.data.core.base.Meta;
import com.air.security.sodb.data.core.constant.MsgIdConstant;
import com.air.security.sodb.data.core.mq.MyProducer;
import com.air.security.sodb.data.core.mq.impl.OrderRocketMQProducer;
import com.air.security.sodb.data.core.quartz.AbstractJob;
import com.air.security.sodb.data.core.util.*;
import com.air.security.sodb.dts.receive.server.MqMessageRecvServer;
import com.air.security.sodb.dts.receive.server.MqMessageRecvServerImpl;
import com.air.security.sodb.dts.receive.util.XmlToJsonUtils;
import com.alibaba.fastjson.JSONObject;
import org.apache.commons.lang3.StringUtils;
import org.quartz.JobDataMap;
import org.slf4j.Logger;
import org.slf4j.LoggerFactory;
import org.springframework.http.ResponseEntity;

/**
 * 天气定时任务
 */
public class WeatherMessageJob extends AbstractJob {

    private static final Logger log = LoggerFactory.getLogger(WeatherMessageJob.class);

    private static final String URL = PropertyUtil.getProperty("weather_url");

    /**
     * RocketMQ生产者
     */
    private MyProducer producer = new OrderRocketMQProducer();

    @Override
    public void executeJob(JobDataMap paramMap) {
        HaLog.info(log, MsgIdConstant.MS_INF_0001, "天气数据采集任务");
        try {
            ResponseEntity<ResultVo> enitty = RestTemplateUtils.get(URL, ResultVo.class);
            ResultVo body = enitty.getBody();
            String message = (String) body.getData();
            if (StringUtils.isBlank(message)) {
                return;
            }
            String json = XmlToJsonUtils.xml2json(message);
            JSONObject jsonObject = JSONObject.parseObject(json);
            if (jsonObject == null) {
                return;
            }
            // 获取消息头标签
            Meta meta = new Meta();
            meta.setEventType("weather_info");
            meta.setRecvSequence(UuidUtil.getUuid32());
            Object o = jsonObject.getJSONObject("MSG").getJSONObject("WeatherData").get("Weather_Info");
            MqMessageRecvServer server = new MqMessageRecvServerImpl(producer);
            server.handle(meta, o.toString());

        } catch (Exception e) {
            HaLog.error(log, MsgIdConstant.MS_ERR_0001, e, "");
        } finally {
            HaLog.info(log, MsgIdConstant.MS_INF_0002, "天气数据采集任务");
        }
    }

}
