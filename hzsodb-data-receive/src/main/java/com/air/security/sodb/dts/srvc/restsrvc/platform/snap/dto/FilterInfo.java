package com.air.security.sodb.dts.srvc.restsrvc.platform.snap.dto;

import io.swagger.annotations.ApiModelProperty;

/**
 * @Description:
 * @author: wanglk
 * @Date: 2021/11/17 15:43
 */
public class FilterInfo {
    /**
     * 年龄最大值（0-300） 标准 1-6：儿童 7-17：少年 18-40：青年 41-65：中年 66-250：老年 0-0：未知
     */
    @ApiModelProperty(value = "年龄最大值（0-300）")
    private int MaxAge;

    /**
     * 年龄最小值
     */
    @ApiModelProperty(value = "年龄最小值")
    private int MinAge;

    /**
     * 相似度（阈值）0~100
     */
    @ApiModelProperty(value = "相似度（阈值）0~100")
    private int Similarity;

    public int getMaxAge () {
        return MaxAge;
    }

    public void setMaxAge (int maxAge) {
        MaxAge = maxAge;
    }

    public int getMinAge () {
        return MinAge;
    }

    public void setMinAge (int minAge) {
        MinAge = minAge;
    }

    public int getSimilarity () {
        return Similarity;
    }

    public void setSimilarity (int similarity) {
        Similarity = similarity;
    }
}
