package com.air.security.sodb.dts.receive.job;

import com.air.security.sodb.data.core.base.Meta;
import com.air.security.sodb.data.core.constant.MsgIdConstant;
import com.air.security.sodb.data.core.exception.SystemBaseException;
import com.air.security.sodb.data.core.mq.MyProducer;
import com.air.security.sodb.data.core.mq.impl.OrderRocketMQProducer;
import com.air.security.sodb.data.core.quartz.AbstractJob;
import com.air.security.sodb.data.core.util.HaLog;
import com.air.security.sodb.data.core.util.PropertyUtil;
import com.air.security.sodb.data.core.util.RestTemplateUtils;
import com.air.security.sodb.data.core.util.UuidUtil;
import com.air.security.sodb.dts.receive.server.MqMessageRecvServer;
import com.air.security.sodb.dts.receive.server.MqMessageRecvServerImpl;
import com.air.security.sodb.dts.receive.util.HaBaseUtils;
import com.air.security.sodb.dts.receive.util.ResponseData;
import com.air.security.sodb.dts.receive.util.YsEquInfo;
import com.alibaba.fastjson.JSONObject;
import org.quartz.JobDataMap;
import org.slf4j.Logger;
import org.slf4j.LoggerFactory;
import org.springframework.http.ResponseEntity;

import java.util.List;

/**
 * <AUTHOR>
 * @date 2021/11/27
 * 定时任务-根据摄像机编码获取人数统计
 */
public class CameraPersonStatisticsJob extends AbstractJob {

    private static final Logger log = LoggerFactory.getLogger(CameraPersonStatisticsJob.class);

    private static String URL = PropertyUtil.getProperty("yushi.camera.person.statistics.url");
    private static String EVENT_TYPE = PropertyUtil.getProperty("camera.person.statistics.event.type");

    private static String VIDEO_EQU_URL = PropertyUtil.getProperty("sodb.video.equ.status.req.url");

    private MyProducer producer = new OrderRocketMQProducer();

    @Override
    public void executeJob(JobDataMap paramMap) {
        HaLog.info(log, MsgIdConstant.MS_INF_0001, "根据摄像机编码获取人数统计定时任务");

        try {
            ResponseData haObj = HaBaseUtils.getInstance().getHaObj(null, VIDEO_EQU_URL, ResponseData.class, null);
            List<YsEquInfo> cacheList = haObj.getDatalist();
            for (YsEquInfo ysEquInfo : cacheList) {
                execPost(URL, ysEquInfo);
            }
        } catch (Exception e) {
            throw new SystemBaseException("根据摄像机编码获取人数统计失败", e);
        } finally {
            HaLog.info(log, MsgIdConstant.MS_INF_0002, "根据摄像机编码获取人数统计定时任务");
        }
    }

    /**
     * @param url
     * @param ysEquInfo
     */
    private void execPost(String url, YsEquInfo ysEquInfo) {
        JSONObject searchCondition = new JSONObject();
        searchCondition.put("camCode", ysEquInfo.getEquCode());
        ResponseEntity<JSONObject> result = RestTemplateUtils.post(url, JSONObject.class);
        String errCode = result.getBody().get("errCode").toString();
        if (!"0".equals(errCode)) {
            HaLog.info(log, MsgIdConstant.MS_ERR_0001, "根据摄像机编码获取人数统计远程调用失败，返回结果：" + result.getBody().get("errMsg").toString());
        }
        JSONObject sendMessage = new JSONObject();
        sendMessage.put("camCode", ysEquInfo.getEquCode());
        sendMessage.put("camName", ysEquInfo.getEquName());
        sendMessage.put("data", result.getBody().get("data"));

        // 获取消息头标签
        Meta meta = new Meta();
        meta.setEventType(EVENT_TYPE);
        meta.setRecvSequence(UuidUtil.getUuid32());

        MqMessageRecvServer server = new MqMessageRecvServerImpl(producer);
        server.handle(meta, sendMessage.toString());

    }
}
