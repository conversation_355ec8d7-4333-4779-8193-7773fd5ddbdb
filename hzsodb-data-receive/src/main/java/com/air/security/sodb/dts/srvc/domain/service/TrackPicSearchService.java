package com.air.security.sodb.dts.srvc.domain.service;

import com.air.security.sodb.dts.srvc.restsrvc.platform.track.TrackResultDto;
import org.springframework.web.multipart.MultipartFile;

import java.util.List;

/**
 * @Description: 以图找图服务service
 * @author: mk
 * @Date: 2021/11/17 16:01
 */
public interface TrackPicSearchService {
    /**
     * 以图找图服务
     * @param queryVo
     * @param file
     * @return 结果
     */
    List<TrackResultDto> findTrackPicList (MultipartFile file, String queryVo);
}
