package com.air.security.sodb.dts.receive.util;

import com.air.security.sodb.data.core.util.HttpRequestUtil;
import com.air.security.sodb.data.core.util.PropertyUtil;
import com.alibaba.fastjson.JSONArray;
import com.alibaba.fastjson.JSONObject;

import java.util.HashMap;
import java.util.List;
import java.util.Map;

public class YsEquInfoCache {

    private static String videoEquinfo = PropertyUtil.getProperty("sodb.video.equ.status.req.url");

    private static Map<String, String> map = new HashMap<>();
    ;

    public static Map<String, String> getCacheList() {
        if (map.size() == 0 || map.isEmpty()) {
            String result = HttpRequestUtil.sendPost(videoEquinfo, "");
            JSONObject resJson = JSONObject.parseObject(result);
            JSONArray cacheArray = resJson.getJSONArray("datalist");
            List<YsEquInfo> cacheList = JSONArray.parseArray(cacheArray.toJSONString(), YsEquInfo.class);
            for (YsEquInfo ysEquInfo : cacheList) {
                map.put(ysEquInfo.getEquCode(), ysEquInfo.getTimeStateId());
            }
        }
        return map;
    }

}
