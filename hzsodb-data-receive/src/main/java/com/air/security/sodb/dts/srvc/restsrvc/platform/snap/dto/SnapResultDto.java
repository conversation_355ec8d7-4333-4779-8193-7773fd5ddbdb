package com.air.security.sodb.dts.srvc.restsrvc.platform.snap.dto;

import io.swagger.annotations.ApiModelProperty;

import java.util.List;

/**
 * @Description: 视频找人返回数据DTO
 * @author: wanglk
 * @Date: 2021/11/17 16:43
 */
public class SnapResultDto {
    /**
     * 返回数据总数
     */
    @ApiModelProperty(value = "返回数据总数")
    private int TotalNum;

    /**
     * 抓拍图片列表
     */
    @ApiModelProperty(value = "抓拍图片列表")
    private List<SnapPicInfoDto> picList;

    public int getTotalNum () {
        return TotalNum;
    }

    public void setTotalNum (int totalNum) {
        TotalNum = totalNum;
    }

    public List<SnapPicInfoDto> getPicList () {
        return picList;
    }

    public void setPicList (List<SnapPicInfoDto> picList) {
        this.picList = picList;
    }
}
