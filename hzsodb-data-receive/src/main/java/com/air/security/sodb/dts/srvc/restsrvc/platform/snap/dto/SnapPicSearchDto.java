package com.air.security.sodb.dts.srvc.restsrvc.platform.snap.dto;

import io.swagger.annotations.ApiModelProperty;

import java.util.List;

/**
 * @Description: 视频找人 dto
 * @author: wanglk
 * @Date: 2021/11/17 15:09
 */
public class SnapPicSearchDto {
    /**
     * 是否获取特征值（计算相似度图片必须传true）
     */
    @ApiModelProperty(value = "是否获取特征值（计算相似度图片必须传true）")
    private Boolean NeedGetFaceFeature;

    /**
     * 排序规则0：降序 1：升序
     */
    @ApiModelProperty(value = "排序规则0：降序 1：升序")
    private int Order;

    /**
     * 查询结果总条数
     */
    @ApiModelProperty(value = "查询结果总条数")
    private int LimitNum;

    /**
     * 抓拍机编码数组，不写数据，默认查询全部
     */
    @ApiModelProperty(value = "抓拍机编码数组，不写数据，默认查询全部")
    private List<String> ListCamID;

    /**
     * 基本条件信息
     */
    @ApiModelProperty(value = "基本条件信息")
    private FaceBasicInfo FaceBasicInfo;

    /**
     * 过滤信息
     */
    @ApiModelProperty(value = "过滤信息")
    private FilterInfo FilterInfo;

    /**
     * 时间区段
     */
    @ApiModelProperty(value = "时间区段")
    private TimeScale TimeScale;

    /**
     * 时间区段
     */
    @ApiModelProperty(value = "分页信息")
    private QueryPageInfo QueryPageInfo;

    /**
     * 检索类型 0：抓拍检索
     */
    @ApiModelProperty(value = "检索类型 0：抓拍检索")
    private String SnapType;

    public Boolean getNeedGetFaceFeature () {
        return NeedGetFaceFeature;
    }

    public void setNeedGetFaceFeature (Boolean needGetFaceFeature) {
        NeedGetFaceFeature = needGetFaceFeature;
    }

    public int getOrder () {
        return Order;
    }

    public void setOrder (int order) {
        Order = order;
    }

    public int getLimitNum () {
        return LimitNum;
    }

    public void setLimitNum (int limitNum) {
        LimitNum = limitNum;
    }

    public List<String> getListCamID () {
        return ListCamID;
    }

    public void setListCamID (List<String> listCamID) {
        ListCamID = listCamID;
    }

    public com.air.security.sodb.dts.srvc.restsrvc.platform.snap.dto.FaceBasicInfo getFaceBasicInfo () {
        return FaceBasicInfo;
    }

    public void setFaceBasicInfo (com.air.security.sodb.dts.srvc.restsrvc.platform.snap.dto.FaceBasicInfo faceBasicInfo) {
        FaceBasicInfo = faceBasicInfo;
    }

    public com.air.security.sodb.dts.srvc.restsrvc.platform.snap.dto.FilterInfo getFilterInfo () {
        return FilterInfo;
    }

    public void setFilterInfo (com.air.security.sodb.dts.srvc.restsrvc.platform.snap.dto.FilterInfo filterInfo) {
        FilterInfo = filterInfo;
    }

    public com.air.security.sodb.dts.srvc.restsrvc.platform.snap.dto.TimeScale getTimeScale () {
        return TimeScale;
    }

    public void setTimeScale (com.air.security.sodb.dts.srvc.restsrvc.platform.snap.dto.TimeScale timeScale) {
        TimeScale = timeScale;
    }

    public com.air.security.sodb.dts.srvc.restsrvc.platform.snap.dto.QueryPageInfo getQueryPageInfo () {
        return QueryPageInfo;
    }

    public void setQueryPageInfo (com.air.security.sodb.dts.srvc.restsrvc.platform.snap.dto.QueryPageInfo queryPageInfo) {
        QueryPageInfo = queryPageInfo;
    }

    public String getSnapType () {
        return SnapType;
    }

    public void setSnapType (String snapType) {
        SnapType = snapType;
    }
}
