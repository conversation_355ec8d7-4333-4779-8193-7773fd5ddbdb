package com.air.security.sodb.dts;

import com.air.security.sodb.data.core.config.KafkaLinstenerConfig;
import com.air.security.sodb.data.core.constant.MsgIdConstant;
import com.air.security.sodb.data.core.quartz.SchedulerManager;
import com.air.security.sodb.data.core.util.*;
import com.air.security.sodb.dts.receive.listener.RocketMQOrderMsgListener;
import org.slf4j.Logger;
import org.slf4j.LoggerFactory;
import org.springframework.boot.ApplicationArguments;
import org.springframework.boot.ApplicationRunner;
import org.springframework.boot.SpringApplication;
import org.springframework.boot.autoconfigure.SpringBootApplication;
import org.springframework.context.annotation.ImportResource;

import javax.annotation.PostConstruct;
import javax.net.ssl.*;
import java.lang.reflect.Method;
import java.util.List;

/**
 * <AUTHOR> @Description 杭州sodb数据接入服务
 */
@ImportResource("classpath:/applicationContext.xml")
@SpringBootApplication(scanBasePackages = "com.air.security.sodb")
public class HzSodbRecvApplication implements ApplicationRunner {

    private static final Logger log = LoggerFactory.getLogger(HzSodbRecvApplication.class);

    private static final String BUCKET = PropertyUtil.getProperty("bucket");

    public static void main(String[] args) {
        SpringApplication.run(HzSodbRecvApplication.class, args);
    }

    /**
     * 初始化
     *
     * @param args
     * @throws Exception
     */
    @Override
    public void run(ApplicationArguments args) throws Exception {
        MinioUtil.makeBucket(BUCKET);
        // 启动定时任务
        runQuartzJob();
        start();
    }

    public static void start() {

        try {
            List<KafkaLinstenerConfig> list = KafkaListenerConfigContextUtil.getContextParam();
            for (KafkaLinstenerConfig config : list) {
                Class<?> clazz = Class.forName(config.getListenerClass());
                Runnable kafkaListener = (Runnable) clazz.newInstance();
                Method setConsumerMethod = clazz.getDeclaredMethod("setConsumerTopic", String.class);
                setConsumerMethod.invoke(kafkaListener, config.getTopic());
                if (kafkaListener instanceof RocketMQOrderMsgListener) {
                    Method setConsumerGroupMethod = clazz.getDeclaredMethod("setConsumerGroup", String.class);
                    setConsumerGroupMethod.invoke(kafkaListener, config.getTopic());
                }
                HaLog.info(log, MsgIdConstant.MS_INF_0001, config.getTopic() + "监听");
                ThreadUtil.runWithNewThread(kafkaListener);
                HaLog.info(log, MsgIdConstant.MS_INF_0002, config.getTopic() + "监听");
            }
        } catch (Exception e) {
            HaLog.error(log, MsgIdConstant.MS_ERR_0001, e, "消息处理异常");
        }

    }

    @PostConstruct
    public void init() {
        try{
            TrustManager[] trustAllCerts = new TrustManager[] {new X509TrustManager() {
                @Override
                public void checkClientTrusted(java.security.cert.X509Certificate[] x509Certificates, String s){
                }
                @Override
                public void checkServerTrusted(java.security.cert.X509Certificate[] x509Certificates, String s){
                }
                @Override
                public java.security.cert.X509Certificate[] getAcceptedIssuers() {
                    return null;
                }
            }
            };
            SSLContext sc = SSLContext.getInstance("SSL");
            sc.init(null, trustAllCerts, new java.security.SecureRandom());
            HttpsURLConnection.setDefaultSSLSocketFactory(sc.getSocketFactory());

            HostnameVerifier allHostsValid = (hostname, session) -> true;
            HttpsURLConnection.setDefaultHostnameVerifier(allHostsValid);
        } catch (Exception e) {
            HaLog.error(log,"项目初始化失败",e);
        }
    }


    /**
     * 启动定时任务
     */
    private static void runQuartzJob() {
        HaLog.info(log, MsgIdConstant.MS_INF_0001, "quartz定时任务");
        SchedulerManager manager = new SchedulerManager();
        manager.runJob("quartzJob.xml");
        HaLog.info(log, MsgIdConstant.MS_INF_0002, "quartz定时任务");
    }

}
