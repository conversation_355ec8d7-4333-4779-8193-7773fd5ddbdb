package com.air.security.sodb.dts.receive.service.card;

import com.air.security.sodb.data.core.base.Meta;
import com.air.security.sodb.data.core.constant.MsgIdConstant;
import com.air.security.sodb.data.core.exception.SystemBaseException;
import com.air.security.sodb.data.core.util.HaLog;
import com.air.security.sodb.dts.receive.service.AbstractMessageRecvService;
import com.alibaba.fastjson.JSONObject;
import org.slf4j.Logger;
import org.slf4j.LoggerFactory;
import org.springframework.stereotype.Service;

/**
 * <AUTHOR> @Description:刷卡数据信息处理
 **/
@Service("MjCardMessageRecvFormatServiceImpl")
public class MjCardMessageRecvFormatServiceImpl extends AbstractMessageRecvService {
    private static final Logger log = LoggerFactory.getLogger(MjCardMessageRecvFormatServiceImpl.class);

    @Override
    public void execute(Meta meta, String messageBody) throws SystemBaseException {
        HaLog.info(log, MsgIdConstant.MS_INF_0001, meta.getEventType());

        JSONObject jsons = new JSONObject();


        JSONObject json = JSONObject.parseObject(messageBody);

        // 响应消息头
        jsons.put("meta", meta);

        // 响应消息体
        jsons.put("body", json);

        // 转换为JSON格式，并发送消息到指定主题
        super.putSendMessage(jsons);
        HaLog.info(log, MsgIdConstant.MS_INF_0002, meta.getEventType());
    }
}
