package com.air.security.sodb.dts.srvc.restsrvc.platform.snap.dto;

import io.swagger.annotations.ApiModelProperty;

/**
 * @Description:
 * @author: wanglk
 * @Date: 2021/11/17 15:26
 */
public class FaceBasicInfo {
    /**
     * 性别 0：女 1：男 9：全部 98：未知
     */
    @ApiModelProperty(value = "性别 0：女 1：男 9：全部 98：未知")
    private int Gender;

    /**
     * 民族
     */
    @ApiModelProperty(value = "民族")
    private int Nation;

    /**
     * 是否带眼镜 0：否 1：是
     */
    @ApiModelProperty(value = "是否带眼镜 0：否 1：是 9：全部 98：未知")
    private int Glasses;

    /**
     * 是否微笑 0：是 1：否 9：全部 98：未知
     */
    @ApiModelProperty(value = "是否微笑 0：是 1：否 9：全部 98：未知")
    private int Smile;

    /**
     * 是否戴口罩，取值为 0：没戴口罩； 1：戴口罩； 9：全部； 98：未知
     */
    @ApiModelProperty(value = "是否戴口罩，取值为 0：没戴口罩； 1：戴口罩； 9：全部； 98：未知")
    private int MaskFlag;

    public int getGender () {
        return Gender;
    }

    public void setGender (int gender) {
        Gender = gender;
    }

    public int getNation () {
        return Nation;
    }

    public void setNation (int nation) {
        Nation = nation;
    }

    public int getGlasses () {
        return Glasses;
    }

    public void setGlasses (int glasses) {
        Glasses = glasses;
    }

    public int getSmile () {
        return Smile;
    }

    public void setSmile (int smile) {
        Smile = smile;
    }

    public int getMaskFlag () {
        return MaskFlag;
    }

    public void setMaskFlag (int maskFlag) {
        MaskFlag = maskFlag;
    }
}
