<?xml version="1.0" encoding="UTF-8"?>
<!--定时任务配置文件： -->
<quartz>


    <job>
        <job-detail>
            <name>ysPeopleSumCountMessage-Job</name>
            <group>ysPeopleSumCountMessageGroup</group>
            <description>人数统计实时查询服务采集定时任务Job</description>
            <job-class>com.air.security.sodb.dts.receive.job.YsPeopleSumCountMessageJob</job-class>
        </job-detail>
        <trigger>
            <!-- JOB开关，ON:开启 OFF:关闭 -->
            <switch>on</switch>
            <name>ysPeopleSumCountMessage-Job-trigger</name>
            <group>ysPeopleSumCountMessageGroup</group>
            <cron-expression>0 0/5 * * * ?</cron-expression>
        </trigger>
    </job>

    <job>
        <job-detail>
            <name>cardMakeInfo-Job</name>
            <group>cardMakeInfoGroup</group>
            <description>门禁卡信息查询服务采集定时任务Job</description>
            <job-class>com.air.security.sodb.dts.receive.job.CardMakeInfoJob</job-class>
        </job-detail>
        <trigger>
            <!-- JOB开关，ON:开启 OFF:关闭 -->
            <switch>on</switch>
            <name>cardMakeInfo-Job-trigger</name>
            <group>cardMakeInfoGroup</group>
            <cron-expression>0 0 3 * * ?</cron-expression>
        </trigger>
    </job>

    <job>
        <job-detail>
            <name>cardPersonInfoRelease-Job</name>
            <group>cardPersonInfoGroup</group>
            <description>门禁人员信息查询服务采集定时任务Job</description>
            <job-class>com.air.security.sodb.dts.receive.job.CardPersonInfoReleaseJob</job-class>
        </job-detail>
        <trigger>
            <!-- JOB开关，ON:开启 OFF:关闭 -->
            <switch>on</switch>
            <name>cardPersonInfoRelease-Job-trigger</name>
            <group>cardPersonInfoGroup</group>
            <cron-expression>0 0 2 * * ?</cron-expression>
        </trigger>
    </job>

    <job>
        <job-detail>
            <name>doorsEquRelease-Job</name>
            <group>doorsEquReleaseGroup</group>
            <description>门基础信息查询服务采集定时任务Job</description>
            <job-class>com.air.security.sodb.dts.receive.job.DoorsEquReleaseJob</job-class>
        </job-detail>
        <trigger>
            <!-- JOB开关，ON:开启 OFF:关闭 -->
            <switch>on</switch>
            <name>doorsEquRelease-Job-trigger</name>
            <group>doorsEquReleaseGroup</group>
            <cron-expression>0 0 1 * * ?</cron-expression>
        </trigger>
    </job>

</quartz>