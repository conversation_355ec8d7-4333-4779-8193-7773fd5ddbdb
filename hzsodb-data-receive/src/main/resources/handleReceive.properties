#\u7EC4\u7EC7\u673A\u6784\u4FE1\u606F
BMFS=MessageOrganizationFormatServiceImpl,cContainerErp,ORGANIZATION_ERP,ERP
#\u4EBA\u5458\u4FE1\u606F
YGTB=MessagePersonnelFormatServiceImpl,cContainerErp,PERSONNEL_ERP,ERP

#\u95E8\u7981\u57FA\u7840\u6570\u636E
door_equ=MessageRecvFormatServiceImpl,cContainerEqu,MJ_EQU,EQU
#\u4EBA\u5458\u57FA\u7840\u6570\u636E
card_person=PersonMessageRecvFormatServiceImpl,cContainerMakeCard,CARD_STAFF_INFO,CARD_DATA
#\u5361\u57FA\u7840\u6570\u636E
card_make=MessageRecvFormatServiceImpl,cContainerMakeCard,CARD_MAKE_INFO,CARD_DATA
#\u95E8\u7981\u53D8\u66F4\u4FE1\u606F\u57FA\u7840\u6570\u636E
T_ACS_CHANGEMESSAGE=MjChangeMessageRecvFormatServiceImpl,cContainerEqu|cContainerMakeCard,MJ_EQU_UE|CARD_MAKE_UE,EQU|CARD_DATA
#\u95E8\u7981\u72B6\u6001\u53D8\u66F4\u4FE1\u606F\u6570\u636E
T_ACS_DOORLOG=MjStateMessageRecvFormatServiceImpl,cContainerEqu,MJ_EQU_UP,EQU_STATES
#\u95E8\u7981\u62A5\u8B66\u4FE1\u606F\u6570\u636E
T_ACS_ALARMMESSAGE = MjAlarmMessageRecvFormatServiceImpl,cContainerAlarm|cContainerAlarmV3,MJ_ALARM|MJ_ALARM_V3,ALARM|ALARM
#\u5237\u5361\u4FE1\u606F\u4FE1\u606F\u6570\u636E
T_ACS_SWIPEMESSAGE = MjCardMessageRecvFormatServiceImpl,cContainerRecord|cContainerRecordV3,MJ_RECORD_CARD|MJ_RECORD_CARD_V3,RECORD|RECORD
#\u5929\u6C14\u4FE1\u606F\u6570\u636E
weather_info = MessageRecvFormatServiceImpl,cContainerWeather,WEATHER_DYN,WEATHER_DATAs

#\u822A\u73ED\u5BA2\u6D41\u6570\u636E
pms_hbkl_info = MessageRecvFormatServiceImpl,cContainerFlight,HBKL_FLIGHT_DYN,FLIGHT_DATA

#\u83B7\u53D6\u5B87\u89C6\u5904\u7F6E\u62A5\u8B66\u6570\u636E
yushiAlarmDispose = MessageRecvFormatServiceImpl,cContainerAlarm|cContainerAlarmV3,ALARM_DISPOSE|MJ_DISPOSE_V3,ALARM|ALARM
YS_PEOPLE_SUM_COUNT=MessageRecvFormatServiceImpl,iSodbEquStatistic,YS_PEOPLE_SUM_COUNT,EQU_STATISTICS

#\u83B7\u53D6\u5B87\u89C6\u62A5\u8B66\u5206\u6790\u6570\u636E
TOPIC_HQ2SYS_SPFXBJ = MessageRecvFormatServiceImpl,cContainerAlarm|cContainerAlarmV3,ALARM_ANALYSIS|MJ_ANALYSIS_V3,ALARM|ALARM

#\u83B7\u53D6\u6D77\u5EB7\u62A5\u8B66
TOPIC_AQYX_YBBJ = MessageRecvFormatServiceImpl,cContainerAlarm|cContainerAlarmV3,HK_ALARM|HK_ALARM_V3,ALARM|ALARM


flight =FightMessageRecvFormatServiceImpl,cContainerFlight,FLIGHT_DYN,FLIGHT_DATA

#\u6839\u636E\u6444\u50CF\u673A\u7F16\u7801\u83B7\u53D6\u4EBA\u6570\u7EDF\u8BA1
camera.person.statistics.event.type = MessageRecvFormatServiceImpl,cContainerEquStatistic,YS_EQU_STATISTICS,EQU_STATISTICS

TOPIC_SSI = ScimsMessageRecvFormatServiceImpl,cContainerPassenger,PSGR_SECURITY,PSGR_DATA
TOPIC_TFCT_XLPDJL2HQ=ScimsCheckedOpenMessageRecvFormatServiceImpl,cContainerPassenger,PSGR_OPEN_CHECKED_INFO,PSGR_DATA
TOPIC_TFCT_XLKBJG2HQ=ScimsCheckedOpenMessageRecvFormatServiceImpl,cContainerPassenger,PSGR_OPEN_CHECKED_RESULT,PSGR_DATA
TOPIC_LKAJXLSJ2HQ_OUT=ScimsCheckedOpenMessageRecvFormatServiceImpl,cContainerPassenger,PSGR_OPEN_HANDHELD_INFO,PSGR_DATA