package com.air.security.sodb.dts.srvc.restsrvc.receive.repair;

import com.air.security.sodb.data.core.constant.MsgIdConstant;
import com.air.security.sodb.data.core.util.*;
import com.alibaba.fastjson.JSONObject;
import org.apache.poi.util.IOUtils;
import org.slf4j.Logger;
import org.slf4j.LoggerFactory;
import org.springframework.web.bind.annotation.CrossOrigin;
import org.springframework.web.bind.annotation.RequestMapping;
import org.springframework.web.bind.annotation.RequestMethod;
import org.springframework.web.bind.annotation.RestController;

import javax.servlet.http.HttpServletRequest;
import javax.servlet.http.HttpServletResponse;
import java.io.IOException;
import java.io.InputStream;
import java.net.HttpURLConnection;
import java.net.URL;
import java.util.Base64;


@CrossOrigin
@RestController
@RequestMapping("/api/release/repairRelease")
public class RepairReleaseController {

    private static final Logger log = LoggerFactory.getLogger(RepairReleaseController.class);

    @RequestMapping(value = "/execute", method = RequestMethod.POST)
    public ResultVo execute(HttpServletRequest request, HttpServletResponse response) {
        HaLog.info(log, MsgIdConstant.MS_INF_0001, "提交报修单信息上报服务");
        try {
            JSONObject jsonObj = RequestUtil.requestGetJson(request);
            String photourl = String.valueOf(jsonObj.get("photourl"));
            if (null != photourl && !"null".equals(photourl) && !photourl.equals("")) {
                String[] split = photourl.split(";");
                for (int i = 0; i < split.length; i++) {
                    String s = split[i];
                    String base64 = getBase64(s);
                    i++;
                    jsonObj.put("photobase" + i, "data:image/jpg;base64," + base64);
                }
            }
            KafkaProducerUtil.send("TOPIC_BX2HQ", jsonObj.toString());
            HaLog.info(log, MsgIdConstant.MS_INF_0002, "提交报修单信息上报服务");
            return ResultVo.success();
        } catch (Exception e) {
            HaLog.error(log, e, MsgIdConstant.MS_ERR_0001);
            return ResultVo.failure(new ResultMessage("提交报修单信息上报服务"));
        }
    }

    public static String getBase64(String url) throws IOException {
        //转成Base64
        InputStream httpsFile = getHttpsFile(url);
        byte[] bytes = IOUtils.toByteArray(httpsFile);
        String result = Base64.getEncoder().encodeToString(bytes);
        return result;
    }

    public static InputStream getHttpsFile(String fileUrl) throws IOException {
        URL url = new URL(fileUrl);
        HttpURLConnection connection = (HttpURLConnection) url.openConnection();
        connection.setRequestMethod("GET");
        connection.setConnectTimeout(10 * 1000);
        return connection.getInputStream();
    }


}