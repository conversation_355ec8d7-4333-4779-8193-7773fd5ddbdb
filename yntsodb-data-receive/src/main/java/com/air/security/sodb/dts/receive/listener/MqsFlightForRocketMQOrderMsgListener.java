package com.air.security.sodb.dts.receive.listener;

import com.air.security.sodb.data.core.base.Meta;
import com.air.security.sodb.data.core.constant.GlobalCodeConstant;
import com.air.security.sodb.data.core.constant.MsgIdConstant;
import com.air.security.sodb.data.core.mq.MyProducer;
import com.air.security.sodb.data.core.mq.impl.OrderRocketMQProducer;
import com.air.security.sodb.data.core.util.HaLog;
import com.air.security.sodb.data.core.util.PropertyUtil;
import com.air.security.sodb.data.core.util.ResourceUtil;
import com.air.security.sodb.data.core.util.ThreadUtil;
import com.air.security.sodb.dts.receive.server.MqMessageRecvServer;
import com.air.security.sodb.dts.receive.server.MqMessageRecvServerImpl;
import com.air.security.sodb.dts.receive.util.IbmMqConscumerUtil;
import com.air.security.sodb.dts.receive.util.MessageByMq;
import com.air.security.sodb.dts.receive.util.XmlToJsonUtils;
import com.alibaba.fastjson.JSONObject;
import com.ibm.mq.MQQueueManager;
import org.apache.commons.lang3.StringUtils;
import org.slf4j.Logger;
import org.slf4j.LoggerFactory;

import java.util.List;
import java.util.Properties;


/**
 * @Description: mqs消息-rocketmq的顺序消息处理监听（航班单独处理）
 * @author: mk
 * @Date: 2018年12月18日
 */
public class MqsFlightForRocketMQOrderMsgListener implements Runnable {

    private static final Logger log = LoggerFactory.getLogger(MqsFlightForRocketMQOrderMsgListener.class);

    private static final Logger log1 = LoggerFactory.getLogger("msgTranLog");

    /**
     * kafka配置文件
     */
    private static Properties properties = ResourceUtil.getInstance().getLocalProperties(
            PropertyUtil.getProperty(GlobalCodeConstant.KAFKA_CONSUMER_CONFIG_PATH_KEY), "kafkaConsumer");
    /**
     * 消费者主题
     */
    private String consumerTopic;

    private MQQueueManager consumer;

    /**
     * RocketMQ生产者
     */
    private MyProducer producer = new OrderRocketMQProducer();

    @Override
    public void run() {
        do {
            // 消费消息
            try {
                List<String> listdata = MessageByMq.getMessage(consumerTopic, properties.getProperty("qmName"), consumer);
                for (String message : listdata) {
                    if (StringUtils.isBlank(message)) {
                        continue;
                    }
                    // 接收消息日志
                    HaLog.infoJson(log1, message);
                    HaLog.infoJson(log, message);
                    message = XmlToJsonUtils.xml2json(message);
                    try {
                        JSONObject json = JSONObject.parseObject(message);
                        String styp = json.getJSONObject("MSG").getJSONObject("META").getString("STYP");
                        // 获取消息头标签
                        Meta meta = new Meta();
                        meta.setEventType(styp);
                        MqMessageRecvServer server = new MqMessageRecvServerImpl(producer);
                        server.handle(meta, message);
                    } catch (Exception e) {
                        HaLog.error(log, MsgIdConstant.MS_ERR_0001, e, "消息处理异常");
                    }
                    ThreadUtil.sleepThreadUnit();
                }

            } catch (Exception e) {
                e.printStackTrace();
            }

        } while (true);
    }

    public String getConsumerTopic() {
        return consumerTopic;
    }

    public void setConsumerTopic(String consumerTopic) {
        this.consumerTopic = consumerTopic;
        this.consumer = IbmMqConscumerUtil.getInstance().getMqQueueManager();
    }

    public MQQueueManager getConsumer() {
        return consumer;
    }

    public void setConsumer(MQQueueManager consumer) {
        this.consumer = consumer;
    }

}
