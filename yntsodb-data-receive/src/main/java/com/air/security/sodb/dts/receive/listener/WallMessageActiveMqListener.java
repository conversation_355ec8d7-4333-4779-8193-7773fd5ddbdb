package com.air.security.sodb.dts.receive.listener;


import com.air.security.sodb.data.core.base.Meta;
import com.air.security.sodb.data.core.constant.MsgIdConstant;
import com.air.security.sodb.data.core.mq.MyProducer;
import com.air.security.sodb.data.core.mq.impl.SyncRocketMQProducer;
import com.air.security.sodb.data.core.util.HaLog;
import com.air.security.sodb.data.core.util.PropertyUtil;
import com.air.security.sodb.dts.receive.server.MqMessageRecvServer;
import com.air.security.sodb.dts.receive.server.MqMessageRecvServerImpl;
import org.apache.activemq.ActiveMQConnectionFactory;
import org.slf4j.Logger;
import org.slf4j.LoggerFactory;

import javax.jms.*;

/**
 * 围界信息
 *
 * <AUTHOR>
public class WallMessageActiveMqListener implements MessageListener {

    private static final Logger log = LoggerFactory.getLogger(WallMessageActiveMqListener.class);

    private static String mqUrl = PropertyUtil.getProperty("wall.active.mq.url");
    private static String mqUsername = PropertyUtil.getProperty("wall.active.mq.username");
    private static String mqPwd = PropertyUtil.getProperty("wall.active.mq.pwd");
    private static String wallTopic = PropertyUtil.getProperty("wall.active.mq.ajTopic");

    /**
     * RocketMQ生产者
     */
    private MyProducer producer = new SyncRocketMQProducer();

    public void receiveData(WallMessageActiveMqListener lkajActiveMqListener) {
        HaLog.info(log, MsgIdConstant.MS_INF_0001, "围界数据监听");
        try {
            //初始化连接工厂
            ActiveMQConnectionFactory connectionFactory = new ActiveMQConnectionFactory(mqUsername, mqPwd, mqUrl);
            connectionFactory.setTrustAllPackages(true);

            //建立连接
            Connection conn = connectionFactory.createConnection();
            //启动连接
            conn.start();
            //创建Session，此方法第一个参数表示会话是否在事务中执行，第二个参数设定会话的应答模式
            Session session = conn.createSession(false, Session.AUTO_ACKNOWLEDGE);
            //创建目标队列
            Topic topic = session.createTopic(wallTopic);
            HaLog.info(log, MsgIdConstant.MS_INF_0001, topic.getTopicName());
            //通过session创建消息的接收者
            MessageConsumer consumer = session.createConsumer(topic);

            HaLog.info(log, MsgIdConstant.MS_INF_0001, "围界数据监听");

            //给接收者添加监听对象
            consumer.setMessageListener(lkajActiveMqListener);
        } catch (JMSException e) {
            HaLog.error(log, e, MsgIdConstant.MS_ERR_0001);
        }
    }

    @Override
    public void onMessage(Message arg0) {
        HaLog.info(log, MsgIdConstant.MS_INF_0001, "围界数据监听");
        // 消息分发处理器
        MqMessageRecvServer server = new MqMessageRecvServerImpl(producer);
        TextMessage textMessage = (TextMessage) arg0;

        try {
            if (textMessage instanceof TextMessage) {
                String text = textMessage.getText();
                HaLog.info(log, MsgIdConstant.MS_INF_0009, "围界消息监听信息", text);
                Meta meta = new Meta();
                meta.setEventType("wallAlarm");
                server.handle(meta, text);
            }
        } catch (Exception e) {
            HaLog.error(log, e, MsgIdConstant.MS_ERR_0001);
        }
    }

}