package com.air.security.sodb.dts.receive.service.flight;


import com.air.security.sodb.data.core.base.Meta;
import com.air.security.sodb.data.core.constant.MsgIdConstant;
import com.air.security.sodb.data.core.exception.SystemBaseException;
import com.air.security.sodb.data.core.util.HaLog;
import com.air.security.sodb.dts.receive.service.AbstractMessageRecvService;
import com.alibaba.fastjson.JSONArray;
import com.alibaba.fastjson.JSONObject;
import org.apache.commons.lang3.StringUtils;
import org.slf4j.Logger;
import org.slf4j.LoggerFactory;
import org.springframework.stereotype.Service;

import java.text.SimpleDateFormat;
import java.util.Date;

/**
 * @Description: 消息格式化的Service实现
 * @author: zhangc
 * @Date: 2018年12月26日
 */
@Service("FightMessageRecvFormatServiceImpl")
public class FightMessageRecvFormatServiceImpl extends AbstractMessageRecvService {

    private static final Logger log = LoggerFactory.getLogger(FightMessageRecvFormatServiceImpl.class);

    /**
     * 业务处理
     */
    @Override
    public void execute(Meta meta, String messageBody) throws SystemBaseException {
        HaLog.info(log, MsgIdConstant.MS_INF_0001, meta.getEventType());

        // 响应json消息
        JSONObject outputMsg = new JSONObject();

        JSONObject json1 = JSONObject.parseObject(messageBody);
        String type = json1.getString("type");
        if (!"AFDS".equals(type)) {
            HaLog.info(log, MsgIdConstant.MS_ERR_0012);
            return;
        }

        String flag = json1.getString("flag");
        JSONObject data = json1.getJSONObject("data");

        if (data.isEmpty()) {
            HaLog.info(log, MsgIdConstant.MS_WAR_0002);
            return;
        }

        data.put("operationType", flag);
        String masterFlightIata = data.getString("masterFlightIata");
        if (StringUtils.isNotBlank(masterFlightIata)) {
            HaLog.info(log, "此航班为共享航班" + masterFlightIata);
            return;
        }
        JSONArray array = new JSONArray();
        for (int i = 1; i < 8; i++) {
            String airport = data.getString("airport" + i);
            if (StringUtils.isNotBlank(airport)) {
                JSONObject json2 = new JSONObject();
                String arptDateTimeDep = data.getString("sta" + i);
                String arptDateTimeArr = data.getString("std" + i);
                formatDateStr(json2, arptDateTimeDep, "arptDateTimeDep", "离港时间格式错误");
                formatDateStr(json2, arptDateTimeArr, "arptDateTimeArr", "到港时间格式错误");
                json2.put("legNo", i);
                json2.put("airportIata", airport);
                array.add(json2);
            }
        }
        data.put("flightRoute", array);
        String checkinRange1 = data.getString("checkinRange1");
        String checkinRange2 = data.getString("checkinRange2");
        String sub = "";
        if (StringUtils.isNotBlank(checkinRange1)) {
            sub += checkinRange1 + ",";
        }
        if (StringUtils.isNotBlank(checkinRange2)) {
            sub += checkinRange2 + ",";
        }
        if (StringUtils.isNotBlank(sub)) {
            data.put("counter", sub.substring(0, sub.length() - 1));
        } else {
            data.put("counter", "");
        }

        String carousel1 = data.getString("carousel1");
        String carousel2 = data.getString("carousel2");
        String sub1 = "";
        if (StringUtils.isNotBlank(carousel1)) {
            sub1 += carousel1 + ",";
        }
        if (StringUtils.isNotBlank(carousel2)) {
            sub1 += carousel2 + ",";
        }
        if (StringUtils.isNotBlank(sub1)) {
            data.put("baggageReclaimId", sub1.substring(0, sub1.length() - 1));
        } else {
            data.put("baggageReclaimId", "");
        }

        String actualGate1Id = data.getString("actualGate1Id");
        String actualGate2Id = data.getString("actualGate2Id");
        String sub2 = "";
        if (StringUtils.isNotBlank(actualGate1Id)) {
            sub2 += actualGate1Id + ",";
        }
        if (StringUtils.isNotBlank(actualGate2Id)) {
            sub2 += actualGate2Id + ",";
        }

        if (StringUtils.isNotBlank(sub2)) {
            data.put("gate", sub2.substring(0, sub2.length() - 1));
        } else {
            data.put("gate", "");
        }

        // 响应消息头
        outputMsg.put("meta", meta);

        // 响应消息体
        outputMsg.put("body", data);

        // 转换为JSON格式，并发送消息到指定主题
        super.putSendMessage(outputMsg);

        HaLog.info(log, MsgIdConstant.MS_INF_0002, meta.getEventType());
    }

    /**
     * 处理时间格式问题
     *
     * @param json
     * @param str
     * @param property
     */
    private static void formatDateStr(JSONObject json, String str, String property, String str1) {
        if (StringUtils.isNotBlank(str)) {
            Long sourceLongDate = Long.parseLong(str);
            try {
                Date d = new Date(sourceLongDate);
                SimpleDateFormat sf = new SimpleDateFormat("yyyy-MM-dd HH:mm:ss");
                json.put(property, sf.format(d));
            } catch (Exception e) {
                HaLog.error(log, MsgIdConstant.MS_ERR_0001, e, str1);
            }
        }

    }

}
