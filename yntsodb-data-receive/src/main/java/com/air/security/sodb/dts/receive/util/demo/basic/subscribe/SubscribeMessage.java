package com.air.security.sodb.dts.receive.util.demo.basic.subscribe;

import com.air.security.sodb.dts.receive.util.demo.login.Login;
import com.air.security.sodb.dts.receive.util.demo.util.HttpEnum;
import com.air.security.sodb.dts.receive.util.demo.util.HttpTestUtils;
import com.alibaba.fastjson.JSONObject;
import com.google.gson.Gson;
import org.slf4j.Logger;
import org.slf4j.LoggerFactory;

import java.util.Map;

public class SubscribeMessage {
    private static final Logger logger = LoggerFactory.getLogger(SubscribeMessage.class);
    public static final String ACTION = "/videoService/eventCenter/messages/subscribe";
    public static final String ACTION1 = "/videoService/eventCenter/messages/subscribeAddress";
    public static final String ACTION2 = "/videoService/accounts/token/keepalive";

    public static String getSubscribeMessage(String ip, int port, String token) {
        try {
            if (ip == null || ip.isEmpty() || port <= 0) {
                logger.error("Invalid IP or port: ip={}, port={}", ip, port);
                return null;
            }
            String content = "?msgId=-1&msgNum=64&type=1";
            String response = HttpTestUtils.httpRequest(HttpEnum.GET, ip, port, ACTION, token, content);
            logger.info("Initial subscribe message response: {}", response);

            Map<String, Object> rsp = new Gson().fromJson(response, Map.class);
            Object msgId = rsp.get("nextMsgId");
            String content1 = "?msgId=" + (msgId == null ? 0 : msgId) + "&msgNum=64&type=1";
            String response1 = HttpTestUtils.httpRequest(HttpEnum.GET, ip, port, ACTION, token, content1);
            logger.info("Final subscribe message response: {}", response1);
            return response1;
        } catch (Exception e) {
            logger.error("Error retrieving subscribe message", e);
            return null;
        }
    }

    public static String getSubscribeAddress(String ip, int port, String token) {
        try {
            if (token == null || token.isEmpty()) {
                logger.error("Token is null or empty");
                return null;
            }
            String content = "";
            String response = HttpTestUtils.httpRequest(HttpEnum.GET, ip, port, ACTION1, token, content);
            JSONObject jsonObject = JSONObject.parseObject(response);
            String subscribeAddress = jsonObject.getString("subscribeAddress");
            return subscribeAddress;
        } catch (Exception e) {
            logger.error("Error retrieving subscribe address", e);
            return null;
        }
    }

    public static String getKeepLogin(String ip, int port, String token) {
        try {
            if (token == null || token.isEmpty()) {
                logger.error("Token is null or empty");
                return null;
            }
            String content = "{\"token\":\"" + token + "\"}";
            String response = HttpTestUtils.httpRequest(HttpEnum.PUT, ip, port, ACTION2, token, content);
            return response;
        } catch (Exception e) {
            logger.error("Error keeping login", e);
            return null;
        }
    }

    public static String getToken(String ip, int port, String userName, String password) {
        try {
            if (ip == null || ip.isEmpty() || port <= 0 || userName == null || userName.isEmpty() || password == null || password.isEmpty()) {
                logger.error("Invalid parameters for getting token: ip={}, port={}, userName={}", ip, port, userName);
                return null;
            }
            String response = Login.login(ip, port, userName, password);
            Map<String, Object> rsp = new Gson().fromJson(response, Map.class);
            String message = (String) rsp.get("message");
            if (message != null && !message.isEmpty()) {
                logger.error("Error message received during token retrieval: {}", message);
                throw new Exception("Error retrieving token: " + message);
            }
            String token = (String) rsp.get("token");
            if (token == null || token.isEmpty()) {
                logger.error("Token received is null or empty");
                throw new Exception("Received token is empty");
            }
            return token;
        } catch (Exception e) {
            logger.error("Error getting token", e);
            return null;
        }
    }
}
