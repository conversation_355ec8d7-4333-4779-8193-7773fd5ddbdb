#4.1.1.6\u822A\u73ED\u5171\u4EAB\u53D8\u66F4\u4E8B\u4EF6\uFF08SFLG\uFF09
SFLG=DfltMessageRecvFormatServiceImpl,iSodbFlight,SFLG,FLIGHT_DATA
#4.1.1.7\u822A\u73ED\u822A\u7EBF\u53D8\u66F4\u4E8B\u4EF6\uFF08AIRL\uFF09
AIRL=DfltMessageRecvFormatServiceImpl,iSodbFlight,AIRL,FLIGHT_DATA
#4.1.1.8\u822A\u73ED\u53F7\u53D8\u66F4\u4E8B\u4EF6\uFF08HBTT\uFF09
HBTT=DfltMessageRecvFormatServiceImpl,iSodbFlight,HBTT,FLIGHT_DATA
#4.1.1.9\u822A\u73ED\u524D\u7AD9\u8D77\u98DE\u4E8B\u4EF6\uFF08ONRE\uFF09
ONRE=DfltMessageRecvFormatServiceImpl,iSodbFlight,ONRE,FLIGHT_DATA
#4.1.1.10\u822A\u73ED\u5230\u8FBE\u672C\u7AD9\u4E8B\u4EF6\uFF08ARRE\uFF09
ARRE=DfltMessageRecvFormatServiceImpl,iSodbFlight,ARRE,FLIGHT_DATA
#4.1.1.11\u822A\u73ED\u672C\u7AD9\u8D77\u98DE\u4E8B\u4EF6\uFF08DEPE\uFF09
DEPE=DfltMessageRecvFormatServiceImpl,iSodbFlight,DEPE,FLIGHT_DATA
#4.1.1.13\u822A\u73ED\u5F00\u59CB\u503C\u673A\u4E8B\u4EF6\uFF08CKIE\uFF09
CKIE=DfltMessageRecvFormatServiceImpl,iSodbFlight,CKIE,FLIGHT_DATA
#4.1.1.14\u822A\u73ED\u622A\u6B62\u503C\u673A\u4E8B\u4EF6\uFF08CKOE\uFF09
CKOE=DfltMessageRecvFormatServiceImpl,iSodbFlight,CKOE,FLIGHT_DATA
#4.1.1.15\u822A\u73ED\u5F00\u59CB\u767B\u673A\u4E8B\u4EF6\uFF08BORE\uFF09
BORE=DfltMessageRecvFormatServiceImpl,iSodbFlight,BORE,FLIGHT_DATA
#4.1.1.16\u822A\u73ED\u8FC7\u7AD9\u767B\u673A\u4E8B\u4EF6\uFF08TBRE\uFF09
TBRE=DfltMessageRecvFormatServiceImpl,iSodbFlight,TBRE,FLIGHT_DATA
#4.1.1.17\u822A\u73ED\u50AC\u4FC3\u767B\u673A\u4E8B\u4EF6\uFF08LBDE\uFF09
LBDE=DfltMessageRecvFormatServiceImpl,iSodbFlight,LBDE,FLIGHT_DATA
#4.1.1.18\u822A\u73ED\u7ED3\u675F\u767B\u673A\u4E8B\u4EF6\uFF08POKE\uFF09
POKE=DfltMessageRecvFormatServiceImpl,iSodbFlight,POKE,FLIGHT_DATA
#4.1.1.19\u822A\u73ED\u5EF6\u8BEF\u4E8B\u4EF6\uFF08DLYE\uFF09
DLYE=DfltMessageRecvFormatServiceImpl,iSodbFlight,DLYE,FLIGHT_DATA
#4.1.1.20\u822A\u73ED\u53D6\u6D88\u4E8B\u4EF6\uFF08CANE\uFF09
CANE=DfltMessageRecvFormatServiceImpl,iSodbFlight,CANE,FLIGHT_DATA
#********\u822A\u73ED\u8FD4\u822A\u4E8B\u4EF6\uFF08RTNE\uFF09
RTNE=DfltMessageRecvFormatServiceImpl,iSodbFlight,RTNE,FLIGHT_DATA
#********\u822A\u73ED\u6ED1\u56DE\u4E8B\u4EF6\uFF08BAKE\uFF09
BAKE=DfltMessageRecvFormatServiceImpl,iSodbFlight,BAKE,FLIGHT_DATA
#********\u822A\u73ED\u5907\u964D\u4E8B\u4EF6\uFF08ALTE\uFF09
ALTE=DfltMessageRecvFormatServiceImpl,iSodbFlight,ALTE,FLIGHT_DATA
#********\u822A\u73ED\u66F4\u6362\u98DE\u673A\u4E8B\u4EF6\uFF08CFCE\uFF09
CFCE=DfltMessageRecvFormatServiceImpl,iSodbFlight,CFCE,FLIGHT_DATA
#********\u822A\u73EDVIP\u4E8B\u4EF6\uFF08VIP\uFF09
VIP=DfltMessageRecvFormatServiceImpl,iSodbFlight,VIP,FLIGHT_DATA
#********\u822A\u73ED\u767B\u673A\u95E8\u52A8\u6001\u4FE1\u606F\u66F4\u65B0\u4E8B\u4EF6\uFF08GTLS\uFF09
GTLS=DfltMessageRecvFormatServiceImpl,iSodbFlight,GTLS,FLIGHT_DATA
#********\u822A\u73ED\u884C\u674E\u63D0\u53D6\u8F6C\u76D8\u52A8\u6001\u4FE1\u606F\u66F4\u65B0\u4E8B\u4EF6\uFF08BLLS\uFF09
BLLS=DfltMessageRecvFormatServiceImpl,iSodbFlight,BLLS,FLIGHT_DATA
#********\u822A\u73ED\u503C\u673A\u67DC\u53F0\u52A8\u6001\u4FE1\u606F\u66F4\u65B0\u4E8B\u4EF6\uFF08CKLS\uFF09
CKLS=DfltMessageRecvFormatServiceImpl,iSodbFlight,CKLS,FLIGHT_DATA
#********\u822A\u73ED\u673A\u4F4D\u52A8\u6001\u4FE1\u606F\u66F4\u65B0\u4E8B\u4EF6\uFF08STLS\uFF09
STLS=DfltMessageRecvFormatServiceImpl,iSodbFlight,STLS,FLIGHT_DATA
#********\u822A\u73ED\u8BA1\u5212\u65F6\u95F4\u4E8B\u4EF6\uFF08FPTT\uFF09
FPTT=DfltMessageRecvFormatServiceImpl,iSodbFlight,FPTT,FLIGHT_DATA
#********\u822A\u73ED\u9884\u8BA1\u65F6\u95F4\u4E8B\u4EF6\uFF08FETT\uFF09
FETT=DfltMessageRecvFormatServiceImpl,iSodbFlight,FETT,FLIGHT_DATA
#********\u822A\u73ED\u5B9E\u9645\u65F6\u95F4\u4E8B\u4EF6\uFF08FRTT\uFF09
FRTT=DfltMessageRecvFormatServiceImpl,iSodbFlight,FRTT,FLIGHT_DATA
#********\u822A\u73ED\u8DD1\u9053\u53D8\u66F4\u4E8B\u4EF6\uFF08RWAY\uFF09
RWAY=DfltMessageRecvFormatServiceImpl,iSodbFlight,RWAY,FLIGHT_DATA
#********\u822A\u73ED\u822A\u7AD9\u697C\u53D8\u66F4\u4E8B\u4EF6\uFF08TRML\uFF09
TRML=DfltMessageRecvFormatServiceImpl,iSodbFlight,TRML,FLIGHT_DATA
#4.1.1.37\u822A\u73ED\u5C5E\u6027\u53D8\u66F4\u4E8B\u4EF6\uFF08FATT\uFF09
FATT=DfltMessageRecvFormatServiceImpl,iSodbFlight,FATT,FLIGHT_DATA
#4.1.3.5\u5F02\u5E38\u539F\u56E0\u6574\u8868\u6570\u636E\u540C\u6B65\u4E8B\u4EF6\uFF08ARDL\uFF09
ARDL=ArdlMessageRecvFormatServiceImpl,cContainerFlight,ARDL,FLIGHT_DATA
#4.1.3.52\u767B\u673A\u95E8\u6574\u8868\u6570\u636E\u540C\u6B65\u4E8B\u4EF6\uFF08GTDL\uFF09
GTDL=GtdlMessageRecvFormatServiceImpl,cContainerEqu,GTDL_EQU,EQU
#4.1.3.67\u503C\u673A\u67DC\u53F0\u4FE1\u606F\u6574\u8868\u6570\u636E\u540C\u6B65\u4E8B\u4EF6\uFF08CCDL\uFF09
CCDL=CcdlMessageRecvFormatServiceImpl,cContainerEqu,CCDL_EQU,EQU
#4.1.1.1\u822A\u73ED\u52A8\u6001\u589E\u52A0\u4E8B\u4EF6\u8BF4\u660E\uFF08DFIE\uFF09
DFIE=DfltMessageRecvFormatServiceImpl,iSodbFlight,DFIE,FLIGHT_DATA
#4.1.1.2\u52A8\u6001\u5220\u9664\u4E8B\u4EF6\u8BF4\u660E\uFF08DFDE\uFF09
DFDE=DfltMessageRecvFormatServiceImpl,iSodbFlight,DFDE,FLIGHT_DATA
#4.1.1.4\u52A8\u6001\u822A\u73ED\u6574\u8868\u540C\u6B65\u4E8B\u4EF6\uFF08DFDL\uFF09
DFDL=DfltMessageRecvFormatServiceImpl,iSodbFlight,DFDL,FLIGHT_DATA
#4.1.1.27\u822A\u73ED\u884C\u674E\u6ED1\u69FD\u53E3\u52A8\u6001\u4FE1\u606F\u66F4\u65B0\u4E8B\u4EF6\uFF08CHLS\uFF09
CHLS=DfltMessageRecvFormatServiceImpl,iSodbFlight,CHLS,FLIGHT_DATA

NXTE=DfltMessageRecvFormatServiceImpl,iSodbFlight,NXTE,FLIGHT_DATA

STIE=StndMessageRecvFormatServiceImpl,cContainerEqu,STIE_EQU_ADD,EQU
STUE=StndMessageRecvFormatServiceImpl,cContainerEqu,STUE_EQU_UPDATE,EQU
STDE=StndMessageRecvFormatServiceImpl,cContainerEqu,STDE_EQU_DELETE,EQU
STDL=StndMessageRecvFormatServiceImpl,cContainerEqu,STDL_EQU_SYNC,EQU
wallAlarm=WallMessageRecvFormatServiceImpl,cContainerAlarm,WALL_ALARM,ALARM
MJ_EQU_ALARM_INFO=MjEquAlarmMessageRecvFormatServiceImpl,cContainerAlarm,MJ_EQU_ALARM_INFO,ALARM

