kafka.listener.config.path =D:\\server\\yntSodb\\config\\kafkaListener.json
kafka.consumer.config.path =D:\\server\\yntSodb\\config\\kafkaConsumer.properties
rocketMq.producer.config.path =D:\\server\\yntSodb\\config\\rocketMQProducer.properties
rocketMq.consumer.config.path =D:\\server\\yntSodb\\config\\rocketMQConsumer.properties
#\u7EBF\u7A0B\u6C60\u5927\u5C0F
threadpool.count=20
#\u56F4\u754C\u7CFB\u7EDFactivemq\u914D\u7F6E
wall.service=false
wall.active.mq.url=failover://tcp://*************:61616
wall.active.mq.username=system
wall.active.mq.pwd=manager
wall.active.mq.ajTopic=CENTER.CLIENT.ALARM.Enclosure.EnclosureAlarm.0401,CENTER.CLIENT.ALARM.Enclosure.EnclosureAlarm.0402,CENTER.CLIENT.ALARM.Enclosure.EnclosureAlarm.0403,CENTER.CLIENT.ALARM.Enclosure.EnclosureAlarm.0404,CENTER.CLIENT.ALARM.Enclosure.EnclosureAlarm.0405
dhAlarm.service=true

baseInfo.ip=**********
baseInfo.port=8314
baseInfo.userName=system
baseInfo.password=Admin@123
baseInfo.type=302,303
